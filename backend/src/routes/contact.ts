import express, { Request, Response } from 'express';
import multer from 'multer';
import nodemailer from 'nodemailer';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/png') {
      cb(null, true);
    } else {
      cb(new Error('Only JPEG and PNG files are allowed'));
    }
  },
});

// Configure nodemailer
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

// Contact form submission
router.post('/', upload.single('screenshot'), async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, email, message, type } = req.body;
    const screenshot = req.file;

    // Validate required fields
    if (!name || !email || !message || !type) {
      res.status(400).json({ error: 'All fields are required' });
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      res.status(400).json({ error: 'Invalid email format' });
      return;
    }

    // Determine recipient email
    const recipientEmail = type === 'support' ? '<EMAIL>' : '<EMAIL>';
    const subject = type === 'support' ? `Support Request from ${name}` : `Contact Form Submission from ${name}`;

    // Prepare email content
    let htmlContent = `
      <h2>${type === 'support' ? 'Support Request' : 'Contact Form Submission'}</h2>
      <p><strong>Name:</strong> ${name}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Message:</strong></p>
      <p>${message.replace(/\n/g, '<br>')}</p>
      <hr>
      <p><em>Sent from Notely Social contact form</em></p>
    `;

    // Prepare email options
    const mailOptions: any = {
      from: process.env.SMTP_USER,
      to: recipientEmail,
      subject,
      html: htmlContent,
      replyTo: email,
    };

    // Add screenshot attachment if provided
    if (screenshot) {
      mailOptions.attachments = [
        {
          filename: `screenshot-${Date.now()}.${screenshot.mimetype === 'image/jpeg' ? 'jpg' : 'png'}`,
          content: screenshot.buffer,
          contentType: screenshot.mimetype,
        },
      ];
    }

    // Send email
    await transporter.sendMail(mailOptions);

    console.log(`${type === 'support' ? 'Support request' : 'Contact form'} submitted by ${email}`);

    res.json({ 
      success: true, 
      message: `Your ${type === 'support' ? 'support request' : 'message'} has been sent successfully!` 
    });

  } catch (error) {
    console.error('Error sending contact form:', error);
    res.status(500).json({ 
      error: 'Failed to send message. Please try again later.' 
    });
  }
});

// Health check endpoint
router.get('/health', (req: Request, res: Response) => {
  res.json({ status: 'Contact service is running' });
});

export default router;
