import express, { Request, Response, Router } from 'express';
import <PERSON><PERSON> from 'stripe';
import { protect } from '../middleware/authMiddleware';
import { IUser } from '../models/User';
import Coupon from '../models/Coupon';
import { asyncHandler } from '../utils/asyncHandler';

const router: Router = express.Router();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

// Create Stripe Checkout session (with optional coupon)
router.post('/create-checkout-session', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const { priceId, couponCode, successUrl, cancelUrl } = req.body;

  console.log('[Billing] Create checkout session request:', {
    userId: user._id,
    email: user.email,
    priceId,
    couponCode,
    hasStripeKey: !!process.env.STRIPE_SECRET_KEY
  });

  if (!priceId) {
    res.status(400).json({ message: 'Price ID is required' });
    return;
  }

  if (!process.env.STRIPE_SECRET_KEY) {
    console.error('[Billing] Stripe secret key not configured');
    res.status(500).json({ message: 'Stripe not configured' });
    return;
  }

  try {
    let couponId: string | undefined;
    let appliedCoupon: { code: string; discountType: string; amount: number } | null = null;

    // Validate and apply coupon if provided
    if (couponCode) {
      const coupon = await Coupon.findOne({ 
        code: couponCode.toUpperCase().trim(),
        isActive: true,
      });

      if (!coupon) {
        res.status(400).json({ message: 'Invalid coupon code' });
        return;
      }

      // Check if expired
      if (coupon.expiresAt && coupon.expiresAt < new Date()) {
        res.status(400).json({ message: 'Coupon has expired' });
        return;
      }

      // Check usage limit
      if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) {
        res.status(400).json({ message: 'Coupon usage limit exceeded' });
        return;
      }

      // Create or get Stripe coupon
      try {
        let stripeCoupon;
        try {
          stripeCoupon = await stripe.coupons.retrieve(coupon.code);
        } catch {
          // Create new Stripe coupon if it doesn't exist
          stripeCoupon = await stripe.coupons.create({
            id: coupon.code,
            name: `${coupon.code} - ${coupon.discountType === 'percentage' ? coupon.amount + '%' : '$' + coupon.amount} off`,
            [coupon.discountType === 'percentage' ? 'percent_off' : 'amount_off']: 
              coupon.discountType === 'percentage' ? coupon.amount : coupon.amount * 100, // Stripe expects cents for amount_off
            currency: coupon.discountType === 'fixed' ? 'usd' : undefined,
            duration: 'once',
          });
        }
        
        couponId = stripeCoupon.id;
        appliedCoupon = {
          code: coupon.code,
          discountType: coupon.discountType,
          amount: coupon.amount,
        };
      } catch (stripeError) {
        console.error('Error creating Stripe coupon:', stripeError);
        res.status(500).json({ message: 'Failed to apply coupon' });
        return;
      }
    }

    // Ensure customer exists
    let customerId = user.stripeCustomerId;
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          userId: user._id.toString(),
        },
      });
      customerId = customer.id;
      
      // Update user with Stripe customer ID
      user.stripeCustomerId = customerId;
      await user.save();
    }

    // Create checkout session
    const sessionParams: Stripe.Checkout.SessionCreateParams = {
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl || `${process.env.CLIENT_URL || `chrome-extension://${process.env.EXTENSION_ID || 'bjckijdjbekeahagdekmefimaepdlklk'}`}/dashboard.html?success=true`,
      cancel_url: cancelUrl || `${process.env.CLIENT_URL || `chrome-extension://${process.env.EXTENSION_ID || 'bjckijdjbekeahagdekmefimaepdlklk'}`}/dashboard.html?tab=settings`,
      metadata: {
        userId: user._id.toString(),
        couponCode: couponCode || '',
      },
    };

    // Add coupon if available
    if (couponId) {
      sessionParams.discounts = [{ coupon: couponId }];
    }

    const session = await stripe.checkout.sessions.create(sessionParams);

    res.json({
      sessionId: session.id,
      url: session.url,
      appliedCoupon,
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ message: 'Failed to create checkout session' });
  }
}));

// Create Stripe Customer Portal session
router.post('/create-portal-session', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  console.log('Create portal session request for user:', user.email, 'stripeCustomerId:', user.stripeCustomerId);

  // If no Stripe customer ID, create one first
  if (!user.stripeCustomerId) {
    console.log('No Stripe customer ID found for user:', user.email, 'creating new customer...');
    try {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.displayName || user.email,
        metadata: {
          userId: user._id.toString(),
        },
      });

      // Update user with new customer ID
      user.stripeCustomerId = customer.id;
      await user.save();

      console.log('Created new Stripe customer:', customer.id, 'for user:', user.email);
    } catch (error) {
      console.error('Error creating Stripe customer:', error);
      res.status(500).json({ message: 'Failed to create billing account. Please try again.' });
      return;
    }
  }

  try {
    // Get return URL from request body or use default based on environment
    // Always use web dashboard for billing portal return URL (Stripe doesn't accept chrome-extension:// URLs)
    const returnUrl = req.body.return_url || 'https://notely.social/dashboard';

    console.log('Creating billing portal session with return URL:', returnUrl);

    const session = await stripe.billingPortal.sessions.create({
      customer: user.stripeCustomerId,
      return_url: returnUrl,
    });

    console.log('Billing portal session created successfully:', session.id);
    res.json({ url: session.url });
  } catch (error) {
    console.error('Error creating billing portal session:', error);
    console.error('Error details:', {
      message: (error as any).message,
      type: (error as any).type,
      code: (error as any).code,
      stripeCustomerId: user.stripeCustomerId
    });

    // Provide more specific error messages based on Stripe error types
    let userMessage = 'Failed to create billing portal session';
    if ((error as any).type === 'StripeInvalidRequestError') {
      if ((error as any).message?.includes('portal')) {
        userMessage = 'Billing portal is not configured. Please contact support.';
      } else if ((error as any).message?.includes('customer')) {
        userMessage = 'Customer account not found. Please contact support.';
      }
    }

    res.status(500).json({
      message: userMessage,
      error: process.env.NODE_ENV === 'development' ? (error as any).message : undefined,
      stripeError: process.env.NODE_ENV === 'development' ? {
        type: (error as any).type,
        code: (error as any).code
      } : undefined
    });
  }
}));

// Cancel subscription
router.post('/cancel-subscription', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  if (!user.stripeSubscriptionId) {
    res.status(400).json({ message: 'No active subscription found' });
    return;
  }

  try {
    const subscription = await stripe.subscriptions.update(user.stripeSubscriptionId, {
      cancel_at_period_end: true,
    });

    res.json({ 
      message: 'Subscription will be canceled at the end of the current billing period',
      cancelAt: subscription.cancel_at,
    });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    res.status(500).json({ message: 'Failed to cancel subscription' });
  }
}));

// Reactivate subscription
router.post('/reactivate-subscription', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  if (!user.stripeSubscriptionId) {
    res.status(400).json({ message: 'No subscription found' });
    return;
  }

  try {
    const subscription = await stripe.subscriptions.update(user.stripeSubscriptionId, {
      cancel_at_period_end: false,
    });

    res.json({ 
      message: 'Subscription reactivated successfully',
      status: subscription.status,
    });
  } catch (error) {
    console.error('Error reactivating subscription:', error);
    res.status(500).json({ message: 'Failed to reactivate subscription' });
  }
}));

// Change subscription plan (upgrade/downgrade)
router.post('/change-plan', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const { priceId } = req.body;

  if (!priceId) {
    res.status(400).json({ message: 'Price ID is required' });
    return;
  }

  if (!user.stripeSubscriptionId) {
    res.status(400).json({ message: 'No active subscription found' });
    return;
  }

  try {
    // Get current subscription
    const subscription = await stripe.subscriptions.retrieve(user.stripeSubscriptionId);

    // Update subscription with new price
    const updatedSubscription = await stripe.subscriptions.update(user.stripeSubscriptionId, {
      items: [{
        id: subscription.items.data[0].id,
        price: priceId,
      }],
      proration_behavior: 'create_prorations', // Handle prorations for plan changes
    });

    // Update user plan in database based on price ID
    const monthlyPriceId = process.env.STRIPE_PRICE_ID_MONTHLY;
    const yearlyPriceId = process.env.STRIPE_PRICE_ID_YEARLY;

    if (priceId === monthlyPriceId || priceId === yearlyPriceId) {
      user.plan = 'premium';
    } else {
      // If it's a different price ID, assume it's a downgrade to free
      user.plan = 'free';
    }

    await user.save();

    res.json({
      message: 'Subscription plan updated successfully',
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        currentPeriodStart: updatedSubscription.current_period_start,
        currentPeriodEnd: updatedSubscription.current_period_end,
      }
    });
  } catch (error) {
    console.error('Error changing subscription plan:', error);
    res.status(500).json({ message: 'Failed to change subscription plan' });
  }
}));

// Get subscription details
router.get('/subscription', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  console.log('Get subscription request for user:', user.email, 'stripeSubscriptionId:', user.stripeSubscriptionId);

  if (!user.stripeSubscriptionId) {
    console.log('No Stripe subscription ID found for user:', user.email);
    res.json({ subscription: null });
    return;
  }

  try {
    const subscription = await stripe.subscriptions.retrieve(user.stripeSubscriptionId);

    console.log('Retrieved subscription from Stripe:', {
      id: subscription.id,
      status: subscription.status,
      currentPeriodStart: subscription.current_period_start,
      currentPeriodEnd: subscription.current_period_end,
      currentPeriodEndType: typeof subscription.current_period_end
    });

    res.json({
      subscription: {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: subscription.current_period_start,
        currentPeriodEnd: subscription.current_period_end,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        cancelAt: subscription.cancel_at,
        trialEnd: subscription.trial_end,
        priceId: subscription.items.data[0]?.price?.id,
      },
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    res.status(500).json({ message: 'Failed to fetch subscription details' });
  }
}));

// Debug endpoint to check billing status
router.get('/debug', protect, asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as IUser;

  const debugInfo = {
    userId: user._id,
    email: user.email,
    plan: user.plan,
    subscriptionStatus: user.subscriptionStatus,
    stripeCustomerId: user.stripeCustomerId,
    stripeSubscriptionId: user.stripeSubscriptionId,
    hasStripeKey: !!process.env.STRIPE_SECRET_KEY,
    environment: process.env.NODE_ENV,
    stripeConnectionTest: '' as string,
    billingPortalTest: '' as string,
  };

  // Test if we can create a test customer (to verify Stripe connection)
  try {
    const testCustomer = await stripe.customers.create({
      email: '<EMAIL>',
      name: 'Test Customer'
    });
    debugInfo.stripeConnectionTest = 'SUCCESS';

    // Test if billing portal configuration exists
    try {
      const portalSession = await stripe.billingPortal.sessions.create({
        customer: testCustomer.id,
        return_url: 'https://example.com'
      });
      debugInfo.billingPortalTest = 'SUCCESS';
    } catch (portalError) {
      debugInfo.billingPortalTest = `FAILED: ${(portalError as any).message}`;
    }

    // Clean up test customer
    await stripe.customers.del(testCustomer.id);
  } catch (error) {
    debugInfo.stripeConnectionTest = `FAILED: ${(error as any).message}`;
    debugInfo.billingPortalTest = 'SKIPPED - Stripe connection failed';
  }

  console.log('Billing debug info:', debugInfo);
  res.json(debugInfo);
}));

// Get available pricing plans
router.get('/plans', asyncHandler(async (req: Request, res: Response) => {
  const plans = [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      interval: null,
      priceId: null,
      features: [
        'Basic AI analysis',
        '1GB cloud storage',
        'Standard support'
      ]
    },
    {
      id: 'monthly',
      name: 'Premium Monthly',
      price: 9.99,
      interval: 'month',
      priceId: process.env.STRIPE_PRICE_ID_MONTHLY,
      features: [
        'Unlimited AI analysis',
        '10GB cloud storage',
        'Advanced content insights',
        'Priority support'
      ]
    },
    {
      id: 'yearly',
      name: 'Premium Yearly',
      price: 99.99,
      interval: 'year',
      priceId: process.env.STRIPE_PRICE_ID_YEARLY,
      features: [
        'Unlimited AI analysis',
        '10GB cloud storage',
        'Advanced content insights',
        'Priority support',
        '2 months free'
      ]
    }
  ];

  res.json({ plans });
}));

export default router;
