import express, { Express, Request, Response } from 'express';
import dotenv from 'dotenv';
import path from 'path'; // Import path module
import cors from 'cors'; // <--- Added import
import connectDB from './config/db'; // Import the DB connection function for MongoDB
import passport from './config/passport'; // Import configured passport
import authRoutes, { stripeWebhookHandler } from './routes/auth'; // Import main auth routes and separated webhook handler
import postRoutes from './routes/postRoutes'; // MongoDB post routes
// Commenting out PostgreSQL routes since we're not using PostgreSQL yet
// import postgresPostRoutes from './routes/postgresPostRoutes'; // PostgreSQL post routes
import instagramRoutes from './routes/instagramRoutes';
import billingRoutes from './routes/billing';
import adminRoutes from './routes/admin';
import couponRoutes from './routes/coupons';
import abuseRoutes from './routes/abuse';
import aiProviderConfigRoutes from './routes/aiProviderConfig';
import costAnalysisRoutes from './routes/costAnalysis';
import unifiedSyncRoutes from './routes/unifiedSyncRoutes';
import contactRoutes from './routes/contact';
import AbuseMonitorWorker from './worker/abuseMonitor';
import { AIProviderConfigService } from './services/aiProviderConfigService';
// import queueRoutes from './routes/queueRoutes'; // Queue management routes - DISABLED for now
import googleTokenRoute from './routes/api/googleToken'; // Import Google token route

// Use default dotenv loading (looks for .env in current working directory)
dotenv.config();

// Connect to MongoDB Database (legacy)
// We'll try to connect but continue even if it fails in production
try {
  connectDB();
} catch (error) {
  console.error('Failed to connect to MongoDB:', error);
  if (process.env.NODE_ENV !== 'production') {
    process.exit(1);
  } else {
    console.warn('Running with limited functionality due to database connection failure');
  }
}

const app: Express = express();
const port = process.env.PORT || 3000; // Use PORT from .env or default to 3000 (Corrected comment)

// --- CORS Middleware ---
// Allow all origins for development. For production, restrict to your extension's origin.
// Configure CORS with specific origins
app.use(cors({
  origin: '*', // Allow all origins for development
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
})); // Configure CORS middleware

// --- Stripe Webhook Route ---
// Mount BEFORE global express.json() to ensure raw body is available for Stripe signature verification
app.post('/auth/stripe/webhook', express.raw({ type: 'application/json' }), stripeWebhookHandler);

// --- Global Middleware ---
// Increase payload limits to handle base64 images (especially Instagram carousels)
app.use(express.json({ limit: '50mb' })); // For parsing application/json with larger payloads
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // For parsing application/x-www-form-urlencoded

// Passport middleware
app.use(passport.initialize());
// Note: We don't need passport.session() if using JWTs for API authentication

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    message: 'Notely.social API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API info endpoint (moved from root to avoid conflict)
app.get('/api', (req: Request, res: Response) => {
  res.json({
    name: 'Notely Social Backend API',
    version: '1.0.0',
    status: 'running',
    endpoints: {
      auth: '/auth',
      posts: '/api/posts',
      unifiedSync: '/api/unified-sync',
      billing: '/billing',
      admin: '/admin'
    }
  });
});

// Mount the rest of the auth routes (excluding the webhook, which is handled above)
app.use('/auth', authRoutes); // All auth routes will be prefixed with /auth

// Mount Google token route
app.use('/', googleTokenRoute); // This will handle /auth/google/token

// Mount API routes
app.use('/api/posts', postRoutes); // MongoDB post routes (legacy)
// Commenting out PostgreSQL routes since we're not using PostgreSQL yet
// app.use('/api/v2/posts', postgresPostRoutes); // PostgreSQL post routes (new)
app.use('/api/instagram', instagramRoutes); // Instagram-specific routes with S3 integration
app.use('/api/unified-sync', unifiedSyncRoutes); // Unified data sync routes (categories, bookmarks, chat)
// app.use('/api/queue', queueRoutes); // Queue management and status routes - DISABLED for now

// Mount billing and admin routes
app.use('/billing', billingRoutes); // Billing management routes
app.use('/admin', adminRoutes); // Admin management routes
app.use('/', couponRoutes); // Coupon routes (includes both admin and public endpoints)
app.use('/', abuseRoutes); // Abuse detection and flagged users routes
app.use('/', aiProviderConfigRoutes); // AI provider configuration routes
app.use('/', costAnalysisRoutes); // Cost analysis and suspicious users routes
app.use('/api/contact', contactRoutes); // Contact form routes

// --- Serve Web Dashboard Static Files ---
// Serve static files from the public directory
app.use(express.static(path.join(__dirname, '../public')));

// Handle React Router - serve index.html for web routes
// Catch-all handler for React Router (must be last)
app.get('/', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/dashboard', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/dashboard/login', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/dashboard/register', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/dashboard/forgot-password', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/dashboard/reset-password', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/dashboard/settings', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/admin', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Handle additional SPA routes
app.get('/terms', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/privacy', (req: Request, res: Response): void => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Legacy routes for backwards compatibility
app.get('/login', (req: Request, res: Response): void => {
  res.redirect('/dashboard/login');
});

app.get('/register', (req: Request, res: Response): void => {
  res.redirect('/dashboard/register');
});

app.get('/settings', (req: Request, res: Response): void => {
  res.redirect('/dashboard/settings');
});



app.listen(port, () => {
  console.log(`[server]: Server is running at http://localhost:${port}`);
  
  // Initialize AI provider configurations
  AIProviderConfigService.initializeDefaults().then(() => {
    console.log('✅ AI Provider configurations initialized');
  }).catch(error => {
    console.error('❌ Failed to initialize AI provider configurations:', error);
  });
  
  // Start abuse monitoring worker
  if (process.env.NODE_ENV === 'production' || process.env.ENABLE_ABUSE_MONITOR === 'true') {
    AbuseMonitorWorker.start();
  } else {
    console.log('🔍 Abuse monitor worker disabled in development (set ENABLE_ABUSE_MONITOR=true to enable)');
  }
});