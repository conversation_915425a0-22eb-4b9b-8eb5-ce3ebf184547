import User, { IUser } from '../models/User';
import AutoTaggingLog, { IAutoTaggingLog } from '../models/AutoTaggingLog';
import { logAIUsage } from '../utils/subscriptionUtils';

export interface AutoTaggingQuotaCheck {
  canAutoTag: boolean;
  reason?: string;
  quotaUsed: number;
  quotaLimit: number; // -1 for unlimited
  timeUntilReset?: Date; // When the 24h window resets
  remainingQuota: number; // -1 for unlimited
}

export interface AutoTaggingResult {
  success: boolean;
  tags: string[];
  tokensUsed: number;
  quotaUsed: number;
  quotaLimit: number;
  error?: string;
}

export class AutoTaggingQuotaService {
  /**
   * Check if user can perform auto-tagging based on their tier and current usage
   */
  static async checkAutoTaggingQuota(userId: string): Promise<AutoTaggingQuotaCheck> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          canAutoTag: false,
          reason: 'User not found',
          quotaUsed: 0,
          quotaLimit: 0,
          remainingQuota: 0,
        };
      }

      // Premium users have unlimited auto-tagging
      if (user.plan === 'premium' || user.autoTaggingQuota === -1) {
        return {
          canAutoTag: true,
          quotaUsed: 0,
          quotaLimit: -1,
          remainingQuota: -1,
        };
      }

      // For free users, check 24h rolling window
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      // Count auto-tagging usage in the last 24 hours
      const recentUsage = await AutoTaggingLog.countDocuments({
        userId: user._id,
        timestamp: { $gte: twentyFourHoursAgo },
      });

      const quotaLimit = user.autoTaggingQuota;
      const remainingQuota = Math.max(0, quotaLimit - recentUsage);

      // Calculate when the quota will reset (24h from oldest usage in window)
      let timeUntilReset: Date | undefined;
      if (recentUsage > 0) {
        const oldestUsage = await AutoTaggingLog.findOne({
          userId: user._id,
          timestamp: { $gte: twentyFourHoursAgo },
        }).sort({ timestamp: 1 });

        if (oldestUsage) {
          timeUntilReset = new Date(oldestUsage.timestamp.getTime() + 24 * 60 * 60 * 1000);
        }
      }

      const canAutoTag = recentUsage < quotaLimit;

      // Log quota check for monitoring
      console.log(`[AutoTagging] Quota check for user ${user.email}: ${recentUsage}/${quotaLimit} used, canAutoTag: ${canAutoTag}`);

      // Log potential quota violations
      if (!canAutoTag) {
        console.warn(`[SECURITY] Auto-tagging quota exceeded - User: ${user.email}, Plan: ${user.plan}, Usage: ${recentUsage}/${quotaLimit}`);
      }

      return {
        canAutoTag,
        reason: canAutoTag ? undefined : `Auto-tagging quota exceeded. You have used ${recentUsage}/${quotaLimit} auto-tags in the last 24 hours. Upgrade to Premium for unlimited auto-tagging.`,
        quotaUsed: recentUsage,
        quotaLimit,
        timeUntilReset,
        remainingQuota,
      };
    } catch (error) {
      console.error('Error checking auto-tagging quota:', error);
      return {
        canAutoTag: false,
        reason: 'Error checking quota',
        quotaUsed: 0,
        quotaLimit: 0,
        remainingQuota: 0,
      };
    }
  }

  /**
   * Record auto-tagging usage and update user quota
   */
  static async recordAutoTaggingUsage(
    userId: string,
    postId: string | undefined,
    platform: string,
    tags: string[],
    tokensUsed: number
  ): Promise<void> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get current quota usage
      const quotaCheck = await this.checkAutoTaggingQuota(userId);

      // Create auto-tagging log entry
      await AutoTaggingLog.create({
        userId: user._id,
        postId,
        platform: platform as any,
        tagsGenerated: tags,
        tokensUsed,
        timestamp: new Date(),
        userPlan: user.plan,
        quotaUsedBefore: quotaCheck.quotaUsed,
        quotaUsedAfter: quotaCheck.quotaUsed + 1,
      });

      // Update user's last auto-tagged timestamp
      user.lastAutoTaggedAt = new Date();
      await user.save();

      // Log AI usage for cost tracking
      await logAIUsage(userId, tokensUsed, 'auto_tagging', platform);

      // Comprehensive logging for monitoring and security
      console.log(`[AutoTagging] SUCCESS - User: ${user.email}, Platform: ${platform}, Tags: ${tags.length}, Tokens: ${tokensUsed}, Quota: ${quotaCheck.quotaUsed + 1}/${quotaCheck.quotaLimit === -1 ? 'unlimited' : quotaCheck.quotaLimit}`);

      // Log for analytics and abuse detection
      if (quotaCheck.quotaLimit !== -1 && quotaCheck.quotaUsed + 1 >= quotaCheck.quotaLimit * 0.8) {
        console.warn(`[MONITORING] User ${user.email} approaching auto-tagging limit: ${quotaCheck.quotaUsed + 1}/${quotaCheck.quotaLimit}`);
      }
    } catch (error) {
      console.error('Error recording auto-tagging usage:', error);
      throw error;
    }
  }

  /**
   * Get auto-tagging usage statistics for a user
   */
  static async getAutoTaggingStats(userId: string, days: number = 30): Promise<{
    totalAutoTags: number;
    autoTagsByPlatform: Record<string, number>;
    autoTagsByDay: Record<string, number>;
    averageTokensPerTag: number;
    currentQuotaStatus: AutoTaggingQuotaCheck;
  }> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      
      const logs = await AutoTaggingLog.find({
        userId,
        timestamp: { $gte: startDate },
      }).sort({ timestamp: -1 });

      const autoTagsByPlatform: Record<string, number> = {};
      const autoTagsByDay: Record<string, number> = {};
      let totalTokens = 0;

      logs.forEach(log => {
        // Count by platform
        autoTagsByPlatform[log.platform] = (autoTagsByPlatform[log.platform] || 0) + 1;
        
        // Count by day
        const day = log.timestamp.toISOString().split('T')[0];
        autoTagsByDay[day] = (autoTagsByDay[day] || 0) + 1;
        
        // Sum tokens
        totalTokens += log.tokensUsed;
      });

      const currentQuotaStatus = await this.checkAutoTaggingQuota(userId);

      return {
        totalAutoTags: logs.length,
        autoTagsByPlatform,
        autoTagsByDay,
        averageTokensPerTag: logs.length > 0 ? Math.round(totalTokens / logs.length) : 0,
        currentQuotaStatus,
      };
    } catch (error) {
      console.error('Error getting auto-tagging stats:', error);
      throw error;
    }
  }

  /**
   * Reset auto-tagging quota for a user (admin function)
   */
  static async resetAutoTaggingQuota(userId: string): Promise<void> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      user.autoTaggingUsed = 0;
      user.lastAutoTaggedAt = undefined;
      await user.save();

      console.log(`[AutoTagging] Reset quota for user ${user.email}`);
    } catch (error) {
      console.error('Error resetting auto-tagging quota:', error);
      throw error;
    }
  }
}
