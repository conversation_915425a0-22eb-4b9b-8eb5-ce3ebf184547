import mongoose, { Schema, Document } from 'mongoose';

export interface IAutoTaggingLog extends Document {
  userId: mongoose.Types.ObjectId;
  postId?: string; // Optional reference to the post that was auto-tagged
  platform: 'twitter' | 'linkedin' | 'reddit' | 'instagram' | 'pinterest';
  tagsGenerated: string[]; // The tags that were generated
  tokensUsed: number; // AI tokens consumed for this auto-tagging
  timestamp: Date;
  userPlan: 'free' | 'premium'; // Plan at time of auto-tagging
  quotaUsedBefore: number; // Quota usage before this auto-tag
  quotaUsedAfter: number; // Quota usage after this auto-tag
  createdAt: Date;
}

const AutoTaggingLogSchema: Schema<IAutoTaggingLog> = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    postId: {
      type: String,
      required: false,
    },
    platform: {
      type: String,
      required: true,
      enum: ['twitter', 'linkedin', 'reddit', 'instagram', 'pinterest'],
    },
    tagsGenerated: {
      type: [String],
      required: true,
      default: [],
    },
    tokensUsed: {
      type: Number,
      required: true,
      min: 0,
    },
    timestamp: {
      type: Date,
      required: true,
      default: Date.now,
    },
    userPlan: {
      type: String,
      required: true,
      enum: ['free', 'premium'],
    },
    quotaUsedBefore: {
      type: Number,
      required: true,
      min: 0,
    },
    quotaUsedAfter: {
      type: Number,
      required: true,
      min: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Compound index for efficient queries by user and date range
AutoTaggingLogSchema.index({ userId: 1, timestamp: -1 });

// Index for cleanup of old logs (keep last 90 days for analytics)
AutoTaggingLogSchema.index({ createdAt: 1 });

// Index for platform-based analytics
AutoTaggingLogSchema.index({ platform: 1, timestamp: -1 });

const AutoTaggingLog = mongoose.model<IAutoTaggingLog>('AutoTaggingLog', AutoTaggingLogSchema);

export default AutoTaggingLog;
