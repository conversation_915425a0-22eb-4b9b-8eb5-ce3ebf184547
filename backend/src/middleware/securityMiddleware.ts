import { Request, Response, NextFunction } from 'express';
import { IUser } from '../models/User';

// Track request rates per user for abuse detection
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const MAX_REQUESTS_PER_MINUTE = 30; // Reasonable limit for API calls

/**
 * Security middleware to detect and log suspicious activity
 */
export const securityMonitor = (req: Request, res: Response, next: NextFunction): void => {
  const user = req.user as IUser;
  const userAgent = req.get('User-Agent') || 'Unknown';
  const ip = req.ip || req.connection.remoteAddress || 'Unknown';
  const now = Date.now(); // Move to top of function for broader scope

  // Log all authenticated API requests for security monitoring
  if (user && req.path.includes('/api/')) {
    console.log(`[SECURITY] API Request - User: ${user.email}, IP: ${ip}, Path: ${req.path}, Method: ${req.method}, UserAgent: ${userAgent.substring(0, 100)}`);
  }

  // Rate limiting per user
  if (user) {
    const userId = user._id.toString();
    const userRequests = requestCounts.get(userId);

    if (!userRequests || now > userRequests.resetTime) {
      // Reset or initialize counter
      requestCounts.set(userId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    } else {
      // Increment counter
      userRequests.count++;
      
      if (userRequests.count > MAX_REQUESTS_PER_MINUTE) {
        console.warn(`[SECURITY] Rate limit exceeded - User: ${user.email}, IP: ${ip}, Requests: ${userRequests.count}/${MAX_REQUESTS_PER_MINUTE} per minute`);
        
        res.status(429).json({
          success: false,
          error: 'Rate limit exceeded. Please slow down your requests.',
          retryAfter: Math.ceil((userRequests.resetTime - now) / 1000),
        });
        return;
      }
    }
  }

  // Detect suspicious patterns
  const suspiciousPatterns = [
    // Automated tools or scripts
    /bot|crawler|spider|scraper/i,
    // Common attack tools
    /sqlmap|nikto|nmap|burp|postman/i,
    // Suspicious automation
    /python-requests|curl|wget/i,
  ];

  const isSuspiciousUserAgent = suspiciousPatterns.some(pattern => pattern.test(userAgent));
  
  if (isSuspiciousUserAgent && user) {
    console.warn(`[SECURITY] Suspicious User-Agent detected - User: ${user.email}, IP: ${ip}, UserAgent: ${userAgent}`);
  }

  // Detect rapid-fire requests (potential automation)
  if (user) {
    const userId = user._id.toString();
    const requestKey = `${userId}_last_request`;
    const lastRequestTime = requestCounts.get(requestKey)?.resetTime || 0;
    const timeSinceLastRequest = now - lastRequestTime;
    
    if (timeSinceLastRequest < 100) { // Less than 100ms between requests
      console.warn(`[SECURITY] Rapid requests detected - User: ${user.email}, IP: ${ip}, Time between requests: ${timeSinceLastRequest}ms`);
    }
    
    requestCounts.set(requestKey, { count: 1, resetTime: now });
  }

  next();
};

/**
 * Middleware specifically for AI-related endpoints
 */
export const aiSecurityMonitor = (req: Request, res: Response, next: NextFunction): void => {
  const user = req.user as IUser;
  const ip = req.ip || req.connection.remoteAddress || 'Unknown';
  
  if (user) {
    console.log(`[AI_SECURITY] AI endpoint access - User: ${user.email}, Plan: ${user.plan}, IP: ${ip}, Endpoint: ${req.path}`);
    
    // Log content length for abuse detection
    const contentLength = req.get('content-length');
    if (contentLength && parseInt(contentLength) > 10000) { // > 10KB
      console.warn(`[AI_SECURITY] Large content detected - User: ${user.email}, Size: ${contentLength} bytes`);
    }
    
    // Check for plan mismatches (potential tampering)
    if (req.body && req.body.userPlan && req.body.userPlan !== user.plan) {
      console.error(`[SECURITY] Plan mismatch detected - User: ${user.email}, Claimed: ${req.body.userPlan}, Actual: ${user.plan}, IP: ${ip}`);
      
      res.status(403).json({
        success: false,
        error: 'Security violation detected. Request blocked.',
      });
      return;
    }
  }

  next();
};

/**
 * Clean up old request tracking data periodically
 */
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of requestCounts.entries()) {
    if (now > data.resetTime) {
      requestCounts.delete(key);
    }
  }
}, 5 * 60 * 1000); // Clean up every 5 minutes
