{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"build": "tsc && npm run copy-js-files && npm run copy-web-files", "copy-js-files": "node -e \"const fs=require('fs-extra'); const path=require('path'); try { const srcRoutes='src/routes'; const distRoutes='dist/routes'; if(fs.existsSync(srcRoutes)) { fs.ensureDirSync(distRoutes); const files=fs.readdirSync(srcRoutes).filter(f=>f.endsWith('.js')); files.forEach(file=>{ fs.copyFileSync(path.join(srcRoutes,file), path.join(distRoutes,file)); console.log('Copied:',file); }); } const srcControllers='src/controllers'; const distControllers='dist/controllers'; if(fs.existsSync(srcControllers)) { fs.ensureDirSync(distControllers); const files=fs.readdirSync(srcControllers).filter(f=>f.endsWith('.js')); files.forEach(file=>{ fs.copyFileSync(path.join(srcControllers,file), path.join(distControllers,file)); console.log('Copied:',file); }); } console.log('JS file copy completed'); } catch(e) { console.error('Error copying JS files:', e.message); }\"", "copy-web-files": "node -e \"const fs=require('fs-extra'); if(fs.existsSync('../dist-web/web')) { fs.copySync('../dist-web/web', 'public'); console.log('Copied web files from dist-web/web to public'); } else if(fs.existsSync('../dist-web')) { fs.copySync('../dist-web', 'public'); console.log('Copied web files from dist-web to public'); } else { console.log('No web files to copy'); }\"", "start": "if [ \"$RAILWAY_SERVICE_NAME\" = \"worker\" ]; then node dist/worker.js; else node dist/server.js; fi", "worker": "node dist/worker.js", "dev": "nodemon --exec \"node_modules/ts-node/dist/bin.js\" src/server.ts", "dev:worker": "nodemon --exec \"node_modules/ts-node/dist/bin.js\" src/worker.ts", "webhook:test": "node scripts/test-webhook.js", "seed": "node scripts/seed-test-data.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bull": "^4.12.9", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "fs-extra": "^11.2.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.1", "node-cron": "^4.1.1", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "stripe": "^14.21.0", "typescript": "^5.8.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/multer": "^1.4.11", "@types/node": "^22.15.3", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pg": "^8.15.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2"}}