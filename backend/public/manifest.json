{"manifest_version": 3, "name": "Notely.social", "version": "1.0.0", "description": "Save posts from social media platforms", "permissions": ["storage", "activeTab", "scripting", "tabs", "contextMenus", "identity", "webRequest"], "oauth2": {"client_id": "952116637409-66fthi1eab2ho5b2dknk2t31ne5cjbva.apps.googleusercontent.com", "scopes": ["openid", "email", "profile"]}, "host_permissions": ["*://*.twitter.com/*", "*://*.x.com/*", "*://*.linkedin.com/*", "*://*.instagram.com/*", "*://*.reddit.com/*", "*://*.pinterest.com/*", "*://*.fbcdn.net/*", "*://*.cdninstagram.com/*", "https://api.notely.social/*", "http://*/*", "https://*/*"], "externally_connectable": {"matches": ["https://api.notely.social/*"]}, "background": {"service_worker": "background.js"}, "action": {"default_icon": {"16": "extension-icons/icon16.png", "48": "extension-icons/icon48.png", "128": "extension-icons/icon128.png"}}, "icons": {"16": "extension-icons/icon16.png", "48": "extension-icons/icon48.png", "128": "extension-icons/icon128.png"}, "content_scripts": [{"matches": ["*://*.twitter.com/*", "*://*.x.com/*"], "js": ["twitter-content.js"]}, {"matches": ["*://*.linkedin.com/*"], "js": ["linkedin-content.js"]}, {"matches": ["*://*.instagram.com/*"], "js": ["instagram-content.js"]}, {"matches": ["*://*.reddit.com/*"], "js": ["reddit-content.js"]}, {"matches": ["*://*.pinterest.com/*"], "js": ["pinterest-content.js"]}], "web_accessible_resources": [{"resources": ["icons/*", "assets/*"], "matches": ["<all_urls>"]}]}