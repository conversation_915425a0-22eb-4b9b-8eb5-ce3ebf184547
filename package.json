{"name": "notely", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:web": "vite --config vite.config.web.ts", "clean": "node -e \"require('fs-extra').removeSync('dist'); require('fs-extra').removeSync('dist-web');\"", "build": "node scripts/build.js", "build:web": "node scripts/build.js web", "preview:web": "vite preview --config vite.config.web.ts", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "setup:stripe": "node scripts/setup-stripe.js", "debug:build": "node scripts/build.js && echo Build completed successfully"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@types/react-router-dom": "^5.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dom-to-image-more": "^3.5.0", "embla-carousel-react": "^8.6.0", "firebase-admin": "^13.3.0", "html-to-image": "^1.11.13", "i18next": "^25.2.1", "idb": "^8.0.0", "lucide-react": "^0.516.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "serve": "^14.2.4", "stripe": "^18.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^8.56.0", "@tailwindcss/postcss": "^4.1.4", "@types/chrome": "^0.0.315", "@types/fs-extra": "^11.0.4", "@types/html2canvas": "^0.5.35", "@types/i18next": "^12.1.0", "@types/node": "^24.0.3", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-i18next": "^7.8.3", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.14", "dotenv-cli": "^8.0.0", "esbuild": "^0.21.5", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "execa": "^9.6.0", "fs-extra": "^11.3.0", "glob": "^11.0.3", "globals": "^13.24.0", "html2canvas": "^1.4.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "typescript": "^5.3.3", "typescript-eslint": "^8.32.1", "vite": "^5.0.12", "vite-plugin-static-copy": "^1.0.5"}}