/**
 * Test file to verify DailyWisdom component behavior
 * Tests that AI-generated quotes are not clickable while post-extracted quotes are clickable
 */

import { WisdomQuote } from '../types/wisdom';

// Mock wisdom quotes for testing
const aiGeneratedQuote: WisdomQuote = {
  id: 'test-ai-1',
  text: 'What we resist in others often reflects what we haven\'t yet accepted in ourselves.',
  author: 'Generated',
  source: 'AI Generated',
  categories: ['Self-awareness'],
  tags: ['Growth', 'Reflection'],
  createdAt: new Date().toISOString(),
  extractedFrom: 'ai-generated'
};

const postExtractedQuote: WisdomQuote = {
  id: 'test-post-1',
  text: 'Success comes from focusing on one initiative at a time rather than spreading efforts across multiple projects.',
  author: 'Business Expert',
  source: 'LinkedIn Post',
  source_link: 'https://linkedin.com/posts/example-123',
  categories: ['Business'],
  tags: ['Focus', 'Strategy', 'Success'],
  createdAt: new Date().toISOString(),
  extractedFrom: 'post',
  relatedPostIds: ['post-123', 'linkedin-456']
};

/**
 * Test function to verify quote clickability logic
 */
function testQuoteClickability() {
  console.log('Testing Daily Wisdom clickability logic...');
  
  // Test AI-generated quote (should not be clickable)
  const isAiClickable = aiGeneratedQuote.extractedFrom === 'post';
  console.log(`AI-generated quote clickable: ${isAiClickable} (expected: false)`);
  
  // Test post-extracted quote (should be clickable)
  const isPostClickable = postExtractedQuote.extractedFrom === 'post';
  console.log(`Post-extracted quote clickable: ${isPostClickable} (expected: true)`);
  
  // Verify results
  const aiTestPassed = !isAiClickable;
  const postTestPassed = isPostClickable;
  
  console.log(`\nTest Results:`);
  console.log(`✓ AI-generated quote test: ${aiTestPassed ? 'PASSED' : 'FAILED'}`);
  console.log(`✓ Post-extracted quote test: ${postTestPassed ? 'PASSED' : 'FAILED'}`);
  console.log(`✓ Overall test: ${aiTestPassed && postTestPassed ? 'PASSED' : 'FAILED'}`);
  
  return aiTestPassed && postTestPassed;
}

/**
 * Test function to verify quote properties
 */
function testQuoteProperties() {
  console.log('\nTesting quote properties...');
  
  // AI-generated quote should not have source_link or relatedPostIds
  const aiHasSourceLink = !!aiGeneratedQuote.source_link;
  const aiHasRelatedIds = !!(aiGeneratedQuote.relatedPostIds && aiGeneratedQuote.relatedPostIds.length > 0);
  
  console.log(`AI quote has source_link: ${aiHasSourceLink} (expected: false)`);
  console.log(`AI quote has relatedPostIds: ${aiHasRelatedIds} (expected: false)`);
  
  // Post-extracted quote should have source_link and relatedPostIds
  const postHasSourceLink = !!postExtractedQuote.source_link;
  const postHasRelatedIds = !!(postExtractedQuote.relatedPostIds && postExtractedQuote.relatedPostIds.length > 0);
  
  console.log(`Post quote has source_link: ${postHasSourceLink} (expected: true)`);
  console.log(`Post quote has relatedPostIds: ${postHasRelatedIds} (expected: true)`);
  
  const aiPropsTest = !aiHasSourceLink && !aiHasRelatedIds;
  const postPropsTest = postHasSourceLink && postHasRelatedIds;
  
  console.log(`\nProperty Test Results:`);
  console.log(`✓ AI quote properties test: ${aiPropsTest ? 'PASSED' : 'FAILED'}`);
  console.log(`✓ Post quote properties test: ${postPropsTest ? 'PASSED' : 'FAILED'}`);
  
  return aiPropsTest && postPropsTest;
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  const clickabilityTest = testQuoteClickability();
  const propertiesTest = testQuoteProperties();
  
  console.log(`\n=== FINAL RESULTS ===`);
  console.log(`All tests passed: ${clickabilityTest && propertiesTest ? 'YES' : 'NO'}`);
  
  if (!clickabilityTest || !propertiesTest) {
    process.exit(1);
  }
}

export { testQuoteClickability, testQuoteProperties, aiGeneratedQuote, postExtractedQuote };
