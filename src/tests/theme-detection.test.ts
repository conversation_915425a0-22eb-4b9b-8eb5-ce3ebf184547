import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { detectCurrentTheme, detectCurrentThemeAsync, isDarkMode, isLightMode } from '../utils/themeUtils';

// Mock Chrome API
const mockChrome = {
  storage: {
    local: {
      get: vi.fn(),
    },
  },
  runtime: {
    lastError: null,
  },
};

// Mock window.matchMedia
const mockMatchMedia = vi.fn();

describe('Theme Detection Utility', () => {
  beforeEach(() => {
    // Reset DOM
    document.documentElement.className = '';
    document.body.className = '';
    document.documentElement.removeAttribute('data-theme');
    document.documentElement.style.removeProperty('--theme-mode');
    
    // Reset mocks
    vi.clearAllMocks();
    mockChrome.runtime.lastError = null;
    
    // Setup window.matchMedia mock
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: mockMatchMedia,
    });
    
    // Setup chrome mock
    (global as any).chrome = mockChrome;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('detectCurrentTheme', () => {
    it('should detect dark theme from document element class', () => {
      document.documentElement.classList.add('dark');
      expect(detectCurrentTheme()).toBe('dark');
    });

    it('should detect light theme from document element class', () => {
      document.documentElement.classList.add('light-theme');
      expect(detectCurrentTheme()).toBe('light');
    });

    it('should detect dark theme from body class', () => {
      document.body.classList.add('dark-theme');
      expect(detectCurrentTheme()).toBe('dark');
    });

    it('should detect light theme from body class', () => {
      document.body.classList.add('light-theme');
      expect(detectCurrentTheme()).toBe('light');
    });

    it('should detect theme from CSS custom property', () => {
      document.documentElement.style.setProperty('--theme-mode', 'dark');
      expect(detectCurrentTheme()).toBe('dark');
      
      document.documentElement.style.setProperty('--theme-mode', 'light');
      expect(detectCurrentTheme()).toBe('light');
    });

    it('should detect theme from data attribute', () => {
      document.documentElement.setAttribute('data-theme', 'dark');
      expect(detectCurrentTheme()).toBe('dark');
      
      document.documentElement.setAttribute('data-theme', 'light');
      expect(detectCurrentTheme()).toBe('light');
    });

    it('should use system preference as fallback', () => {
      mockMatchMedia.mockReturnValue({ matches: true });
      expect(detectCurrentTheme()).toBe('dark');
      
      mockMatchMedia.mockReturnValue({ matches: false });
      expect(detectCurrentTheme()).toBe('light');
    });

    it('should default to dark mode when all detection methods fail', () => {
      mockMatchMedia.mockReturnValue(undefined);
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: undefined,
      });
      
      expect(detectCurrentTheme()).toBe('dark');
    });

    it('should prioritize document element class over other methods', () => {
      document.documentElement.classList.add('dark');
      document.body.classList.add('light-theme');
      document.documentElement.style.setProperty('--theme-mode', 'light');
      mockMatchMedia.mockReturnValue({ matches: false });
      
      expect(detectCurrentTheme()).toBe('dark');
    });
  });

  describe('detectCurrentThemeAsync', () => {
    it('should resolve with stored theme from Chrome storage', async () => {
      mockChrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({ theme: 'light' });
      });
      
      const theme = await detectCurrentThemeAsync();
      expect(theme).toBe('light');
    });

    it('should fallback to sync detection when Chrome storage fails', async () => {
      mockChrome.runtime.lastError = new Error('Storage error');
      document.documentElement.classList.add('dark');
      
      const theme = await detectCurrentThemeAsync();
      expect(theme).toBe('dark');
    });

    it('should fallback to sync detection when not in Chrome extension context', async () => {
      (global as any).chrome = undefined;
      document.documentElement.classList.add('light-theme');
      
      const theme = await detectCurrentThemeAsync();
      expect(theme).toBe('light');
    });

    it('should fallback to sync detection when stored theme is invalid', async () => {
      mockChrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({ theme: 'invalid-theme' });
      });
      document.documentElement.classList.add('dark');
      
      const theme = await detectCurrentThemeAsync();
      expect(theme).toBe('dark');
    });
  });

  describe('isDarkMode', () => {
    it('should return true when theme is dark', () => {
      document.documentElement.classList.add('dark');
      expect(isDarkMode()).toBe(true);
    });

    it('should return false when theme is light', () => {
      document.documentElement.classList.add('light-theme');
      expect(isDarkMode()).toBe(false);
    });
  });

  describe('isLightMode', () => {
    it('should return true when theme is light', () => {
      document.documentElement.classList.add('light-theme');
      expect(isLightMode()).toBe(true);
    });

    it('should return false when theme is dark', () => {
      document.documentElement.classList.add('dark');
      expect(isLightMode()).toBe(false);
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle missing document gracefully', () => {
      const originalDocument = global.document;
      (global as any).document = undefined;
      
      // Should not throw error
      expect(() => {
        // This would normally fail, but we're testing error handling
        try {
          detectCurrentTheme();
        } catch (error) {
          // Expected to fail in this test environment
        }
      }).not.toThrow();
      
      global.document = originalDocument;
    });

    it('should handle CSS property access errors', () => {
      const originalGetComputedStyle = global.getComputedStyle;
      (global as any).getComputedStyle = vi.fn(() => {
        throw new Error('CSS access error');
      });
      
      // Should fallback to other methods
      document.documentElement.classList.add('dark');
      expect(detectCurrentTheme()).toBe('dark');
      
      global.getComputedStyle = originalGetComputedStyle;
    });

    it('should handle matchMedia not being available', () => {
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: undefined,
      });
      
      // Should default to dark mode
      expect(detectCurrentTheme()).toBe('dark');
    });
  });
});