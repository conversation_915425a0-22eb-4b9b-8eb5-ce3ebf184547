import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useWebAuth } from '../context/WebAuthContext';
import { PremiumBadge } from '../../components/PremiumBadge';
import PremiumBadge from '../../components/PremiumBadge';

interface Post {
  id: string;
  title: string;
  content: string;
  platform: string;
  category?: string;
  tags: string[];
  createdAt: string;
  imageUrl?: string;
}

const WebDashboard: React.FC = () => {
  const { isAuthenticated, isLoading, user, logout } = useWebAuth();
  const navigate = useNavigate();
  const [subscription, setSubscription] = useState<any>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(false);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/dashboard/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    if (isAuthenticated && user) {
      // Check if user is admin and redirect to admin dashboard
      const adminEmails = ['<EMAIL>']; // Add other admin emails as needed
      if (user.email && adminEmails.includes(user.email.toLowerCase())) {
        navigate('/admin');
        return;
      }

      fetchSubscription();
    }
  }, [isAuthenticated, user, navigate]);

  const fetchSubscription = async () => {
    setIsLoadingSubscription(true);
    try {
      const token = localStorage.getItem('authToken');
      if (!token) return;

      const response = await fetch('https://api.notely.social/billing/subscription', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSubscription(data.subscription);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setIsLoadingSubscription(false);
    }
  };

  const handleManageBilling = async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) return;

      const response = await fetch('https://api.notely.social/billing/create-portal-session', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          return_url: 'https://notely.social/dashboard'
        })
      });

      if (response.ok) {
        const data = await response.json();
        window.open(data.url, '_blank');
      } else {
        throw new Error('Failed to create billing portal session');
      }
    } catch (error) {
      console.error('Error opening billing portal:', error);
      alert('Failed to open billing portal. Please try again.');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/dashboard/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleOpenExtension = () => {
    try {
      if (window.chrome && window.chrome.runtime) {
        window.chrome.runtime.sendMessage(
          'hnlopcaeidipbmokamhboiooholpecbf', // Extension ID
          { action: 'openDashboard' },
          (response) => {
            if (window.chrome.runtime.lastError) {
              alert('Chrome extension not found. Please install the Notely Chrome extension.');
            } else {
              console.log('Extension opened successfully');
            }
          }
        );
      } else {
        alert('This feature requires the Chrome browser with the Notely extension installed.');
      }
    } catch (error) {
      console.error('Error opening extension:', error);
      alert('Unable to open extension. Please make sure the Notely Chrome extension is installed.');
    }
  };

  const formatDate = (timestamp: number | null | undefined): string => {
    if (!timestamp) return 'N/A';
    try {
      // Convert Unix timestamp to milliseconds
      const date = new Date(timestamp * 1000);
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-notely-bg">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-notely-lavender"></div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-notely-bg">
      {/* Header */}
      <header className="bg-notely-surface border-b border-notely-border shadow-notely-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <img
                src="/notely.svg"
                alt="Notely"
                className="h-8 w-auto mr-3"
              />
              <h1 className="text-xl font-bold text-notely-text-primary">
                Notely Social
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              {user && (
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-semibold text-notely-text-primary">
                        {user.displayName || user.name}
                      </p>
                      <PremiumBadge 
                        plan={user.plan} 
                        subscriptionStatus={user.subscriptionStatus}
                        size="sm"
                      />
                    </div>
                    <p className="text-xs text-notely-text-secondary">{user.email}</p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => navigate('/dashboard/settings')}
                      className="text-sm text-notely-text-secondary hover:text-notely-accent transition-colors"
                    >
                      Settings
                    </button>
                    <button
                      onClick={handleOpenExtension}
                      className="text-sm text-notely-accent hover:text-notely-accent-dark transition-colors font-medium"
                      title="Open Chrome Extension"
                    >
                      🧩 Extension
                    </button>
                    <button
                      onClick={handleLogout}
                      className="text-sm text-red-600 hover:text-red-800 transition-colors"
                    >
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-notely-text-primary mb-2">
            Account Dashboard
          </h2>
          <p className="text-notely-text-secondary">
            Manage your subscription and account settings
          </p>
        </div>

        {/* Chrome Extension Notice */}
        <div className="bg-notely-card rounded-lg p-6 mb-8 border border-notely-border">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-notely-lavender rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-notely-text-primary mb-2">
                Get the Chrome Extension
              </h3>
              <p className="text-notely-text-secondary mb-4">
                To save and manage posts from social media platforms, install our Chrome extension. 
                Your saved posts will sync across all your devices.
              </p>
              <div className="flex space-x-4">
                <a
                  href="https://chrome.google.com/webstore/detail/notely-social"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-notely-lavender text-white rounded-md hover:bg-notely-lavender-dark transition-colors"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  Install Chrome Extension
                </a>
                <a
                  href="/dashboard.html"
                  className="inline-flex items-center px-4 py-2 border border-notely-border text-notely-text-primary rounded-md hover:bg-notely-surface transition-colors"
                >
                  Open Extension Dashboard
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Subscription Management */}
        <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
          <h3 className="text-lg font-semibold text-notely-text-primary mb-4">
            Subscription Management
          </h3>

          {isLoadingSubscription ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-lavender"></div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Current Plan */}
              <div>
                <label className="block text-sm font-medium text-notely-text-secondary mb-1">
                  Current Plan
                </label>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-notely-text-primary capitalize">
                      {user?.plan || 'free'} {user?.subscriptionStatus && `(${user.subscriptionStatus})`}
                    </p>
                    {subscription && subscription.cancelAtPeriodEnd && (
                      <p className="text-xs text-orange-600">
                        Cancels on {formatDate(subscription.currentPeriodEnd)}
                      </p>
                    )}
                  </div>
                  <PremiumBadge
                    plan={user?.plan || 'free'}
                    subscriptionStatus={user?.subscriptionStatus}
                    size="sm"
                  />
                </div>
              </div>

              {/* Free Plan Upgrade */}
              {(!user?.plan || user.plan === 'free') && (
                <div className="border-t border-notely-border pt-4">
                  {user?.subscriptionStatus === 'trialing' && subscription?.trialEnd ? (
                    <p className="text-sm text-blue-600 dark:text-blue-400 mb-3">
                      You're on a free trial until {formatDate(subscription.trialEnd)}. Pick a plan to continue.
                    </p>
                  ) : (
                    <p className="text-sm text-notely-text-secondary mb-3">
                      Upgrade to Premium for unlimited saves, AI features, and more.
                    </p>
                  )}
                  <button
                    onClick={() => {
                      try {
                        import('../../utils/upgradeUtils').then(({ navigateToPlanChooser, UPGRADE_SOURCES }) => {
                          navigateToPlanChooser(UPGRADE_SOURCES.WEB_DASHBOARD);
                        });
                      } catch (error) {
                        console.error('Error navigating to plan chooser:', error);
                        // Fallback to settings page
                        navigate('/dashboard/settings?selectPlan=1');
                      }
                    }}
                    className="bg-notely-lavender text-white text-sm font-medium rounded-md px-4 py-2 hover:bg-notely-lavender/90 transition-colors"
                  >
                    Upgrade to Premium
                  </button>
                </div>
              )}

              {/* Premium Plan - Manage Billing */}
              {user?.plan === 'premium' && (
                <div className="border-t border-notely-border pt-4">
                  <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                    <div>
                      <span className="text-notely-text-secondary">Status:</span>
                      <span className="ml-2 capitalize text-notely-text-primary">{subscription?.status || 'Active'}</span>
                    </div>
                    {subscription?.currentPeriodEnd && (
                      <div>
                        <span className="text-notely-text-secondary">Next billing:</span>
                        <span className="ml-2 text-notely-text-primary">{formatDate(subscription.currentPeriodEnd)}</span>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-notely-text-secondary mb-3">
                    You have Premium access. Manage your subscription and billing.
                  </p>
                  <button
                    onClick={handleManageBilling}
                    className="bg-indigo-600 text-white text-sm font-medium rounded-md px-4 py-2 hover:bg-indigo-700 transition-colors"
                  >
                    Manage Billing
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default WebDashboard;
