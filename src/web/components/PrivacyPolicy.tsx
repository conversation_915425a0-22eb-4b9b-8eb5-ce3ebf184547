import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

const PrivacyPolicy: React.FC = () => {
  const [activeSection, setActiveSection] = useState<string>('');

  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll('section[id]');
      
      sections.forEach(section => {
        const sectionTop = (section as HTMLElement).offsetTop;
        const sectionHeight = (section as HTMLElement).offsetHeight;
        const scrollY = window.scrollY;
        
        if (scrollY >= sectionTop - 100 && scrollY < sectionTop + sectionHeight - 100) {
          setActiveSection(section.id);
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Call once on mount
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="bg-notely-bg min-h-screen">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-notely-surface border-b border-notely-border backdrop-blur-lg bg-opacity-80">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link to="/" className="flex items-center">
                <img src="/notely-dark.svg" alt="Notely" className="h-8 w-auto" />
              </Link>
            </div>
            <nav className="flex space-x-8">
              <Link to="/" className="text-notely-text-secondary hover:text-notely-accent transition-colors">
                Home
              </Link>
              <Link to="/terms" className="text-notely-text-secondary hover:text-notely-accent transition-colors">
                Terms
              </Link>
              <Link to="/privacy" className="text-notely-accent font-medium">
                Privacy
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="lg:grid lg:grid-cols-12 lg:gap-8">
          {/* Sidebar */}
          <aside className="hidden lg:block lg:col-span-3">
            <div className="sticky top-24">
              <h3 className="text-lg font-semibold text-notely-text-primary mb-4">On this page</h3>
              <nav className="space-y-2">
                <button 
                  onClick={() => scrollToSection('introduction')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'introduction' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Introduction
                </button>
                <button 
                  onClick={() => scrollToSection('information-collection')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'information-collection' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Information Collection
                </button>
                <button 
                  onClick={() => scrollToSection('information-usage')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'information-usage' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Information Usage
                </button>
                <button 
                  onClick={() => scrollToSection('data-storage')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'data-storage' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Data Storage
                </button>
                <button 
                  onClick={() => scrollToSection('cookies')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'cookies' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Cookies
                </button>
                <button 
                  onClick={() => scrollToSection('third-party')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'third-party' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Third-Party Services
                </button>
                <button 
                  onClick={() => scrollToSection('user-rights')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'user-rights' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  User Rights
                </button>
                <button 
                  onClick={() => scrollToSection('changes')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'changes' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Changes to Policy
                </button>
                <button 
                  onClick={() => scrollToSection('contact')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'contact' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Contact Us
                </button>
              </nav>
            </div>
          </aside>

          {/* Main Content */}
          <main className="lg:col-span-9">
            <div className="prose prose-invert max-w-none">
              <h1 className="text-4xl font-bold mb-8 text-notely-text-primary">Privacy Policy</h1>
              
              <p className="text-notely-text-secondary mb-8">
                Last updated: June 1, 2023
              </p>

              <section id="introduction" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Introduction</h2>
                <p className="text-notely-text-secondary mb-4">
                  Welcome to Notely's Privacy Policy. This document explains how we collect, use, and protect your personal information when you use our browser extension and website.
                </p>
                <p className="text-notely-text-secondary mb-4">
                  At Notely, we are committed to protecting your privacy and ensuring the security of your personal information. We believe in transparency about our data practices and want you to understand how we handle your information.
                </p>
                <p className="text-notely-text-secondary">
                  By using Notely, you agree to the collection and use of information in accordance with this policy. This Privacy Policy applies to all users of Notely's services, including the browser extension and website.
                </p>
              </section>

              <section id="information-collection" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Information Collection</h2>
                <p className="text-notely-text-secondary mb-4">
                  We collect several types of information for various purposes to provide and improve our service to you:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">
                    <strong className="text-notely-text-primary">Account Information:</strong> When you create an account, we collect your email address and authentication information.
                  </li>
                  <li className="mb-2">
                    <strong className="text-notely-text-primary">Saved Content:</strong> We store the social media posts and content you choose to save using our extension.
                  </li>
                  <li className="mb-2">
                    <strong className="text-notely-text-primary">Usage Data:</strong> We collect information on how you interact with our extension and website, including features used and time spent.
                  </li>
                  <li className="mb-2">
                    <strong className="text-notely-text-primary">Device Information:</strong> We collect information about your browser type, version, and operating system.
                  </li>
                </ul>
                <p className="text-notely-text-secondary">
                  We do not collect or store your browsing history outside of the specific social media posts you choose to save.
                </p>
              </section>

              <section id="information-usage" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Information Usage</h2>
                <p className="text-notely-text-secondary mb-4">
                  We use the collected information for various purposes:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">To provide and maintain our service</li>
                  <li className="mb-2">To notify you about changes to our service</li>
                  <li className="mb-2">To provide customer support</li>
                  <li className="mb-2">To gather analysis or valuable information so that we can improve our service</li>
                  <li className="mb-2">To detect, prevent and address technical issues</li>
                  <li className="mb-2">To provide personalized features like categorization and tagging of saved content</li>
                </ul>
                <p className="text-notely-text-secondary">
                  We will never sell your personal information to third parties.
                </p>
              </section>

              <section id="data-storage" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Data Storage</h2>
                <p className="text-notely-text-secondary mb-4">
                  Your data is stored in two locations:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">
                    <strong className="text-notely-text-primary">Local Storage:</strong> Some data is stored locally on your device for faster access and offline functionality.
                  </li>
                  <li className="mb-2">
                    <strong className="text-notely-text-primary">Cloud Storage:</strong> Your saved content is also synchronized with our secure cloud servers to enable access across multiple devices.
                  </li>
                </ul>
                <p className="text-notely-text-secondary">
                  We implement industry-standard security measures to protect your data from unauthorized access, alteration, disclosure, or destruction.
                </p>
              </section>

              <section id="cookies" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Cookies</h2>
                <p className="text-notely-text-secondary mb-4">
                  We use cookies and similar tracking technologies to track activity on our website and hold certain information.
                </p>
                <p className="text-notely-text-secondary mb-4">
                  Cookies are files with a small amount of data which may include an anonymous unique identifier. Cookies are sent to your browser from a website and stored on your device.
                </p>
                <p className="text-notely-text-secondary">
                  You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent. However, if you do not accept cookies, you may not be able to use some portions of our service.
                </p>
              </section>

              <section id="third-party" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Third-Party Services</h2>
                <p className="text-notely-text-secondary mb-4">
                  We may employ third-party companies and individuals to facilitate our service, provide the service on our behalf, perform service-related services, or assist us in analyzing how our service is used.
                </p>
                <p className="text-notely-text-secondary">
                  These third parties have access to your personal information only to perform these tasks on our behalf and are obligated not to disclose or use it for any other purpose.
                </p>
              </section>

              <section id="user-rights" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">User Rights</h2>
                <p className="text-notely-text-secondary mb-4">
                  You have the following rights regarding your personal information:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">The right to access your personal data</li>
                  <li className="mb-2">The right to rectify inaccurate personal data</li>
                  <li className="mb-2">The right to request the deletion of your personal data</li>
                  <li className="mb-2">The right to restrict processing of your personal data</li>
                  <li className="mb-2">The right to data portability</li>
                  <li className="mb-2">The right to object to the processing of your personal data</li>
                </ul>
                <p className="text-notely-text-secondary">
                  To exercise any of these rights, please contact us using the information provided in the "Contact Us" section.
                </p>
              </section>

              <section id="changes" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Changes to This Privacy Policy</h2>
                <p className="text-notely-text-secondary mb-4">
                  We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.
                </p>
                <p className="text-notely-text-secondary">
                  You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page.
                </p>
              </section>

              <section id="contact" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Contact Us</h2>
                <p className="text-notely-text-secondary mb-4">
                  If you have any questions about this Privacy Policy, please contact us:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">By email: <EMAIL></li>
                  <li className="mb-2">By visiting the contact page on our website: https://notely.social/contact</li>
                </ul>
              </section>
            </div>
          </main>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-notely-surface border-t border-notely-border py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <img src="/notely-dark.svg" alt="Notely" className="h-6 w-auto" />
            </div>
            <div className="flex items-center space-x-6">
              <Link to="/privacy" className="text-notely-accent transition-colors">Privacy Policy</Link>
              <Link to="/terms" className="text-notely-text-secondary hover:text-notely-accent transition-colors">Terms of Service</Link>
              <a href="mailto:<EMAIL>" className="text-notely-text-secondary hover:text-notely-accent transition-colors">Support</a>
            </div>
          </div>
          <div className="mt-8 text-center text-sm text-notely-text-muted">
            © 2023 Notely. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PrivacyPolicy;
