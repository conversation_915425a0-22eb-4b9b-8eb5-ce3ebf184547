import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useWebAuth } from '../context/WebAuthContext';
import { BillingSettings } from '../../components/BillingSettings';

const WebSettings: React.FC = () => {
  const { isAuthenticated, isLoading, user, getToken } = useWebAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/dashboard/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Handle query parameters for plan selection
  useEffect(() => {
    if (isAuthenticated && user) {
      const selectPlan = searchParams.get('selectPlan');
      if (selectPlan === '1') {
        // Auto-scroll to billing settings section
        const timer = setTimeout(() => {
          const billingSection = document.querySelector('[data-section="billing"]');
          if (billingSection) {
            billingSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 500);

        return () => clearTimeout(timer);
      }
    }
  }, [isAuthenticated, user, searchParams]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-notely-bg">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-notely-lavender"></div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-notely-bg">
      {/* Header */}
      <header className="bg-notely-surface border-b border-notely-border shadow-notely-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/dashboard')}
                className="mr-4 text-notely-text-secondary hover:text-notely-accent transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-xl font-bold text-notely-text-primary">
                Settings
              </h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Account Information */}
          <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
            <h3 className="text-lg font-semibold text-notely-text-primary mb-4">
              Account Information
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-notely-text-secondary">
                  Display Name
                </label>
                <p className="mt-1 text-sm text-notely-text-primary">
                  {user.displayName || user.name}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-notely-text-secondary">
                  Email
                </label>
                <p className="mt-1 text-sm text-notely-text-primary">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-notely-text-secondary">
                  Account Type
                </label>
                <p className="mt-1 text-sm text-notely-text-primary capitalize">
                  {user.role} - {user.plan}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-notely-text-secondary">
                  Email Verified
                </label>
                <p className="mt-1 text-sm">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    user.emailVerified 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {user.emailVerified ? 'Verified' : 'Not Verified'}
                  </span>
                </p>
              </div>
            </div>
          </div>

          {/* Billing Settings */}
          <div data-section="billing">
            <BillingSettings user={user} getToken={getToken} />
          </div>

          {/* Chrome Extension */}
          <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
            <h3 className="text-lg font-semibold text-notely-text-primary mb-4">
              Chrome Extension
            </h3>
            <p className="text-notely-text-secondary mb-4">
              Install our Chrome extension to save posts directly from social media platforms.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://chrome.google.com/webstore/detail/notely-social"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-notely-lavender text-white rounded-md hover:bg-notely-lavender-dark transition-colors"
              >
                Install Extension
              </a>
              <a
                href="/dashboard.html"
                className="inline-flex items-center px-4 py-2 border border-notely-border text-notely-text-primary rounded-md hover:bg-notely-surface transition-colors"
              >
                Open Extension Dashboard
              </a>
            </div>
          </div>

          {/* Data & Privacy */}
          <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
            <h3 className="text-lg font-semibold text-notely-text-primary mb-4">
              Data & Privacy
            </h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-notely-text-primary">Data Export</h4>
                <p className="text-sm text-notely-text-secondary mb-2">
                  Download all your saved posts and data.
                </p>
                <button className="text-sm text-notely-lavender hover:text-notely-lavender-dark transition-colors">
                  Request Data Export
                </button>
              </div>
              <div>
                <h4 className="text-sm font-medium text-notely-text-primary">Account Deletion</h4>
                <p className="text-sm text-notely-text-secondary mb-2">
                  Permanently delete your account and all associated data.
                </p>
                <button className="text-sm text-red-600 hover:text-red-800 transition-colors">
                  Delete Account
                </button>
              </div>
            </div>
          </div>

          {/* Support */}
          <div className="bg-notely-card rounded-lg p-6 border border-notely-border">
            <h3 className="text-lg font-semibold text-notely-text-primary mb-4">
              Support
            </h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-notely-text-primary">Help Center</h4>
                <p className="text-sm text-notely-text-secondary mb-2">
                  Find answers to common questions and learn how to use Notely Social.
                </p>
                <a
                  href="/help"
                  className="text-sm text-notely-lavender hover:text-notely-lavender-dark transition-colors"
                >
                  Visit Help Center
                </a>
              </div>
              <div>
                <h4 className="text-sm font-medium text-notely-text-primary">Contact Support</h4>
                <p className="text-sm text-notely-text-secondary mb-2">
                  Get in touch with our support team for assistance.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-sm text-notely-lavender hover:text-notely-lavender-dark transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default WebSettings;
