import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

const TermsOfService: React.FC = () => {
  useEffect(() => {
    // Smooth scrolling for navigation links
    const handleNavClick = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {
        e.preventDefault();
        const targetId = target.getAttribute('href')?.substring(1);
        const targetElement = document.getElementById(targetId || '');
        if (targetElement) {
          targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    };

    document.addEventListener('click', handleNavClick);
    return () => document.removeEventListener('click', handleNavClick);
  }, []);

  const sections = [
    { id: 'eligibility', title: '1. Eligibility' },
    { id: 'account-registration', title: '2. Account Registration' },
    { id: 'use-of-service', title: '3. Use of the Service' },
    { id: 'user-content', title: '4. User Content' },
    { id: 'privacy', title: '5. Privacy' },
    { id: 'intellectual-property', title: '6. Intellectual Property' },
    { id: 'service-availability', title: '7. Service Availability' },
    { id: 'third-party-services', title: '8. Third-Party Services' },
    { id: 'limitation-of-liability', title: '9. Limitation of Liability' },
    { id: 'termination', title: '10. Termination' },
    { id: 'changes-to-terms', title: '11. Changes to Terms' },
    { id: 'contact-us', title: '12. Contact Us' },
  ];

  return (
    <div className="min-h-screen bg-notely-bg">
      {/* Header */}
      <header className="bg-white border-b border-notely-border sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-2">
              <img src="/notely.svg" alt="Notely Logo" className="w-8 h-8" />
              <span className="text-xl font-semibold text-notely-text-primary">Notely</span>
            </Link>
            <nav className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-notely-text-secondary hover:text-notely-accent transition-colors">
                Home
              </Link>
              <Link to="/dashboard" className="text-notely-text-secondary hover:text-notely-accent transition-colors">
                Dashboard
              </Link>
              <Link to="/privacy" className="text-notely-text-secondary hover:text-notely-accent transition-colors">
                Privacy
              </Link>
              <Link to="/terms" className="text-notely-text-secondary hover:text-notely-accent transition-colors">
                Terms
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Main Content */}
          <main className="flex-1 max-w-4xl">
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-5xl font-bold text-notely-text-primary mb-4">
                Terms of Service
              </h1>
              <p className="text-notely-text-secondary text-sm">
                Last Updated: August 14, 2025
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border border-notely-border">
              {/* Introduction */}
              <div className="mb-8">
                <p className="text-notely-text-primary leading-relaxed">
                  These Terms of Service ("Terms") govern your access to and use of the Notely Chrome Extension ("Service"), operated by Notely ("we," "our," or "us"). By installing or using the Service, you agree to these Terms. If you do not agree, you must not use the Service.
                </p>
              </div>

              {/* Section 1: Eligibility */}
              <section id="eligibility" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  1. Eligibility
                </h2>
                <p className="text-notely-text-primary leading-relaxed">
                  You must be at least 16 years old to use Notely. By creating an account, you confirm that you meet this requirement.
                </p>
              </section>

              {/* Section 2: Account Registration */}
              <section id="account-registration" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  2. Account Registration
                </h2>
                <div className="text-notely-text-primary leading-relaxed space-y-4">
                  <p>To access certain features of Notely, you must create an account and provide:</p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Your name</li>
                    <li>Your email address</li>
                    <li>A password (securely stored in encrypted form)</li>
                  </ul>
                  <p>You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.</p>
                </div>
              </section>

              {/* Section 3: Use of the Service */}
              <section id="use-of-service" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  3. Use of the Service
                </h2>
                <div className="text-notely-text-primary leading-relaxed space-y-4">
                  <p>You agree to:</p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Use the Service only for lawful purposes.</li>
                    <li>Not attempt to reverse-engineer, copy, or disrupt the Service.</li>
                    <li>Not use the Service to store, share, or distribute illegal, harmful, or infringing content.</li>
                  </ul>
                  <p>We reserve the right to suspend or terminate your account if you violate these Terms.</p>
                </div>
              </section>

              {/* Section 4: User Content */}
              <section id="user-content" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  4. User Content
                </h2>
                <div className="text-notely-text-primary leading-relaxed space-y-4">
                  <p>Any posts, images, links, or other material you save in Notely ("User Content") remain your property.</p>
                  <p>We do not review, monitor, or analyze your saved content unless required by law.</p>
                  <p>You are solely responsible for ensuring that your User Content complies with applicable laws.</p>
                </div>
              </section>

              {/* Section 5: Privacy */}
              <section id="privacy" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  5. Privacy
                </h2>
                <p className="text-notely-text-primary leading-relaxed">
                  Your personal data is handled in accordance with our{' '}
                  <Link to="/privacy" className="text-notely-accent underline hover:text-notely-accent-hover">
                    Privacy Policy
                  </Link>
                  . By using Notely, you agree to the collection and use of information as outlined in that policy.
                </p>
              </section>

              {/* Section 6: Intellectual Property */}
              <section id="intellectual-property" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  6. Intellectual Property
                </h2>
                <p className="text-notely-text-primary leading-relaxed">
                  All rights, title, and interest in and to the Service, including its software, branding, and design, are owned by or licensed to Notely. You may not copy, modify, or distribute any part of the Service without our prior written consent.
                </p>
              </section>

              {/* Section 7: Service Availability */}
              <section id="service-availability" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  7. Service Availability
                </h2>
                <p className="text-notely-text-primary leading-relaxed">
                  We aim to keep the Service available at all times, but we do not guarantee uninterrupted operation. We may modify, suspend, or discontinue the Service at any time without notice.
                </p>
              </section>

              {/* Section 8: Third-Party Services */}
              <section id="third-party-services" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  8. Third-Party Services
                </h2>
                <p className="text-notely-text-primary leading-relaxed">
                  Notely may integrate with or link to third-party services. We are not responsible for the content, policies, or practices of those third parties.
                </p>
              </section>

              {/* Section 9: Limitation of Liability */}
              <section id="limitation-of-liability" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  9. Limitation of Liability
                </h2>
                <p className="text-notely-text-primary leading-relaxed">
                  To the maximum extent permitted by law, Notely shall not be liable for any direct, indirect, incidental, or consequential damages arising from your use of the Service.
                </p>
              </section>

              {/* Section 10: Termination */}
              <section id="termination" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  10. Termination
                </h2>
                <p className="text-notely-text-primary leading-relaxed">
                  You may stop using Notely at any time. We may suspend or terminate your account if you violate these Terms or if we are required to do so by law. Upon termination, your access to the Service will end, and your data will be deleted in accordance with our Privacy Policy.
                </p>
              </section>

              {/* Section 11: Changes to Terms */}
              <section id="changes-to-terms" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  11. Changes to Terms
                </h2>
                <p className="text-notely-text-primary leading-relaxed">
                  We may update these Terms from time to time. If we make material changes, we will notify you via email or through the Service. Continued use of the Service after such changes constitutes your acceptance of the new Terms.
                </p>
              </section>

              {/* Section 12: Contact Us */}
              <section id="contact-us" className="mb-8">
                <h2 className="text-xl font-semibold text-notely-accent mb-4 pb-2 border-b-2 border-notely-accent inline-block">
                  12. Contact Us
                </h2>
                <div className="text-notely-text-primary leading-relaxed space-y-4">
                  <p>If you have questions about these Terms, please contact:</p>
                  <div className="bg-notely-bg border border-notely-border rounded-lg p-4">
                    <strong className="text-notely-text-primary">📧 <EMAIL></strong>
                  </div>
                </div>
              </section>
            </div>
          </main>

          {/* Sidebar Navigation */}
          <aside className="hidden lg:block w-72 sticky top-24 h-fit">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-notely-border">
              <h3 className="text-lg font-semibold text-notely-text-primary mb-4">
                Table of Contents
              </h3>
              <nav className="space-y-2">
                {sections.map((section) => (
                  <a
                    key={section.id}
                    href={`#${section.id}`}
                    className="block px-3 py-2 text-sm text-notely-text-secondary hover:text-notely-accent hover:bg-notely-bg rounded-md transition-colors"
                  >
                    {section.title}
                  </a>
                ))}
              </nav>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
};

export default TermsOfService;