import React from 'react';
import ReactDOM from 'react-dom/client';
import '../index.css';
import '../styles/notely-theme.css';
import LandingPage from './components/LandingPage';

// Test component to debug
const TestApp = () => {
  return (
    <div className="min-h-screen bg-black text-white p-8">
      <h1 className="text-4xl font-bold mb-4">Debug Test</h1>
      <p className="text-xl mb-4">Server is working! Now testing LandingPage...</p>
      <div className="border border-white p-4">
        <LandingPage />
      </div>
    </div>
  );
};

// Simple direct rendering of the landing page without React Router
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <TestApp />
  </React.StrictMode>
);
