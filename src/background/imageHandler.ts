// Image fetching handler to bypass CORS restrictions for Instagram images

// Timeout for fetch operations in milliseconds
const FETCH_TIMEOUT = 15000; // 15 seconds

// Cache for storing fetched images to avoid redundant fetches
const imageCache: Record<string, string> = {};

/**
 * Converts a blob to a data URL
 */
function blobToDataURL(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * Fetches an image using XMLHttpRequest which has fewer CORS restrictions in extensions
 * @param url The URL to fetch
 * @returns Promise that resolves to a data URL
 */
function fetchImageWithXhr(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    xhr.open('GET', url, true);
    xhr.responseType = 'blob';

    // Set headers to mimic a browser request
    xhr.setRequestHeader('Accept', 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8');
    xhr.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');
    xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36');
    xhr.setRequestHeader('Referer', 'https://www.instagram.com/');
    xhr.setRequestHeader('Origin', 'https://www.instagram.com');
    xhr.setRequestHeader('Sec-Fetch-Dest', 'image');
    xhr.setRequestHeader('Sec-Fetch-Mode', 'no-cors');
    xhr.setRequestHeader('Sec-Fetch-Site', 'cross-site');

    // Set timeout
    xhr.timeout = FETCH_TIMEOUT;

    xhr.onload = async function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const blob = xhr.response;
          const dataUrl = await blobToDataURL(blob);
          resolve(dataUrl);
        } catch (error) {
          reject(new Error(`Error converting blob to data URL: ${error}`));
        }
      } else {
        reject(new Error(`XHR failed with status: ${xhr.status}`));
      }
    };

    xhr.onerror = function() {
      reject(new Error('XHR request failed'));
    };

    xhr.ontimeout = function() {
      reject(new Error(`XHR request timed out for URL: ${url}`));
    };

    xhr.send();
  });
}

/**
 * Attempts to fetch an image with multiple strategies
 */
async function fetchImageWithFallbacks(url: string): Promise<string> {
  // Check if URL is already a data URL
  if (url.startsWith('data:')) {
    return url;
  }

  // Check if image is in cache
  if (imageCache[url]) {
    return imageCache[url];
  }

  // Try direct XHR fetch first (works better in extensions)
  try {
    const dataUrl = await fetchImageWithXhr(url);

    // Cache the result
    imageCache[url] = dataUrl;
    return dataUrl;
  } catch (error) {
    // XHR fetch failed, try fallback
  }

  // Try direct fetch as fallback
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.instagram.com/',
        'Origin': 'https://www.instagram.com'
      }
    });

    if (response.ok) {
      const blob = await response.blob();
      const dataUrl = await blobToDataURL(blob);

      // Cache the result
      imageCache[url] = dataUrl;
      return dataUrl;
    }
  } catch (error) {
    // Direct fetch failed
  }

  // Try using a data URL placeholder with the actual image URL
  try {
    return createImagePlaceholder(url);
  } catch (error) {
    // Image element creation failed
  }

  // If all fetches fail, return a placeholder image
  // Use a placeholder image but don't cache it
  return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMjAwIDIwMCI+PHJlY3Qgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNlZWUiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgYWxpZ25tZW50LWJhc2VsaW5lPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJtb25vc3BhY2UsIHNhbnMtc2VyaWYiIGZpbGw9IiNhYWEiPkltYWdlIE5vdCBBdmFpbGFibGU8L3RleHQ+PC9zdmc+';
}

/**
 * Creates a placeholder image with the URL embedded
 * This is useful for debugging and as a last resort
 */
function createImagePlaceholder(url: string): string {
  // Create a shortened version of the URL for display
  const shortUrl = url.length > 30 ? url.substring(0, 30) + '...' : url;

  // Create an SVG with the URL text
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
      <rect width="200" height="200" fill="#eee"/>
      <text x="50%" y="45%" font-size="14" text-anchor="middle" alignment-baseline="middle" font-family="monospace, sans-serif" fill="#888">Image Not Available</text>
      <text x="50%" y="55%" font-size="10" text-anchor="middle" alignment-baseline="middle" font-family="monospace, sans-serif" fill="#aaa">${shortUrl}</text>
    </svg>
  `;

  // Convert SVG to a data URL
  return 'data:image/svg+xml;base64,' + btoa(svg);
}

// Set up the message listener for image fetching
export function setupImageFetchingHandler() {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // Handle FETCH_IMAGE action to bypass CORS restrictions for Instagram images
    if (message.action === 'FETCH_IMAGE' && message.url) {
      const requestId = message.requestId || Date.now().toString();

      // Start the fetch process
      fetchImageWithFallbacks(message.url)
        .then(dataUrl => {
          // Send response via callback AND message (for backward compatibility)
          sendResponse({
            action: 'FETCH_IMAGE_RESULT',
            requestId,
            status: 'success',
            dataUrl
          });

          // Also send as message for any listeners expecting it
          chrome.runtime.sendMessage({
            action: 'FETCH_IMAGE_RESULT',
            requestId,
            status: 'success',
            dataUrl
          }).catch(() => {
            // Ignore errors if no listeners
          });
        })
        .catch(() => {
          // Send response via callback AND message (for backward compatibility)
          const errorResponse = {
            action: 'FETCH_IMAGE_RESULT',
            requestId,
            status: 'error',
            message: 'Failed to load image'
          };

          sendResponse(errorResponse);

          // Also send as message for any listeners expecting it
          chrome.runtime.sendMessage(errorResponse).catch(() => {
            // Ignore errors if no listeners
          });
        });

      return true; // We're handling this asynchronously
    }

    return false; // Not handled by this listener
  });
}
