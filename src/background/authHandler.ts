// Authentication handler for background script
import { env } from '../config/env';

const SERVER_BASE_URL = env.SERVER_BASE_URL;

export function handleLogin() {
  // Create the OAuth 2.0 URL with the required parameters
  const authUrl = new URL(`${SERVER_BASE_URL}/auth/google`);
  
  // Open the auth window
  chrome.windows.create({
    url: authUrl.toString(),
    type: 'popup',
    width: 600,
    height: 700
  });
}

// Listen for messages from the auth callback page
chrome.runtime.onMessageExternal.addListener((message, sender) => {
  if (message.type === 'AUTH_CALLBACK' && message.token) {
    // Store the token
    void (async () => {
      await chrome.storage.local.set({
        'auth_token': message.token,
        'authToken': message.token, // Also store as authToken for compatibility
        'auth_timestamp': Date.now()
      });
      // Fetch user profile data
      try {
        const response = await fetch(`${SERVER_BASE_URL}/auth/me`, {
          headers: {
            'Authorization': `Bearer ${message.token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const userData = await response.json();
          await chrome.storage.local.set({ user_profile: userData });
        } else {
          console.error('[Background] Failed to fetch user profile');
        }
      } catch (error) {
        console.error('[Background] Error fetching user profile');
      }

      // Close the auth window if it's still open
      if (sender.tab?.windowId) {
        chrome.windows.remove(sender.tab.windowId);
      }

      // Notify any listeners that auth is complete
      chrome.runtime.sendMessage({
        type: 'AUTH_COMPLETED',
        success: true
      });
    });
  }
});
