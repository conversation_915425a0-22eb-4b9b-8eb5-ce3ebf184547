/**
 * Button Manager - Unified button creation and styling for all platforms
 * 
 * This module provides consistent button styling and behavior across all social media platforms.
 * It centralizes button creation, state management, and styling to avoid duplication.
 */

import { Platform } from '../../types';

export interface ButtonConfig {
  text?: string;
  className?: string;
  position?: 'inline' | 'absolute';
  platform?: Platform;
}

export interface ButtonState {
  idle: string;
  saving: string;
  saved: string;
  error: string;
}

export class ButtonManager {
  private static readonly SAVE_BUTTON_CLASS = 'notely-save-button';
  private static readonly PROCESSED_ELEMENT_CLASS = 'notely-processed';

  private static readonly DEFAULT_BUTTON_STATES: ButtonState = {
    idle: 'Save Post',
    saving: 'Saving...',
    saved: 'Saved!',
    error: 'Error'
  };

  /**
   * Create a standardized save button
   */
  static createSaveButton(config: ButtonConfig = {}): HTMLButtonElement {
    const button = document.createElement('button');
    button.className = config.className || this.SAVE_BUTTON_CLASS;

    // Use platform-specific text
    const buttonText = config.text || this.getButtonText(config.platform);
    
    // Create button with icon + text
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
      </svg>
      <span>${buttonText}</span>
    `;

    // Apply base styles
    button.style.cssText = this.getButtonStyles(config.platform, config.position);

    return button;
  }

  /**
   * Get platform-specific button styles
   */
  private static getButtonStyles(platform?: Platform, position: 'inline' | 'absolute' = 'inline'): string {
    const baseStyles = `
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 20px;
      height: 32px;
      padding: 0 12px;
      margin: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 9999;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    `;

    const positionStyles = position === 'absolute' ? `
      position: absolute;
      bottom: 8px;
      right: 12px;
      z-index: 1000;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    ` : '';

    // Platform-specific adjustments
    const platformStyles = this.getPlatformSpecificStyles(platform);

    return baseStyles + positionStyles + platformStyles;
  }

  /**
   * Get platform-specific button text
   */
  private static getButtonText(platform?: Platform): string {
    switch (platform) {
      case 'LinkedIn':
        return 'Save';
      default:
        return this.DEFAULT_BUTTON_STATES.idle;
    }
  }

  /**
   * Get platform-specific style adjustments
   */
  private static getPlatformSpecificStyles(platform?: Platform): string {
    switch (platform) {
      case 'X/Twitter':
        return `
          /* Twitter already uses 20px radius in base styles */
        `;
      case 'Instagram':
        return `
          /* Use Instagram's rounded style */
          border-radius: 8px;
        `;
      case 'LinkedIn':
        return `
          /* Use LinkedIn's more square style */
          border-radius: 4px;
        `;
      default:
        return '';
    }
  }

  /**
   * Update button state with appropriate styling
   */
  static updateButtonState(
    button: HTMLButtonElement,
    state: keyof ButtonState,
    customText?: string,
    platform?: Platform
  ): void {
    let text = customText;
    if (!text) {
      if (state === 'idle' && platform) {
        text = this.getButtonText(platform);
      } else {
        text = this.DEFAULT_BUTTON_STATES[state];
      }
    }
    
    // Update button content with appropriate icon and text
    let icon = '';
    switch (state) {
      case 'saving':
        icon = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 6v6l4 2"/>
               </svg>`;
        break;
      case 'saved':
        icon = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 6L9 17l-5-5"/>
               </svg>`;
        break;
      case 'error':
        icon = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
               </svg>`;
        break;
      case 'idle':
      default:
        if (state === 'idle') {
          icon = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                 </svg>`;
        }
        break;
    }
    
    button.innerHTML = `${icon}<span>${text}</span>`;

    // Update styling based on state
    switch (state) {
      case 'saving':
        button.disabled = true;
        button.style.backgroundColor = '#999999';
        button.style.cursor = 'not-allowed';
        break;
      case 'saved':
        button.disabled = false;
        button.style.backgroundColor = '#17bf63';
        button.style.cursor = 'pointer';
        break;
      case 'error':
        button.disabled = false;
        button.style.backgroundColor = '#e0245e';
        button.style.cursor = 'pointer';
        break;
      case 'idle':
      default:
        button.disabled = false;
        button.style.backgroundColor = '#4CAF50';
        button.style.cursor = 'pointer';
        break;
    }
  }

  /**
   * Reset button to idle state after a delay
   */
  static resetButtonAfterDelay(
    button: HTMLButtonElement, 
    delay: number = 3000,
    platform?: Platform
  ): void {
    setTimeout(() => {
      if (button.parentNode) {
        // Update to idle state with icon and text
        this.updateButtonState(button, 'idle', undefined, platform);
        
        // Reapply base styles to ensure consistent appearance
        const baseStyles = this.getButtonStyles(platform);
        
        // Apply styles while preserving the innerHTML we just set
        button.style.cssText = baseStyles;
        button.disabled = false;
      }
    }, delay);
  }

  /**
   * Add click handler with standard behavior
   */
  static addClickHandler(
    button: HTMLButtonElement,
    handler: (event: MouseEvent) => void | Promise<void>
  ): void {
    button.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      handler(event);
    });
  }

  /**
   * Check if element has already been processed
   */
  static isProcessed(element: Element): boolean {
    return element.classList.contains(this.PROCESSED_ELEMENT_CLASS);
  }

  /**
   * Mark element as processed
   */
  static markAsProcessed(element: Element): void {
    element.classList.add(this.PROCESSED_ELEMENT_CLASS);
  }

  /**
   * Get the save button class name
   */
  static getSaveButtonClass(): string {
    return this.SAVE_BUTTON_CLASS;
  }

  /**
   * Get the processed element class name
   */
  static getProcessedElementClass(): string {
    return this.PROCESSED_ELEMENT_CLASS;
  }

  /**
   * Find the best insertion point for a button in a post element
   */
  static findButtonInsertionPoint(
    postElement: HTMLElement,
    platform: Platform,
    selectors: string[] = []
  ): { container: HTMLElement; method: 'append' | 'prepend' | 'absolute' } {
    // Platform-specific selectors for action bars/footers
    const platformSelectors = this.getPlatformSelectors(platform);
    const allSelectors = [...selectors, ...platformSelectors];

    // Try to find an appropriate container
    for (const selector of allSelectors) {
      const container = postElement.querySelector(selector) as HTMLElement;
      if (container) {
        return { container, method: 'append' };
      }
    }

    // Fallback to absolute positioning
    return { container: postElement, method: 'absolute' };
  }

  /**
   * Get platform-specific selectors for button placement
   */
  private static getPlatformSelectors(platform: Platform): string[] {
    switch (platform) {
      case 'X/Twitter':
        return [
          'div[role="group"]',
          '[data-testid="tweet"] footer',
          '[data-testid="tweetDetail"] footer'
        ];
      case 'Instagram':
        return [
          'section',
          '.post-actions',
          'article footer'
        ];
      case 'LinkedIn':
        return [
          '.update-components-footer',
          '.feed-shared-social-actions',
          '.social-details-social-actions',
          '.feed-shared-social-action-bar'
        ];
      default:
        return [];
    }
  }
}
