/**
 * LinkedIn Service
 *
 * Handles LinkedIn-specific post extraction and saving logic.
 */

import { Post, Platform, MediaItem } from '../../types';
import { BasePlatformService } from '../base/BasePlatformService';
import { ExtractionOptions, SaveOptions, ExtractionResult, SaveResult, ImageExtractionResult } from '../base/types';
import { savePost } from '../../storage';

export class LinkedInService extends BasePlatformService {
  constructor() {
    super('LinkedIn');
  }
  
  /**
   * Extract video sources from LinkedIn post element
   * @param element The post element
   * @returns Array of video MediaItems
   */
  private extractVideoSources(element: HTMLElement): MediaItem[] {
    const videos: MediaItem[] = [];
    const videoElements = element.querySelectorAll('video');
    
    videoElements.forEach((videoEl, index) => {
      if (videoEl instanceof HTMLVideoElement) {
        let src = '';
        
        // Try to get video source from src attribute
        if (videoEl.src) {
          src = videoEl.src;
        } 
        // Try to get from source elements
        else {
          const sources = videoEl.querySelectorAll('source');
          if (sources.length > 0) {
            // Get the first available source
            for (const source of sources) {
              if (source instanceof HTMLSourceElement && source.src) {
                src = source.src;
                break;
              }
            }
          }
        }
        
        if (src) {
          console.log(`[LinkedIn] 🎥 Found video ${index + 1}: ${src.substring(0, 50)}...`);
          videos.push({ 
            type: 'video', 
            url: src, 
            alt: `LinkedIn video ${index + 1}`,
            width: videoEl.videoWidth || undefined,
            height: videoEl.videoHeight || undefined
          });
        } else if (videoEl.poster) {
          // If no video source but has poster, treat as image fallback
          console.log(`[LinkedIn] 🎥 Found video poster as fallback: ${videoEl.poster.substring(0, 50)}...`);
          videos.push({ 
            type: 'image', 
            url: videoEl.poster, 
            alt: `LinkedIn video thumbnail ${index + 1}` 
          });
        }
      }
    });
    
    return videos;
  }

  /**
   * Parse count text like '1.2K' or '1,234' to a number
   * @param countText The text to parse
   * @returns The parsed number
   */
  private parseCountText(countText: string): number {
    try {
      // Remove any non-numeric characters except for K, M, B, and decimal point
      const sanitized = countText.replace(/[^0-9KMBkmb.]/g, '');
      
      if (sanitized.length === 0) return 0;
      
      // Check for K, M, B suffixes
      if (/[KkMmBb]$/.test(sanitized)) {
        const number = parseFloat(sanitized.slice(0, -1));
        const suffix = sanitized.slice(-1).toUpperCase();
        
        if (suffix === 'K') return Math.round(number * 1000);
        if (suffix === 'M') return Math.round(number * 1000000);
        if (suffix === 'B') return Math.round(number * 1000000000);
      }
      
      return parseInt(sanitized, 10) || 0;
    } catch (error) {
      console.error('[LinkedIn] Error parsing count text:', error);
      return 0;
    }
  }

  /**
   * Convert relative LinkedIn timestamp to absolute date
   * @param relativeTime The relative time string (e.g., '7h', '2d', '1w')
   * @returns ISO date string
   */
  private convertRelativeTime(relativeTime: string): string {
    try {
      const now = new Date();
      const match = relativeTime.match(/^(\d+)([smhdw])$/);
      
      if (!match) {
        return now.toISOString(); // Default to current time if format doesn't match
      }

      const [, value, unit] = match;
      const num = parseInt(value, 10);

      switch (unit) {
        case 's': // seconds
          now.setSeconds(now.getSeconds() - num);
          break;
        case 'm': // minutes
          now.setMinutes(now.getMinutes() - num);
          break;
        case 'h': // hours
          now.setHours(now.getHours() - num);
          break;
        case 'd': // days
          now.setDate(now.getDate() - num);
          break;
        case 'w': // weeks
          now.setDate(now.getDate() - (num * 7));
          break;
        default:
          break;
      }

      return now.toISOString();
    } catch (error) {
      console.error('[LinkedIn] Error converting relative time:', error);
      return new Date().toISOString();
    }
  }

  /**
   * Extract post data from a LinkedIn post element
   * @param element The post element
   * @param options Options for extraction
   * @returns ExtractionResult containing the extracted post data or an error
   */
  async extractPostData(element: HTMLElement, options?: ExtractionOptions): Promise<ExtractionResult> {
    try {
      console.log('[LinkedIn] Starting post extraction');

      // LinkedIn-specific extraction logic with multiple selector fallbacks

      // Extract author information with multiple selector options
      let authorElement = element.querySelector('.update-components-actor__name');
      if (!authorElement) {
        authorElement = element.querySelector('.feed-shared-actor__name');
      }
      if (!authorElement) {
        authorElement = element.querySelector('.update-components-actor__title');
      }

      // Clean up the author name to remove duplicates and extra content
      let authorName = authorElement?.textContent?.trim() || '';

      // Remove common LinkedIn UI elements and duplicates
      authorName = authorName
        .replace(/\s*•\s*Following.*$/i, '') // Remove "• Following" and everything after
        .replace(/\s*Verified.*$/i, '') // Remove "Verified" and everything after
        .replace(/\s*\n.*$/s, '') // Remove everything after first newline
        .replace(/(.+?)\1+/g, '$1') // Remove duplicates (e.g., "John SmithJohn Smith" -> "John Smith")
        .trim();

      console.log('[LinkedIn] Extracted author name:', authorName);

      // Extract author title/subtitle (professional title)
      let authorTitleElement = null;

      // Look for the subtitle/title element in the actor area
      const actorArea = element.querySelector('.update-components-actor, .feed-shared-actor');
      if (actorArea) {
        // Try different selectors for the author title/subtitle
        authorTitleElement = actorArea.querySelector('.update-components-actor__description');
        if (!authorTitleElement) {
          authorTitleElement = actorArea.querySelector('.feed-shared-actor__description');
        }
        if (!authorTitleElement) {
          authorTitleElement = actorArea.querySelector('.update-components-actor__sub-description');
        }
        if (!authorTitleElement) {
          authorTitleElement = actorArea.querySelector('.feed-shared-actor__sub-description');
        }
        if (!authorTitleElement) {
          // Look for elements with specific LinkedIn title patterns
          authorTitleElement = actorArea.querySelector('.update-components-actor__meta .visually-hidden');
        }
        if (!authorTitleElement) {
          // Look in the meta area for title information
          const metaArea = actorArea.querySelector('.update-components-actor__meta, .feed-shared-actor__meta');
          if (metaArea) {
            const spans = metaArea.querySelectorAll('span');
            for (const span of spans) {
              const text = span.textContent?.trim() || '';
              if (text && text !== authorName && !text.match(/^\d+[smhdw]/) && !text.includes('•') && text.length > 10) {
                authorTitleElement = span;
                break;
              }
            }
          }
        }
        if (!authorTitleElement) {
          // Look for any element that contains professional title patterns
          const titleElements = actorArea.querySelectorAll('span, div');
          for (const el of titleElements) {
            const text = el.textContent?.trim() || '';
            // Check if this looks like a professional title (contains common job-related keywords or patterns)
            if (text && text !== authorName && text.length > 5 &&
                (text.includes('|') || text.includes(' / ') || text.includes(' at ') ||
                 text.includes('Manager') || text.includes('Director') || text.includes('Engineer') ||
                 text.includes('Designer') || text.includes('Developer') || text.includes('Analyst') ||
                 text.includes('Specialist') || text.includes('Consultant') || text.includes('Lead') ||
                 text.includes('Senior') || text.includes('Junior') || text.includes('Associate') ||
                 text.includes('Executive') || text.includes('Founder') || text.includes('CEO') ||
                 text.includes('CTO') || text.includes('VP') || text.includes('Head of') ||
                 text.includes('Creator') || text.includes('Expert') || text.includes('Strategist'))) {
              authorTitleElement = el;
              break;
            }
          }
        }
      }

      const authorTitle = authorTitleElement?.textContent?.trim() || '';
      console.log('[LinkedIn] Extracted author title:', authorTitle || 'Not found');

      // Extract timestamp from LinkedIn post
      let timestampElement = null;
      let timestamp = '';

      // Try different selectors for timestamp
      timestampElement = element.querySelector('.update-components-actor__sub-description time');
      if (!timestampElement) {
        timestampElement = element.querySelector('.feed-shared-actor__sub-description time');
      }
      if (!timestampElement) {
        timestampElement = element.querySelector('.update-components-actor__meta time');
      }
      if (!timestampElement) {
        timestampElement = element.querySelector('.feed-shared-actor__meta time');
      }
      if (!timestampElement) {
        // Try to find timestamp in text content
        const metaElements = element.querySelectorAll('.update-components-actor__meta span, .feed-shared-actor__meta span');
        for (const el of metaElements) {
          const text = el.textContent?.trim() || '';
          if (text.match(/^\d+[smhdw]$/)) {
            timestampElement = el;
            break;
          }
        }
      }

      timestamp = timestampElement?.textContent?.trim() || '';
      console.log('[LinkedIn] Extracted timestamp:', timestamp);

      // Convert relative timestamp to absolute date
      const absoluteTimestamp = this.convertRelativeTime(timestamp);
      console.log('[LinkedIn] Converted to absolute timestamp:', absoluteTimestamp);

      // Extract author avatar with very specific selectors based on actual LinkedIn structure
      let authorAvatarElement = null;
      let selectorUsed = 'none';

      // Try the most specific selector first - the actual author avatar
      authorAvatarElement = element.querySelector('.update-components-actor__avatar-image');
      if (authorAvatarElement) selectorUsed = '.update-components-actor__avatar-image';

      if (!authorAvatarElement) {
        authorAvatarElement = element.querySelector('.update-components-actor__image-container img');
        if (authorAvatarElement) selectorUsed = '.update-components-actor__image-container img';
      }

      if (!authorAvatarElement) {
        authorAvatarElement = element.querySelector('.feed-shared-actor__avatar img');
        if (authorAvatarElement) selectorUsed = '.feed-shared-actor__avatar img';
      }

      if (!authorAvatarElement) {
        // Look specifically in the actor avatar container
        const avatarContainer = element.querySelector('.js-update-components-actor__avatar, .update-components-actor__avatar');
        if (avatarContainer) {
          authorAvatarElement = avatarContainer.querySelector('img');
          if (authorAvatarElement) selectorUsed = 'avatar container img';
        }
      }

      if (!authorAvatarElement) {
        // Look for images with specific alt text patterns that indicate author avatar
        const actorArea = element.querySelector('.update-components-actor, .feed-shared-actor');
        if (actorArea) {
          // Look for images with alt text containing "View [name]'s" which is the author avatar pattern
          authorAvatarElement = actorArea.querySelector('img[alt*="View "][alt*="\'s"]');
          if (authorAvatarElement) selectorUsed = 'alt text View [name]\'s';

          if (!authorAvatarElement) {
            // Look for images with profile-related alt text
            authorAvatarElement = actorArea.querySelector('img[alt*="profile"], img[alt*="avatar"]');
            if (authorAvatarElement) selectorUsed = 'alt text profile/avatar';
          }
        }
      }

      const authorAvatar = authorAvatarElement instanceof HTMLImageElement ? authorAvatarElement.src : '';
      console.log('[LinkedIn] Extracted author avatar:', authorAvatar ? `Found using: ${selectorUsed}` : 'Not found');
      if (authorAvatarElement && authorAvatarElement instanceof HTMLImageElement) {
        console.log('[LinkedIn] Avatar alt text:', authorAvatarElement.alt);
        console.log('[LinkedIn] Avatar src:', authorAvatarElement.src);
      }

      // Extract post content with multiple selector options
      let contentElement = element.querySelector('.update-components-text');
      if (!contentElement) {
        contentElement = element.querySelector('.feed-shared-update-v2__description');
      }
      if (!contentElement) {
        contentElement = element.querySelector('.feed-shared-text');
      }

      const textContent = contentElement?.textContent?.trim() || '';
      console.log('[LinkedIn] Extracted text content:', textContent ? 'Found' : 'Not found');

      // Extract post URL - prioritize building from activity URN for accuracy
      let permalink = '';

      // Method 1: Build URL from activity URN (most reliable)
      const activityUrn = element.getAttribute('data-id') || element.getAttribute('data-urn');
      if (activityUrn && activityUrn.includes('urn:li:activity:')) {
        const activityIdMatch = activityUrn.match(/urn:li:activity:(\d+)/);
        if (activityIdMatch && activityIdMatch[1]) {
          const activityId = activityIdMatch[1];
          permalink = `https://www.linkedin.com/feed/update/urn:li:activity:${activityId}/`;
          console.log('[LinkedIn] Built permalink from activity URN:', permalink);
        }
      }

      // Method 2: Search for activity URN in nested elements if not found on root
      if (!permalink) {
        const allElements = element.querySelectorAll('*');
        for (const el of allElements) {
          const dataId = el.getAttribute('data-id') || el.getAttribute('data-urn');
          if (dataId && dataId.includes('urn:li:activity:')) {
            const activityIdMatch = dataId.match(/urn:li:activity:(\d+)/);
            if (activityIdMatch && activityIdMatch[1]) {
              const activityId = activityIdMatch[1];
              permalink = `https://www.linkedin.com/feed/update/urn:li:activity:${activityId}/`;
              console.log('[LinkedIn] Built permalink from nested activity URN:', permalink);
              break;
            }
          }

          // Also check other data attributes that might contain activity URNs
          for (const attr of el.attributes) {
            if (attr.name.startsWith('data-') && attr.value.includes('urn:li:activity:')) {
              const activityIdMatch = attr.value.match(/urn:li:activity:(\d+)/);
              if (activityIdMatch && activityIdMatch[1]) {
                const activityId = activityIdMatch[1];
                permalink = `https://www.linkedin.com/feed/update/urn:li:activity:${activityId}/`;
                console.log('[LinkedIn] Built permalink from data attribute activity URN:', permalink);
                break;
              }
            }
          }
          if (permalink) break;
        }
      }

      // Method 3: Try to find existing permalink elements (fallback)
      if (!permalink) {
        let permalinkElement = element.querySelector('.update-components-update-v2__permalink-link');
        if (!permalinkElement) {
          permalinkElement = element.querySelector('.feed-shared-update-v2__permalink');
        }
        if (!permalinkElement) {
          permalinkElement = element.querySelector('a[data-control-name="update_permalink"]');
        }
        if (!permalinkElement) {
          permalinkElement = element.querySelector('a[href*="/feed/update/"]');
        }
        if (!permalinkElement) {
          permalinkElement = element.querySelector('a[href*="/posts/"]');
        }
        if (!permalinkElement) {
          // Look for timestamp links which often contain permalinks
          permalinkElement = element.querySelector('time[datetime] a, a time[datetime]');
        }
        if (!permalinkElement) {
          // Look for any link in the header area that might be a permalink
          const headerArea = element.querySelector('.update-components-actor, .feed-shared-actor');
          if (headerArea) {
            const links = headerArea.querySelectorAll('a[href*="linkedin.com"]');
            for (const link of links) {
              const href = link.getAttribute('href') || '';
              if (href.includes('/posts/') || href.includes('/feed/update/') || href.includes('/activity-')) {
                permalinkElement = link;
                break;
              }
            }
          }
        }

        if (permalinkElement instanceof HTMLAnchorElement && permalinkElement.href) {
          permalink = permalinkElement.href;
          console.log('[LinkedIn] Found existing permalink element:', permalink);
        }
      }

      // Method 4: Extract from current page URL if it's a post page
      if (!permalink) {
        const currentUrl = window.location.href;
        if (currentUrl.includes('/posts/') || currentUrl.includes('/feed/update/')) {
          permalink = currentUrl;
          console.log('[LinkedIn] Using current page URL as permalink:', permalink);
        }
      }

      // Method 5: Generate fallback URL (last resort)
      if (!permalink) {
        const currentUrl = window.location.href;
        const timestamp = Date.now();
        permalink = `${currentUrl}#post-${timestamp}`;
        console.log('[LinkedIn] Generated fallback permalink:', permalink);
      }

      console.log('[LinkedIn] Final permalink result:', permalink ? 'Found' : 'Not found');
      if (permalink) {
        console.log('[LinkedIn] Final permalink URL:', permalink);
        // Validate that we have a proper LinkedIn URL
        if (permalink.includes('linkedin.com') && (permalink.includes('/feed/update/') || permalink.includes('/posts/'))) {
          console.log('[LinkedIn] ✅ Valid LinkedIn post URL extracted');
        } else {
          console.log('[LinkedIn] ⚠️ Fallback URL generated (not a direct post link)');
        }
      } else {
        console.error('[LinkedIn] ❌ Failed to extract any permalink');
      }

      // Generate a unique ID
      const id = `linkedin-${Date.now()}-${Math.floor(Math.random() * 10000)}`;

      // Extract videos
      const videoItems = this.extractVideoSources(element);
      console.log(`[LinkedIn] Found ${videoItems.length} video(s)`);

      // Extract engagement metrics (likes, comments, shares)
      let likes = 0;
      let comments = 0;
      let shares = 0;
      
      // Try to find reactions/likes count with multiple selectors
      // First try the specific reactions count element
      const reactionsSelector = element.querySelector('.social-details-social-counts__reactions-count');
      if (reactionsSelector) {
        const likesText = reactionsSelector.textContent?.trim();
        if (likesText) {
          // Convert text like '1,234' or '1.2K' to number
          likes = this.parseCountText(likesText);
          console.log('[LinkedIn] Extracted likes count:', likes);
        }
      }
      
      // If no reactions found, try looking for like button text
      if (likes === 0) {
        const likeButton = element.querySelector('[aria-label*="like"] span, [aria-label*="Like"] span');
        if (likeButton) {
          const likesText = likeButton.textContent?.trim();
          if (likesText && /\d/.test(likesText)) { // Only if it contains a number
            likes = this.parseCountText(likesText);
            console.log('[LinkedIn] Extracted likes from button:', likes);
          }
        }
      }
      
      // Extract comments count - look for text containing "comments"
      const commentElements = Array.from(element.querySelectorAll('button, span'));
      for (const el of commentElements) {
        const text = el.textContent?.trim() || '';
        if (text.toLowerCase().includes('comment')) {
          // Extract number from text like "24 comments"
          const match = text.match(/(\d[\d.,]*\s*[KkMmBb]?)\s*comments?/i);
          if (match && match[1]) {
            comments = this.parseCountText(match[1]);
            console.log('[LinkedIn] Extracted comments count:', comments);
            break;
          }
        }
      }
      
      // Extract shares/reposts count - look for text containing "repost"
      const shareElements = Array.from(element.querySelectorAll('button, span'));
      for (const el of shareElements) {
        const text = el.textContent?.trim() || '';
        if (text.toLowerCase().includes('repost')) {
          // Extract number from text like "94 reposts"
          const match = text.match(/(\d[\d.,]*\s*[KkMmBb]?)\s*reposts?/i);
          if (match && match[1]) {
            shares = this.parseCountText(match[1]);
            console.log('[LinkedIn] Extracted reposts count:', shares);
            break;
          }
        }
      }

      // Prepare media array with videos and images
      let mediaItems: MediaItem[] = [];
      
      // Add videos first
      if (videoItems.length > 0) {
        mediaItems = mediaItems.concat(videoItems);
      }
      
      // Extract images if requested and no videos found (to avoid duplicates)
      if (options?.includeImages !== false && videoItems.length === 0) {
        const imageResult = await this.extractImageUrls(element, options);
        if (imageResult.success && imageResult.images) {
          console.log(`[LinkedIn] 🖼️ Found ${imageResult.images.length} image(s)`);
          mediaItems = mediaItems.concat(imageResult.images);
        }
      }

      // Create the post object with extracted data
      const post: Post = {
        id,
        platform: 'LinkedIn',
        author: authorName,
        authorName,
        authorTitle,
        authorUrl: `https://www.linkedin.com/in/${authorName.toLowerCase().replace(/\s+/g, '-')}`,
        authorAvatar,
        textContent,
        content: textContent,
        timestamp: absoluteTimestamp, // Use the converted absolute timestamp
        permalink,
        savedAt: new Date().toISOString(),
        media: mediaItems, // Use combined media items
        stats: {
          likes,
          comments,
          shares,
          views: 0
        },
        categories: [],
        tags: []
      };

      console.log('[LinkedIn] Post extraction completed successfully');
      return {
        success: true,
        post
      };
    } catch (error) {
      console.error('[LinkedIn] Error extracting post data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Extract image URLs from a LinkedIn post element or post data
   * @param source The post element or post data
   * @param options Options for extraction
   * @returns ImageExtractionResult containing the extracted images or an error
   */
  async extractImageUrls(source: Post | HTMLElement, options?: ExtractionOptions): Promise<ImageExtractionResult> {
    try {
      console.log('[LinkedIn] Starting image extraction');
      const images: MediaItem[] = [];
      const uniqueUrls = new Set<string>(); // To avoid duplicate images

      if (source instanceof HTMLElement) {
        // LinkedIn-specific image extraction logic from DOM with multiple selector fallbacks

        // Enhanced selectors for images including carousel/slide detection
        const selectors = [
          '.update-components-image img',
          '.feed-shared-image img',
          '.feed-shared-update-v2__content img',
          '.feed-shared-article__image img',
          '.feed-shared-external-video__image img',
          '.feed-shared-mini-article__image img',
          '.feed-shared-update-v2 img[data-test-id="image"]',
          '.artdeco-card img[width]',
          // Additional selectors for LinkedIn carousels/slides
          '.feed-shared-document-carousel img',
          '.feed-shared-carousel img',
          '.document-carousel img',
          '.carousel-item img',
          '.slide img',
          // More comprehensive carousel selectors
          '.feed-shared-carousel-card img',
          '.feed-shared-document-carousel-card img',
          '.document-carousel-card img',
          '.carousel-card img',
          '.feed-shared-update-v2__content .carousel img',
          '.feed-shared-update-v2__content .document-carousel img',
          // Generic image selectors for carousel content
          'div[role="group"] img',
          'div[aria-label*="carousel"] img',
          'div[aria-label*="slide"] img',
          'div[data-test-id*="carousel"] img',
          'div[data-test-id*="slide"] img',
          // LinkedIn document carousel selectors (NEW - based on actual structure)
          '.ssplayer-carousel img',
          '.carousel-lazy-element',
          '.carousel-slide img',
          '.carousel-track img',
          '.carousel-track-container img',
          // Broader selectors for missed images
          '.feed-shared-update-v2 img[src*="media.licdn.com"]',
          '.update-components-update-v2 img[src*="media.licdn.com"]'
        ];

        // Try each selector and collect ALL images (don't break early for multi-image posts)
        let foundImagesWithSelector = false;

        for (const selector of selectors) {
          const imgElements = source.querySelectorAll(selector);
          console.log(`[LinkedIn] Found ${imgElements.length} images with selector: ${selector}`);

          imgElements.forEach((img) => {
            if (img instanceof HTMLImageElement && img.src) {
              // Skip profile images, icons, and small images
              if (!img.src.includes('profile-image') &&
                  !img.src.includes('icon') &&
                  !img.src.includes('emoji') &&
                  (img.width > 100 || img.height > 100)) {

                // Skip if we already have this URL
                if (!uniqueUrls.has(img.src)) {
                  uniqueUrls.add(img.src);
                  images.push({
                    type: 'image',
                    url: img.src,
                    alt: img.alt || 'LinkedIn image',
                    width: img.width || undefined,
                    height: img.height || undefined
                  });
                  foundImagesWithSelector = true;
                  console.log(`[LinkedIn] Added image: ${img.src.substring(0, 80)}... (${img.width}x${img.height})`);
                }
              } else {
                console.log(`[LinkedIn] Skipped image: ${img.src.substring(0, 80)}... (${img.width}x${img.height}) - profile/icon/small`);
              }
            }
          });
        }

        // If no images found with standard selectors, try a broader search
        if (images.length === 0) {
          console.log('[LinkedIn] No images found with standard selectors, trying broader search...');

          // Try to find images in a different way - look for any large images
          const allImages = source.querySelectorAll('img');
          console.log(`[LinkedIn] Found ${allImages.length} total img elements in post`);

          allImages.forEach((img, index) => {
            if (img instanceof HTMLImageElement && img.src) {
              const width = img.naturalWidth || img.width || 0;
              const height = img.naturalHeight || img.height || 0;
              const isContentImage = width > 200 && height > 200 &&
                                   img.src.includes('media.licdn.com') &&
                                   !img.src.includes('profile-') &&
                                   !img.src.includes('company-logo') &&
                                   !img.src.includes('icon');

              console.log(`[LinkedIn] Image ${index + 1}: ${img.src.substring(0, 80)}... (${width}x${height}) content=${isContentImage}`);

              if (isContentImage && !uniqueUrls.has(img.src)) {
                uniqueUrls.add(img.src);
                images.push({
                  type: 'image',
                  url: img.src,
                  alt: img.alt || 'LinkedIn image',
                  width: width || undefined,
                  height: height || undefined
                });
                foundImagesWithSelector = true;
                console.log(`[LinkedIn] ✅ Added content image: ${img.src.substring(0, 80)}...`);
              }
            }
          });

          // Check for LinkedIn document carousel iframes
          const iframes = source.querySelectorAll('iframe');
          console.log(`[LinkedIn] Found ${iframes.length} iframes in post`);

          for (const iframe of iframes) {
            try {
              if (iframe instanceof HTMLIFrameElement && iframe.contentDocument) {
                console.log('[LinkedIn] Accessing iframe content for carousel images...');
                const iframeImages = iframe.contentDocument.querySelectorAll('img');
                console.log(`[LinkedIn] Found ${iframeImages.length} images in iframe`);

                iframeImages.forEach((img, index) => {
                  if (img instanceof HTMLImageElement && img.src) {
                    console.log(`[LinkedIn] Iframe image ${index + 1}: ${img.src.substring(0, 80)}...`);
                    if (!uniqueUrls.has(img.src)) {
                      uniqueUrls.add(img.src);
                      images.push({
                        type: 'image',
                        url: img.src,
                        alt: img.alt || 'LinkedIn carousel image',
                        width: img.width || undefined,
                        height: img.height || undefined
                      });
                      foundImagesWithSelector = true;
                    }
                  }
                });
              }
            } catch (error) {
              console.log(`[LinkedIn] Cannot access iframe content (cross-origin): ${error.message}`);

              // Check if this is a LinkedIn document iframe (cross-origin protected)
              if (iframe.src && iframe.src.includes('media.licdn.com') && iframe.src.includes('document')) {
                console.log('[LinkedIn] 📄 Found LinkedIn document carousel iframe');
                console.log('[LinkedIn] ⚠️  Document carousel images cannot be extracted due to cross-origin security');
                console.log('[LinkedIn] 🚫 Skipping this post - document carousels are meaningless without images');

                // Return failure to skip saving this post entirely
                return {
                  success: false,
                  error: 'Document carousel detected - skipping post as images cannot be extracted'
                };
              }
            }
          }

          // Check for LinkedIn document carousel structure in post
          let documentCarousel = source.querySelector('.ssplayer-wrapper, .ssplayer-carousel');

          // If not found in post, check the entire document (carousel might be in a separate container)
          if (!documentCarousel) {
            console.log('[LinkedIn] No carousel found in post, checking entire document...');
            documentCarousel = document.querySelector('.ssplayer-wrapper, .ssplayer-carousel');

            // If still not found, check for any carousel-related elements
            if (!documentCarousel) {
              const carouselElements = document.querySelectorAll('[class*="carousel"], [class*="ssplayer"], [class*="slide"]');
              console.log(`[LinkedIn] Found ${carouselElements.length} carousel-related elements in document`);

              carouselElements.forEach((el, index) => {
                console.log(`[LinkedIn] Carousel element ${index + 1}: ${el.className}`);
              });

              // Check if there are any elements with carousel-lazy-element class
              let lazyElements = document.querySelectorAll('.carousel-lazy-element');
              console.log(`[LinkedIn] Found ${lazyElements.length} carousel-lazy-element in document`);

              // If carousel elements found, extract them immediately
              if (lazyElements.length > 0) {
                console.log(`[LinkedIn] ✅ Found ${lazyElements.length} carousel images already loaded!`);

                lazyElements.forEach((img, index) => {
                  if (img instanceof HTMLImageElement) {
                    // Get image URL from src or data-src
                    const imageUrl = img.src || img.getAttribute('data-src') || img.getAttribute('data-delayed-url');

                    if (imageUrl && imageUrl.includes('media.licdn.com') && !uniqueUrls.has(imageUrl)) {
                      uniqueUrls.add(imageUrl);
                      images.push({
                        type: 'image',
                        url: imageUrl,
                        alt: img.alt || `LinkedIn document page ${index + 1}`,
                        width: img.naturalWidth || img.width || undefined,
                        height: img.naturalHeight || img.height || undefined
                      });
                      foundImagesWithSelector = true;
                      console.log(`[LinkedIn] ✅ Added carousel image ${index + 1}: ${imageUrl.substring(0, 80)}...`);
                    }
                  }
                });

                // If we found carousel images, we can return early
                if (images.length > 0) {
                  console.log(`[LinkedIn] 🎉 Successfully extracted ${images.length} carousel images!`);
                  documentCarousel = lazyElements[0].closest('.ssplayer-wrapper, .ssplayer-carousel') ||
                                   lazyElements[0].closest('[class*="carousel"]') ||
                                   lazyElements[0].parentElement?.parentElement;
                }
              } else {
                // No carousel elements found - check if this might be a document post
                const hasIframe = source.querySelector('iframe');
                const hasDocumentIndicators = source.innerHTML.includes('document') ||
                                            source.innerHTML.includes('PDF') ||
                                            source.innerHTML.includes('slides');

                if (hasIframe || hasDocumentIndicators) {
                  console.log('[LinkedIn] 📄 This appears to be a document post without accessible images');
                  console.log('[LinkedIn] 🚫 Skipping this post - document posts are meaningless without images');

                  // Return failure to skip saving this post entirely
                  return {
                    success: false,
                    error: 'Document post detected - skipping as images cannot be extracted'
                  };
                } else {
                  console.log('[LinkedIn] No carousel elements found and no document indicators');
                }
              }

              if (lazyElements.length > 0) {
                // Use the parent container of the first lazy element
                documentCarousel = lazyElements[0].closest('.ssplayer-wrapper, .ssplayer-carousel') ||
                                 lazyElements[0].closest('[class*="carousel"]') ||
                                 lazyElements[0].parentElement?.parentElement;
                console.log('[LinkedIn] Using parent container of carousel-lazy-element');
              } else {
                // Try to find and trigger document preview to load carousel
                console.log('[LinkedIn] No carousel loaded, looking for document preview to trigger...');

                // Check if this post contains a LinkedIn document carousel (which we can't extract)
                // Be more specific to avoid false positives
                const slideshareIframe = source.querySelector('iframe[src*="slideshare"]');
                const documentIframe = source.querySelector('iframe[src*="feedshare-document"]');
                const ssplayerWrapper = source.querySelector('.ssplayer-wrapper');
                const hasDocumentHTML = source.innerHTML.includes('feedshare-document') && source.innerHTML.includes('ssplayer');

                console.log(`[LinkedIn] Document carousel check: slideshare=${!!slideshareIframe}, document=${!!documentIframe}, ssplayer=${!!ssplayerWrapper}, html=${hasDocumentHTML}`);

                const hasDocumentCarousel = slideshareIframe || documentIframe || ssplayerWrapper || hasDocumentHTML;

                if (hasDocumentCarousel) {
                  console.log('[LinkedIn] ⚠️  Document carousel detected - these cannot be automatically extracted due to LinkedIn security restrictions');
                  console.log('[LinkedIn] 🚫 Skipping this post - document carousels are meaningless without images');

                  // Return failure to skip saving this post entirely
                  return {
                    success: false,
                    error: 'Document carousel detected - skipping as images cannot be extracted'
                  };
                }

                // Don't try to trigger document carousels for regular posts
                // The document carousel detection above already handles actual carousels
                console.log('[LinkedIn] This is a regular post, continuing with normal image extraction...');


              }
            }
          }

          if (documentCarousel) {
            console.log('[LinkedIn] Found document carousel structure, checking for accessible images...');

            // Try to find data attributes with image URLs
            const elementsWithData = documentCarousel.querySelectorAll('*');
            for (const el of elementsWithData) {
              for (const attr of el.attributes) {
                if (attr.name === 'data-src' && attr.value.includes('media.licdn.com')) {
                  console.log(`[LinkedIn] Found data-src image: ${attr.value.substring(0, 80)}...`);
                  if (!uniqueUrls.has(attr.value)) {
                    uniqueUrls.add(attr.value);
                    images.push({
                      type: 'image',
                      url: attr.value,
                      alt: 'LinkedIn document page',
                      width: undefined,
                      height: undefined
                    });
                    foundImagesWithSelector = true;
                  }
                }
              }
            }

            // Also check the innerHTML for any image URLs
            const htmlContent = documentCarousel.innerHTML;
            const imageUrlMatches = htmlContent.match(/https:\/\/media\.licdn\.com\/dms\/image\/[^"'\s]+/g);
            if (imageUrlMatches) {
              console.log(`[LinkedIn] Found ${imageUrlMatches.length} image URLs in carousel HTML`);
              imageUrlMatches.forEach((url, index) => {
                if (!uniqueUrls.has(url)) {
                  uniqueUrls.add(url);
                  images.push({
                    type: 'image',
                    url: url,
                    alt: `LinkedIn document page ${index + 1}`,
                    width: undefined,
                    height: undefined
                  });
                  foundImagesWithSelector = true;
                  console.log(`[LinkedIn] Added from HTML: ${url.substring(0, 80)}...`);
                }
              });
            }
          }

          // Look for all images in the post and log them for debugging
          const debugImages = source.querySelectorAll('img');
          console.log(`[LinkedIn] Found ${debugImages.length} total images in post for debugging:`);

          debugImages.forEach((img, index) => {
            if (img instanceof HTMLImageElement) {
              console.log(`[LinkedIn] Debug image ${index + 1}: ${img.src.substring(0, 80)}... (${img.width}x${img.height}) alt: "${img.alt}"`);

              // Try to add images that look like content images
              if (img.src &&
                  img.src.includes('media.licdn.com') &&
                  !img.src.includes('profile-image') &&
                  !img.src.includes('icon') &&
                  !img.alt?.toLowerCase().includes('profile') &&
                  (img.width > 200 || img.height > 200)) {

                if (!uniqueUrls.has(img.src)) {
                  uniqueUrls.add(img.src);
                  images.push({
                    type: 'image',
                    url: img.src,
                    alt: img.alt || 'LinkedIn image',
                    width: img.width || undefined,
                    height: img.height || undefined
                  });
                  foundImagesWithSelector = true;
                  console.log(`[LinkedIn] Added from debug search: ${img.src.substring(0, 80)}...`);
                }
              }
            }
          });
        }

        // If we found images, log the count
        if (foundImagesWithSelector) {
          console.log(`[LinkedIn] Successfully extracted ${images.length} images from post`);
        }

        // Extract video thumbnails with multiple selector fallbacks
        const videoSelectors = [
          '.update-components-video',
          '.feed-shared-video',
          '.feed-shared-linkedin-video',
          '.feed-shared-external-video'
        ];

        for (const selector of videoSelectors) {
          const videoElements = source.querySelectorAll(selector);
          console.log(`[LinkedIn] Found ${videoElements.length} videos with selector: ${selector}`);

          videoElements.forEach((video) => {
            const thumbnail = video.querySelector('img');
            if (thumbnail instanceof HTMLImageElement && thumbnail.src) {
              // Skip if we already have this URL
              if (!uniqueUrls.has(thumbnail.src)) {
                uniqueUrls.add(thumbnail.src);
                images.push({
                  type: 'image',
                  url: thumbnail.src,
                  alt: thumbnail.alt || 'LinkedIn video thumbnail',
                  width: thumbnail.width || undefined,
                  height: thumbnail.height || undefined
                });
              }
            }
          });

          // If we found video thumbnails with this selector, no need to try others
          if (images.length > 0) {
            break;
          }
        }
      } else {
        // Extract images from post data
        if (source.media) {
          images.push(...source.media);
        }
      }

      console.log(`[LinkedIn] Image extraction completed, found ${images.length} images`);
      return {
        success: true,
        images
      };
    } catch (error) {
      console.error('[LinkedIn] Error extracting images:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Save post data to local storage
   * @param post The post data to save
   * @returns Promise<boolean> indicating success or failure
   */
  async saveToLocal(post: Post): Promise<boolean> {
    try {
      await savePost(post);
      return true;
    } catch (error) {
      console.error('Error saving LinkedIn post to local storage:', error);
      return false;
    }
  }

  /**
   * Upload post data to cloud storage
   * @param post The post data to upload
   * @returns Promise<boolean> indicating success or failure
   */
  async uploadToCloud(post: Post): Promise<boolean> {
    try {
      const { syncToCloud } = await import('../../services/cloudSyncService');
      const result = await syncToCloud(post);
      return result.success;
    } catch (error) {
      console.error('Error uploading LinkedIn post to cloud:', error);
      return false;
    }
  }
}
