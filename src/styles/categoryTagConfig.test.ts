/**
 * Tests for Category Tag Configuration
 * Verifies that the styling configuration meets requirements 3.1, 3.2, and 3.3
 */

import test from 'node:test';
import test from 'node:test';
import { describe } from 'node:test';
import test from 'node:test';
import test from 'node:test';
import { describe } from 'node:test';
import test from 'node:test';
import test from 'node:test';
import test from 'node:test';
import { describe } from 'node:test';
import test from 'node:test';
import test from 'node:test';
import test from 'node:test';
import { describe } from 'node:test';
import { describe } from 'node:test';
import { 
  categoryTagConfig, 
  getCategoryTagStyles, 
  getCountBadgeStyles,
  categoryTagVariants 
} from './categoryTagConfig';

describe('Category Tag Configuration', () => {
  describe('Base Configuration', () => {
    test('should have consistent base styles with required padding and sizing', () => {
      const baseStyles = categoryTagConfig.baseStyles;
      
      // Requirement 3.1: Consistent padding (px-3 py-1.5)
      expect(baseStyles).toContain('px-3');
      expect(baseStyles).toContain('py-1.5');
      
      // Requirement 3.2: Uniform border radius (rounded-full)
      expect(baseStyles).toContain('rounded-full');
      
      // Requirement 3.3: Consistent font size (text-sm)
      expect(baseStyles).toContain('text-sm');
      
      // Should include transition effects
      expect(baseStyles).toContain('transition-all');
      expect(baseStyles).toContain('duration-200');
    });

    test('should have dark mode styles for both selected and unselected states', () => {
      expect(categoryTagConfig.dark.selected).toBeDefined();
      expect(categoryTagConfig.dark.unselected).toBeDefined();
      expect(categoryTagConfig.dark.countBadge.selected).toBeDefined();
      expect(categoryTagConfig.dark.countBadge.unselected).toBeDefined();
    });

    test('should have light mode styles for both selected and unselected states', () => {
      expect(categoryTagConfig.light.selected).toBeDefined();
      expect(categoryTagConfig.light.unselected).toBeDefined();
      expect(categoryTagConfig.light.countBadge.selected).toBeDefined();
      expect(categoryTagConfig.light.countBadge.unselected).toBeDefined();
    });
  });

  describe('Style Generation Functions', () => {
    test('getCategoryTagStyles should return complete style strings', () => {
      // Test dark mode selected
      const darkSelected = getCategoryTagStyles(true, true);
      expect(darkSelected).toContain('px-3 py-1.5');
      expect(darkSelected).toContain('rounded-full');
      expect(darkSelected).toContain('text-sm');
      expect(darkSelected).toContain('bg-gradient-to-r');

      // Test light mode unselected
      const lightUnselected = getCategoryTagStyles(false, false);
      expect(lightUnselected).toContain('px-3 py-1.5');
      expect(lightUnselected).toContain('rounded-full');
      expect(lightUnselected).toContain('text-sm');
      expect(lightUnselected).toContain('bg-white');
    });

    test('getCountBadgeStyles should return appropriate badge styles', () => {
      // Test dark mode selected badge - enhanced contrast
      const darkSelectedBadge = getCountBadgeStyles(true, true);
      expect(darkSelectedBadge).toContain('ml-2'); // Updated spacing
      expect(darkSelectedBadge).toContain('text-xs');
      expect(darkSelectedBadge).toContain('rounded-md');
      expect(darkSelectedBadge).toContain('px-2'); // Updated padding
      expect(darkSelectedBadge).toContain('bg-black/30'); // Enhanced contrast
      expect(darkSelectedBadge).toContain('text-white');
      expect(darkSelectedBadge).toContain('shadow-sm');

      // Test dark mode unselected badge - improved contrast
      const darkUnselectedBadge = getCountBadgeStyles(false, true);
      expect(darkUnselectedBadge).toContain('bg-zinc-700/80');
      expect(darkUnselectedBadge).toContain('text-zinc-300');
      expect(darkUnselectedBadge).toContain('hover:bg-zinc-600/80');

      // Test light mode selected badge - stronger contrast
      const lightSelectedBadge = getCountBadgeStyles(true, false);
      expect(lightSelectedBadge).toContain('bg-zinc-400/90');
      expect(lightSelectedBadge).toContain('text-zinc-800');
      expect(lightSelectedBadge).toContain('font-semibold');

      // Test light mode unselected badge - improved readability
      const lightUnselectedBadge = getCountBadgeStyles(false, false);
      expect(lightUnselectedBadge).toContain('bg-zinc-200/90');
      expect(lightUnselectedBadge).toContain('text-zinc-700');
      expect(lightUnselectedBadge).toContain('hover:bg-zinc-300/90');
    });

    test('should support different variants', () => {
      const compactStyles = getCategoryTagStyles(true, true, 'compact');
      expect(compactStyles).toContain('text-xs');
      expect(compactStyles).toContain('px-2 py-1');

      const largeStyles = getCategoryTagStyles(true, true, 'large');
      expect(largeStyles).toContain('text-base');
      expect(largeStyles).toContain('px-4 py-2');
    });
  });

  describe('Accessibility Features', () => {
    test('should include focus ring styles', () => {
      const baseStyles = categoryTagConfig.baseStyles;
      expect(baseStyles).toContain('focus:ring-2');
      expect(baseStyles).toContain('focus:ring-purple-500/50');
      expect(baseStyles).toContain('focus:ring-offset-2');
    });

    test('should have proper focus ring offset for different themes', () => {
      expect(categoryTagConfig.dark.selected).toContain('focus:ring-offset-gray-900');
      expect(categoryTagConfig.light.selected).toContain('focus:ring-offset-white');
    });
  });

  describe('Contrast and Readability', () => {
    test('dark mode should use high contrast colors', () => {
      // Dark mode unselected should use zinc-300 text for better contrast
      expect(categoryTagConfig.dark.unselected).toContain('text-zinc-300');
      expect(categoryTagConfig.dark.unselected).toContain('bg-zinc-800');
    });

    test('light mode should use high contrast colors', () => {
      // Light mode selected should use zinc-800 text on zinc-200 background
      expect(categoryTagConfig.light.selected).toContain('text-zinc-800');
      expect(categoryTagConfig.light.selected).toContain('bg-zinc-200');
    });
  });
});