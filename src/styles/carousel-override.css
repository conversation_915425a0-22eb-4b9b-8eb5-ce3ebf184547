/* Force vertical centering for carousel images */
.embla__container {
  height: 100% !important;
}

.embla__slide {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure ImageSwiper container fills height */
.image-swiper-container {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Force carousel content to center */
[data-carousel-content] {
  height: 100% !important;
}

[data-carousel-item] {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Specific targeting for our carousel component */
.carousel-image-container {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.carousel-image-container img {
  max-height: 100% !important;
  max-width: 100% !important;
  object-fit: contain !important;
}

/* Enhanced carousel dots styling */
.carousel-dots-container {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 6px !important;
}

/* Pill-shaped dots for multi-image carousels */
.carousel-dot.w-5 {
  transition: all 0.2s ease-in-out !important;
}

.carousel-dot.w-5:hover {
  background-color: rgba(255, 255, 255, 0.8) !important;
}

.carousel-dot.w-5.bg-accent {
  width: 20px !important; /* Make active dot wider */
}

/* Focus styles for accessibility */
.carousel-dot:focus-visible {
  outline: 2px solid white !important;
  outline-offset: 2px !important;
}