@tailwind base;
@tailwind components;
@tailwind utilities;

@import './notely-theme.css';
@import './ui-improvements.css';

:root {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  /* Theme colors are handled by notely-theme.css */
}

#root {
  @apply w-full h-full;
}

/* Hide scrollbars while maintaining scroll functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Line clamp utilities for consistent text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced card animations and states */
.notely-card-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.notely-card-enhanced:hover {
  transform: translateY(-2px);
}

/* Icon alignment helpers */
.icon-aligned {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Typography scale consistency */
.text-hierarchy-h1 {
  @apply text-xl font-bold leading-tight;
}

.text-hierarchy-h2 {
  @apply text-base font-semibold leading-tight;
}

.text-hierarchy-body {
  @apply text-sm font-normal leading-relaxed;
}

.text-hierarchy-caption {
  @apply text-xs font-medium leading-normal;
}