// Category tag styling configuration for Notely

import { Theme } from '../utils/themeUtils';

export const getCategoryTagStyles = (isSelected: boolean, isDarkMode: boolean = false): string => {
  // Base styles for all category tags - matching the beautiful design from the example
  const baseStyles = [
    'relative',
    'inline-flex',
    'items-center',
    'justify-center',
    'px-3',
    'py-1.5',
    'rounded-full',
    'text-xs',
    'font-medium',
    'whitespace-nowrap',
    'transition-colors',
    'duration-200',
    'ease-in-out',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'focus:ring-purple-500',
    'm-0.5',
    'flex-shrink-0',
    'max-w-[120px]',
    'sm:max-w-[140px]',
    'md:max-w-[160px]',
    'lg:max-w-[180px]',
    'xl:max-w-none',
  ];

  // Selected state styles - matching the exact example provided
  if (isSelected) {
    return [
      ...baseStyles,
      isDarkMode
        ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white border border-transparent shadow-lg hover:from-purple-700 hover:to-indigo-700 focus:ring-purple-400'
        : 'bg-purple-100 text-purple-800 border border-purple-200 shadow-sm hover:bg-purple-50 hover:border-purple-300 focus:ring-purple-500',
    ].join(' ');
  }

  // Default (unselected) state styles - clean white/dark styling matching the example
  return [
    ...baseStyles,
    isDarkMode
      ? 'bg-gray-800/90 text-gray-200 border border-gray-700 hover:bg-gray-700/90 hover:text-gray-100 hover:border-gray-600 focus:ring-gray-500 backdrop-blur-sm'
      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gray-900 focus:ring-gray-400 shadow-sm hover:shadow-md',
  ].join(' ');
};

export const getCountBadgeStyles = (isSelected: boolean, isDarkMode: boolean = false): string => {
  const baseStyles = [
    'ml-1.5',
    'inline-flex',
    'items-center',
    'justify-center',
    'px-1.5',
    'py-0.5',
    'rounded-full',
    'text-[10px]',
    'font-medium',
    'min-w-[18px]',
    'h-[18px]',
    'leading-none',
  ];

  // Selected state - white badge with semi-transparent background
  if (isSelected) {
    return [
      ...baseStyles,
      isDarkMode
        ? 'bg-white/20 text-white/90'
        : 'bg-white/30 text-white',
    ].join(' ');
  }

  // Default state - gray badge matching the example
  return [
    ...baseStyles,
    isDarkMode
      ? 'bg-gray-700 text-gray-300'
      : 'bg-gray-100 text-gray-600',
  ].join(' ');
};

// Tag styles - minimal and consistent design
export const getTagStyles = (isSelected: boolean, isDarkMode: boolean = false): string => {
  // Base styles for all tags - compact and minimal
  const baseStyles = [
    'relative',
    'inline-flex',
    'items-center',
    'justify-center',
    'px-2',
    'py-1',
    'rounded',
    'text-xs',
    'font-medium',
    'whitespace-nowrap',
    'transition-colors',
    'duration-200',
    'ease-in-out',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-1',
    'focus:ring-notely-lavender/30',
    'm-0.5',
    'flex-shrink-0',
  ];

  // Selected state styles - subtle accent
  if (isSelected) {
    return [
      ...baseStyles,
      isDarkMode
        ? 'bg-notely-lavender/20 text-notely-lavender border border-notely-lavender/30 hover:bg-notely-lavender/30'
        : 'bg-notely-lavender/10 text-notely-lavender border border-notely-lavender/20 hover:bg-notely-lavender/20',
    ].join(' ');
  }

  // Default (unselected) state styles - neutral surface
  return [
    ...baseStyles,
    isDarkMode
      ? 'bg-notely-surface/60 text-notely-text-secondary border border-notely-border-dark/20 hover:bg-notely-surface/80 hover:text-notely-text-primary hover:border-notely-border-dark/30'
      : 'bg-notely-surface text-notely-text-secondary border border-notely-border/10 hover:bg-notely-surface/80 hover:text-notely-text-primary hover:border-notely-border/20',
  ].join(' ');
};
