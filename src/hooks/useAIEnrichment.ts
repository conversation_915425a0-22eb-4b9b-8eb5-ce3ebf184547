import { useState, useCallback } from 'react';
import { generateAICategories, generateAISnapNote } from '../services/aiService';
import { CORE_CATEGORIES } from '../config/constants';
import type { CoreSubCategorySlug } from '../config/constants';
import { generateSecureAITags, generateSecureAIFastTake, generateSecureAIInsight } from '../services/secureQuotaService';
import { canMakeAICall } from '../services/usageTrackingService';

export interface AIEnrichmentResult {
  aiCategory: string | null;
  aiTags: string[];
  userCategory: string;
  userTags: string[];
  snapNote?: string | null;
  insight?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    emoji: string;
    contextTags: string[];
  } | null;
  fastTake?: string | null;
}

export interface AIEnrichmentState {
  isLoading: boolean;
  error: string | null;
  result: AIEnrichmentResult | null;
  canUseAI: boolean;
  usageInfo: {
    canCall: boolean;
    reason?: string;
    remainingCalls?: number;
  } | null;
}

/**
 * Hook for managing AI enrichment of posts
 */
export const useAIEnrichment = (userPlan: 'free' | 'premium' | null) => {
  const [state, setState] = useState<AIEnrichmentState>({
    isLoading: false,
    error: null,
    result: null,
    canUseAI: false,
    usageInfo: null
  });

  /**
   * Check if user can use AI features
   */
  const checkAIAvailability = useCallback(async () => {
    const usageInfo = await canMakeAICall(userPlan);
    setState(prev => ({
      ...prev,
      canUseAI: usageInfo.canCall,
      usageInfo
    }));
    return usageInfo;
  }, [userPlan]);

  /**
   * Find matching category from predefined categories
   */
  const findMatchingCategory = (aiSuggestedCategory: string): string | null => {
    const allCategories = Object.values(CORE_CATEGORIES).flat();

    // Exact match first
    const exactMatch = allCategories.find(cat =>
      cat.toLowerCase() === aiSuggestedCategory.toLowerCase()
    );
    if (exactMatch) return exactMatch;

    // Fuzzy match - check if AI suggestion contains or is contained in any category
    const fuzzyMatch = allCategories.find(cat => {
      const catLower = cat.toLowerCase().replace(/_/g, ' ');
      const aiLower = aiSuggestedCategory.toLowerCase().replace(/_/g, ' ');
      return catLower.includes(aiLower) || aiLower.includes(catLower);
    });

    return fuzzyMatch || null;
  };

  // Use the secure quota service for tag generation

  /**
   * Enrich post content with AI
   */
  const enrichPost = useCallback(async (postContent: string, imageUrl?: string): Promise<AIEnrichmentResult | null> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Note: Server-side quota enforcement is now handled by the secure API endpoints
      // Client-side checks have been removed for security

      // Make AI calls in parallel for better performance
      // Note: Using secure server-side API for all AI features (SECURE)
      const [categoriesResult, tagsResult, insightResult, fastTakeResult, snapNoteResult] = await Promise.allSettled([
        generateAICategories(postContent),
        generateSecureAITags(postContent, 'twitter'), // Use secure server-side API
        generateSecureAIInsight(postContent), // Use secure server-side API
        generateSecureAIFastTake(postContent), // Use secure server-side API
        generateAISnapNote(postContent, imageUrl)
      ]);

      // Process categories
      let aiCategory: string | null = null;
      if (categoriesResult.status === 'fulfilled' && categoriesResult.value.length > 0) {
        const suggestedCategory = categoriesResult.value[0];
        aiCategory = findMatchingCategory(suggestedCategory) || suggestedCategory;
      }

      // Process tags (from secure server-side API)
      let aiTags: string[] = [];
      if (tagsResult.status === 'fulfilled') {
        // The secure API returns an object with success, tags, and quota info
        const tagResponse = tagsResult.value;
        if (tagResponse && tagResponse.success) {
          aiTags = tagResponse.tags || [];
        } else if (tagResponse && tagResponse.error) {
          console.warn('Auto-tagging failed:', tagResponse.error);
          // Could show quota info to user here if needed
        }
      }

      // Process insight (from secure server-side API)
      let insight: { sentiment: 'positive' | 'neutral' | 'negative'; emoji: string; contextTags: string[] } | null = null;
      if (insightResult.status === 'fulfilled') {
        const insightResponse = insightResult.value;
        if (insightResponse && insightResponse.success) {
          insight = insightResponse.insight;
        } else if (insightResponse && insightResponse.error) {
          console.warn('Insight generation failed:', insightResponse.error);
        }
      }

      // Process fast take (from secure server-side API)
      let fastTake: string | null = null;
      if (fastTakeResult.status === 'fulfilled') {
        const fastTakeResponse = fastTakeResult.value;
        if (fastTakeResponse && fastTakeResponse.success) {
          fastTake = fastTakeResponse.fastTake || null;
        } else if (fastTakeResponse && fastTakeResponse.error) {
          console.warn('FastTake generation failed:', fastTakeResponse.error);
        }
      }

      // Process snap note
      const snapNote = snapNoteResult.status === 'fulfilled' ? snapNoteResult.value : null;

      const result: AIEnrichmentResult = {
        aiCategory,
        aiTags,
        userCategory: aiCategory || '', // Initialize with AI suggestion
        userTags: [...aiTags], // Initialize with AI suggestions
        snapNote,
        insight,
        fastTake
      };

      setState(prev => ({
        ...prev,
        isLoading: false,
        result,
        usageInfo: null // Server-side quota info is handled by the secure API
      }));

      return result;

    } catch (error) {
      console.error('AI enrichment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'AI enrichment failed';

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));

      return null;
    }
  }, [userPlan]);

  /**
   * Update user-edited category
   */
  const updateUserCategory = useCallback((category: string) => {
    setState(prev => ({
      ...prev,
      result: prev.result ? {
        ...prev.result,
        userCategory: category
      } : null
    }));
  }, []);

  /**
   * Update user-edited tags
   */
  const updateUserTags = useCallback((tags: string[]) => {
    setState(prev => ({
      ...prev,
      result: prev.result ? {
        ...prev.result,
        userTags: tags
      } : null
    }));
  }, []);

  /**
   * Add a user tag
   */
  const addUserTag = useCallback((tag: string) => {
    setState(prev => ({
      ...prev,
      result: prev.result ? {
        ...prev.result,
        userTags: [...new Set([...prev.result.userTags, tag])]
      } : null
    }));
  }, []);

  /**
   * Remove a user tag
   */
  const removeUserTag = useCallback((tag: string) => {
    setState(prev => ({
      ...prev,
      result: prev.result ? {
        ...prev.result,
        userTags: prev.result.userTags.filter(t => t !== tag)
      } : null
    }));
  }, []);

  /**
   * Clear all AI data
   */
  const clearAIData = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      result: null,
      canUseAI: false,
      usageInfo: null
    });
  }, []);

  return {
    ...state,
    enrichPost,
    updateUserCategory,
    updateUserTags,
    addUserTag,
    removeUserTag,
    clearAIData,
    checkAIAvailability
  };
};
