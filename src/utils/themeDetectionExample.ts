// Example usage of theme detection utility
// This demonstrates how the theme detection functions work in practice

import { detectCurrentTheme, detectCurrentThemeAsync, isDarkMode, isLightMode } from './themeUtils';

/**
 * Example: Using theme detection in a component
 */
export const getThemeAwareStyles = () => {
  const currentTheme = detectCurrentTheme();
  
  console.log('Current theme detected:', currentTheme);
  console.log('Is dark mode:', isDarkMode());
  console.log('Is light mode:', isLightMode());
  
  return {
    theme: currentTheme,
    isDark: isDarkMode(),
    isLight: isLightMode()
  };
};

/**
 * Example: Async theme detection for extension contexts
 */
export const getThemeAwareStylesAsync = async () => {
  const currentTheme = await detectCurrentThemeAsync();
  
  console.log('Current theme detected (async):', currentTheme);
  
  return {
    theme: currentTheme,
    isDark: currentTheme === 'dark',
    isLight: currentTheme === 'light'
  };
};

/**
 * Example: Theme detection with fallback scenarios
 */
export const demonstrateThemeDetection = () => {
  console.log('=== Theme Detection Demonstration ===');
  
  // Scenario 1: Clean environment
  console.log('\n1. Clean environment (should default to dark):');
  console.log('Theme:', detectCurrentTheme());
  
  // Scenario 2: Document element has dark class
  document.documentElement.classList.add('dark');
  console.log('\n2. Document element with "dark" class:');
  console.log('Theme:', detectCurrentTheme());
  
  // Scenario 3: Override with light theme on body
  document.body.classList.add('light-theme');
  console.log('\n3. Body with "light-theme" class (document still has dark):');
  console.log('Theme:', detectCurrentTheme()); // Should still be dark (higher priority)
  
  // Scenario 4: Remove document class, body class should take effect
  document.documentElement.classList.remove('dark');
  console.log('\n4. Removed document dark class, body has light-theme:');
  console.log('Theme:', detectCurrentTheme()); // Should be light now
  
  // Scenario 5: CSS custom property
  document.documentElement.style.setProperty('--theme-mode', 'dark');
  document.body.classList.remove('light-theme');
  console.log('\n5. CSS custom property set to dark:');
  console.log('Theme:', detectCurrentTheme());
  
  // Scenario 6: Data attribute
  document.documentElement.style.removeProperty('--theme-mode');
  document.documentElement.setAttribute('data-theme', 'light');
  console.log('\n6. Data attribute set to light:');
  console.log('Theme:', detectCurrentTheme());
  
  // Clean up
  document.documentElement.removeAttribute('data-theme');
  
  console.log('\n=== Theme Detection Complete ===');
};

/**
 * Test all fallback scenarios
 */
export const testFallbackScenarios = () => {
  console.log('=== Testing Fallback Scenarios ===');
  
  // Store original values
  const originalMatchMedia = window.matchMedia;
  
  try {
    // Test system preference fallback
    (window as any).matchMedia = (query: string) => ({
      matches: query.includes('prefers-color-scheme: dark'),
      media: query
    });
    
    console.log('\n1. System prefers dark mode:');
    console.log('Theme:', detectCurrentTheme());
    
    // Test system preference for light
    (window as any).matchMedia = (query: string) => ({
      matches: query.includes('prefers-color-scheme: light'),
      media: query
    });
    
    console.log('\n2. System prefers light mode:');
    console.log('Theme:', detectCurrentTheme());
    
    // Test no matchMedia support
    (window as any).matchMedia = undefined;
    console.log('\n3. No matchMedia support (should default to dark):');
    console.log('Theme:', detectCurrentTheme());
    
  } finally {
    // Restore original matchMedia
    window.matchMedia = originalMatchMedia;
  }
  
  console.log('\n=== Fallback Testing Complete ===');
};