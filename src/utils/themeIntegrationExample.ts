// Example of how theme detection integrates with SimpleCategorySelector
// This demonstrates the practical usage of the theme detection utility

import { detectCurrentTheme, isDarkMode, isLightMode } from './themeUtils';

/**
 * Enhanced button styles function that uses theme detection
 * This is how the SimpleCategorySelector would use the theme detection utility
 */
export const getEnhancedButtonStyles = (isSelected: boolean) => {
  const currentTheme = detectCurrentTheme();
  const baseStyles = 'text-sm font-medium rounded-full px-3 py-1.5 transition-all duration-200 focus:outline-none relative hover:scale-105';

  // Theme-aware styling configuration
  const styleConfig = {
    dark: {
      selected: 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg hover:shadow-purple-500/25 hover:from-purple-600 hover:to-indigo-600',
      unselected: 'bg-zinc-800/60 text-zinc-300 border border-zinc-700/50 hover:bg-zinc-700/60 hover:text-zinc-200 hover:border-zinc-600/50'
    },
    light: {
      selected: 'bg-zinc-200 text-zinc-800 border border-zinc-300 shadow-sm hover:bg-zinc-300 hover:shadow-md',
      unselected: 'bg-white text-zinc-600 border border-zinc-200 hover:bg-zinc-50 hover:text-zinc-800 hover:border-zinc-300'
    }
  };

  const themeStyles = styleConfig[currentTheme];
  const stateStyles = isSelected ? themeStyles.selected : themeStyles.unselected;

  return `${baseStyles} ${stateStyles}`;
};

/**
 * Enhanced count badge styles that use theme detection
 */
export const getEnhancedCountBadgeStyles = (isSelected: boolean) => {
  const currentTheme = detectCurrentTheme();
  
  const badgeConfig = {
    dark: {
      selected: 'bg-black/20 text-white/90',
      unselected: 'bg-zinc-700/60 text-zinc-300'
    },
    light: {
      selected: 'bg-zinc-300/80 text-zinc-700',
      unselected: 'bg-zinc-100 text-zinc-600'
    }
  };

  const themeBadges = badgeConfig[currentTheme];
  const stateBadge = isSelected ? themeBadges.selected : themeBadges.unselected;

  return `ml-1.5 text-xs font-medium rounded-md px-1.5 py-0.5 ${stateBadge}`;
};

/**
 * Demonstration of theme detection in action
 */
export const demonstrateThemeIntegration = () => {
  console.log('=== Theme Integration Demonstration ===');
  
  // Test different theme scenarios
  const scenarios = [
    { name: 'Default (should be dark)', setup: () => {} },
    { 
      name: 'Dark mode via document class', 
      setup: () => document.documentElement.classList.add('dark') 
    },
    { 
      name: 'Light mode via body class', 
      setup: () => {
        document.documentElement.classList.remove('dark');
        document.body.classList.add('light-theme');
      }
    }
  ];

  scenarios.forEach((scenario, index) => {
    console.log(`\n${index + 1}. ${scenario.name}:`);
    scenario.setup();
    
    const theme = detectCurrentTheme();
    const selectedStyles = getEnhancedButtonStyles(true);
    const unselectedStyles = getEnhancedButtonStyles(false);
    const selectedBadge = getEnhancedCountBadgeStyles(true);
    const unselectedBadge = getEnhancedCountBadgeStyles(false);
    
    console.log(`   Theme: ${theme}`);
    console.log(`   isDarkMode: ${isDarkMode()}`);
    console.log(`   isLightMode: ${isLightMode()}`);
    console.log(`   Selected button classes: ${selectedStyles.substring(0, 100)}...`);
    console.log(`   Unselected button classes: ${unselectedStyles.substring(0, 100)}...`);
    console.log(`   Selected badge classes: ${selectedBadge}`);
    console.log(`   Unselected badge classes: ${unselectedBadge}`);
  });

  // Clean up
  document.documentElement.classList.remove('dark');
  document.body.classList.remove('light-theme');
  
  console.log('\n=== Integration Demonstration Complete ===');
};

/**
 * Validate theme detection reliability
 */
export const validateThemeDetection = () => {
  console.log('=== Theme Detection Validation ===');
  
  const tests = [
    {
      name: 'Document element dark class',
      setup: () => document.documentElement.classList.add('dark'),
      expected: 'dark',
      cleanup: () => document.documentElement.classList.remove('dark')
    },
    {
      name: 'Document element light-theme class',
      setup: () => document.documentElement.classList.add('light-theme'),
      expected: 'light',
      cleanup: () => document.documentElement.classList.remove('light-theme')
    },
    {
      name: 'Body dark-theme class',
      setup: () => document.body.classList.add('dark-theme'),
      expected: 'dark',
      cleanup: () => document.body.classList.remove('dark-theme')
    },
    {
      name: 'Body light-theme class',
      setup: () => document.body.classList.add('light-theme'),
      expected: 'light',
      cleanup: () => document.body.classList.remove('light-theme')
    },
    {
      name: 'CSS custom property dark',
      setup: () => document.documentElement.style.setProperty('--theme-mode', 'dark'),
      expected: 'dark',
      cleanup: () => document.documentElement.style.removeProperty('--theme-mode')
    },
    {
      name: 'CSS custom property light',
      setup: () => document.documentElement.style.setProperty('--theme-mode', 'light'),
      expected: 'light',
      cleanup: () => document.documentElement.style.removeProperty('--theme-mode')
    },
    {
      name: 'Data attribute dark',
      setup: () => document.documentElement.setAttribute('data-theme', 'dark'),
      expected: 'dark',
      cleanup: () => document.documentElement.removeAttribute('data-theme')
    },
    {
      name: 'Data attribute light',
      setup: () => document.documentElement.setAttribute('data-theme', 'light'),
      expected: 'light',
      cleanup: () => document.documentElement.removeAttribute('data-theme')
    }
  ];

  let passed = 0;
  let failed = 0;

  tests.forEach((test, index) => {
    console.log(`\n${index + 1}. Testing: ${test.name}`);
    
    try {
      test.setup();
      const detected = detectCurrentTheme();
      
      if (detected === test.expected) {
        console.log(`   ✅ PASS: Expected ${test.expected}, got ${detected}`);
        passed++;
      } else {
        console.log(`   ❌ FAIL: Expected ${test.expected}, got ${detected}`);
        failed++;
      }
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      failed++;
    } finally {
      test.cleanup();
    }
  });

  console.log(`\n=== Validation Results ===`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Total: ${tests.length}`);
  console.log(`Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`);
  
  return { passed, failed, total: tests.length };
};