// Theme utility functions for Notely

export type Theme = 'light' | 'dark';

// Track whether a theme has been applied to avoid unnecessary re-initialization
let themeInitialized = false;

/**
 * Try to get stored theme from localStorage
 */
const tryGetStoredTheme = (): Theme | null => {
  try {
    if (typeof localStorage !== 'undefined') {
      const stored = localStorage.getItem('theme') || localStorage.getItem('notely-theme');
      if (stored === 'dark' || stored === 'light') {
        return stored;
      }
    }
  } catch (error) {
    console.debug('localStorage read failed:', error);
  }
  return null;
};

/**
 * Detect current theme from multiple sources with comprehensive fallback logic
 * This function checks various methods to determine the current theme with priority order:
 * 1. Stored user preference (localStorage/Chrome storage) - highest priority
 * 2. Document element classes (dark, light-theme)
 * 3. Body classes (dark-theme, light-theme)
 * 4. CSS custom properties (--theme-mode)
 * 5. Data attributes (data-theme)
 * 6. System preference as fallback
 * 7. Default to dark mode (<PERSON><PERSON>'s default)
 */
export const detectCurrentTheme = (): Theme => {
  try {
    // Ensure we have access to document
    if (typeof document === 'undefined') {
      return 'dark'; // Default for server-side rendering or non-browser environments
    }

    // Method 1: Check stored user preference FIRST (highest priority)
    try {
      const storedTheme = tryGetStoredTheme();
      if (storedTheme) {
        return storedTheme;
      }
    } catch (storageError) {
      console.debug('Storage access failed:', storageError);
    }

    // Method 2: Check document element classes
    const documentElement = document.documentElement;
    if (documentElement?.classList?.contains('dark')) {
      return 'dark';
    }
    if (documentElement?.classList?.contains('light') || documentElement?.classList?.contains('light-theme')) {
      return 'light';
    }

    // Method 3: Check body classes
    const body = document.body;
    if (body?.classList?.contains('dark') || body?.classList?.contains('dark-theme')) {
      return 'dark';
    }
    if (body?.classList?.contains('light') || body?.classList?.contains('light-theme')) {
      return 'light';
    }

    // Method 4: Check for common theme indicators
    const bodyStyle = window.getComputedStyle(body);
    const backgroundColor = bodyStyle.backgroundColor;
    
    // If background is very light (white/light gray), assume light mode
    if (backgroundColor === 'rgb(255, 255, 255)' || 
        backgroundColor === 'rgba(255, 255, 255, 1)' ||
        backgroundColor.includes('248, 250, 252') || // slate-50
        backgroundColor.includes('249, 250, 251')) { // gray-50
      return 'light';
    }
    
    // If background is very dark, assume dark mode
    if (backgroundColor === 'rgb(0, 0, 0)' || 
        backgroundColor === 'rgba(0, 0, 0, 1)' ||
        backgroundColor.includes('17, 24, 39') || // gray-900
        backgroundColor.includes('15, 23, 42')) { // slate-900
      return 'dark';
    }

    // Method 5: Check CSS custom properties
    try {
      if (typeof getComputedStyle !== 'undefined' && documentElement) {
        const computedStyle = getComputedStyle(documentElement);
        const themeProperty = computedStyle.getPropertyValue('--theme-mode')?.trim();
        if (themeProperty === 'dark' || themeProperty === 'light') {
          return themeProperty as Theme;
        }
      }
    } catch (cssError) {
      // CSS access failed, continue to next method
      console.debug('CSS property access failed:', cssError);
    }

    // Method 6: Check data attributes
    try {
      const themeDataAttr = documentElement?.getAttribute('data-theme');
      if (themeDataAttr === 'dark' || themeDataAttr === 'light') {
        return themeDataAttr as Theme;
      }
    } catch (attrError) {
      // Attribute access failed, continue to next method
      console.debug('Data attribute access failed:', attrError);
    }

    // Removed system preference detection to prevent overriding user's manual selection

    // Final fallback: Notely defaults to dark mode
    return 'dark';
    
  } catch (error) {
    // Complete failure - return safe default
    console.warn('Theme detection failed completely, using default:', error);
    return 'dark';
  }
};

/**
 * Get stored user theme preference without falling back to DOM/system detection
 * This function ONLY checks stored preferences and returns null if none found
 */
export const getStoredThemePreference = (): Promise<Theme | null> => {
  return new Promise((resolve) => {
    try {
      console.log('[DEBUG] getStoredThemePreference() called');
      
      // Method 1: Check localStorage first (user preference)
      const localTheme = tryGetStoredTheme();
      console.log('[DEBUG] localStorage theme:', localTheme);
      if (localTheme) {
        console.log('[DEBUG] Found localStorage theme:', localTheme);
        resolve(localTheme);
        return;
      }

      // Method 2: Try Chrome extension storage
      if (typeof chrome !== 'undefined' && chrome?.storage?.local) {
        try {
          chrome.storage.local.get(['theme'], (result) => {
            console.log('[DEBUG] Chrome storage result:', result);
            if (chrome.runtime.lastError) {
              console.log('[DEBUG] Chrome storage error, returning null');
              resolve(null);
              return;
            }

            const storedTheme = result?.theme;
            if (storedTheme === 'dark' || storedTheme === 'light') {
              console.log('[DEBUG] Found Chrome storage theme:', storedTheme);
              resolve(storedTheme);
            } else {
              console.log('[DEBUG] No valid Chrome storage theme, returning null');
              resolve(null);
            }
          });
        } catch (chromeError) {
          console.debug('Chrome storage access failed:', chromeError);
          console.log('[DEBUG] Chrome storage exception, returning null');
          resolve(null);
        }
      } else {
        // No Chrome storage available, return null (no stored preference)
        console.log('[DEBUG] No Chrome storage available, returning null');
        resolve(null);
      }
    } catch (error) {
      console.warn('Stored theme preference check failed:', error);
      console.log('[DEBUG] Exception in getStoredThemePreference, returning null');
      resolve(null);
    }
  });
};

// Removed getSystemPreference function to prevent automatic theme detection

/**
 * Asynchronous theme detection with comprehensive fallback logic
 * This function tries multiple async sources before falling back to sync detection:
 * 1. Chrome storage (if in extension context)
 * 2. LocalStorage (web context)
 * 3. Synchronous detection as final fallback
 */
export const detectCurrentThemeAsync = (): Promise<Theme> => {
  return new Promise((resolve) => {
    try {
      // First check stored preferences without DOM fallback
      getStoredThemePreference().then((storedTheme) => {
        if (storedTheme) {
          resolve(storedTheme);
        } else {
          // No stored preference, use full detection
          const detectedTheme = detectCurrentTheme();
          resolve(detectedTheme);
        }
      }).catch((error) => {
        console.warn('Stored preference check failed:', error);
        // Fallback to sync detection
        const detectedTheme = detectCurrentTheme();
        resolve(detectedTheme);
      });
    } catch (error) {
      console.warn('Async theme detection failed:', error);
      resolve(detectCurrentTheme()); // Final fallback to sync detection
    }
  });
};

/**
 * Helper function to try localStorage for theme detection
 */
const tryLocalStorage = (fallbackTheme: Theme, resolve: (theme: Theme) => void): void => {
  try {
    if (typeof localStorage !== 'undefined') {
      const storedTheme = localStorage.getItem('theme') || localStorage.getItem('notely-theme');
      if (storedTheme === 'dark' || storedTheme === 'light') {
        resolve(storedTheme);
        return;
      }
    }
  } catch (storageError) {
    console.debug('localStorage access failed:', storageError);
  }
  
  // localStorage failed or no valid theme found, use fallback
  resolve(fallbackTheme);
};

/**
 * Check if current environment is in dark mode
 * Convenience function that returns boolean
 */
export const isDarkMode = (): boolean => {
  return detectCurrentTheme() === 'dark';
};

/**
 * Check if current environment is in light mode
 * Convenience function that returns boolean
 */
export const isLightMode = (): boolean => {
  return detectCurrentTheme() === 'light';
};

/**
 * Initialize theme on page load with fallback logic
 * Always ensures a theme is applied
 */
export const initializeTheme = (force: boolean = false): void => {
  try {
    // Avoid re-initialization unless forced
    if (!force) {
      if (themeInitialized) {
        console.log('Theme already initialized, skipping');
        return;
      }

      if (typeof document !== 'undefined') {
        const root = document.documentElement;
        if (root?.classList?.contains('dark') || root?.classList?.contains('light-theme')) {
          console.log('Theme already applied, skipping initialization');
          themeInitialized = true;
          return;
        }
      }
    }

    console.log('Initializing theme...');

    // Try Chrome storage first
    if (typeof chrome !== 'undefined' && chrome?.storage?.local) {
      chrome.storage.local.get(['theme'], (result) => {
        if (chrome.runtime.lastError) {
          // Chrome storage failed, check localStorage
          const storedTheme = tryGetStoredTheme();
          if (storedTheme) {
            console.log('Using stored theme from localStorage:', storedTheme);
            applyTheme(storedTheme);
          } else {
            // No stored preference, apply default dark theme
            console.log('No stored theme found, applying default dark theme');
            applyTheme('dark');
          }
          return;
        }
        
        const savedTheme = result?.theme;
        if (savedTheme === 'dark' || savedTheme === 'light') {
          console.log('Using stored theme from Chrome storage:', savedTheme);
          applyTheme(savedTheme);
        } else {
          // No valid stored theme, apply default dark theme
          console.log('No valid stored theme, applying default dark theme');
          applyTheme('dark');
        }
      });
    } else {
      // Not in Chrome extension, try localStorage
      const savedTheme = tryGetStoredTheme();
      if (savedTheme) {
        console.log('Using stored theme from localStorage:', savedTheme);
        applyTheme(savedTheme);
      } else {
        // No stored preference, apply default dark theme
        console.log('No stored theme found, applying default dark theme');
        applyTheme('dark');
      }
    }
  } catch (error) {
    console.warn('Theme initialization failed:', error);
    // Always apply a fallback theme
    applyTheme('dark');
  }
};

/**
 * Apply theme to the document with error handling
 */
export const applyTheme = (theme: Theme): void => {
  try {
    if (typeof document === 'undefined') {
      return; // Not in browser environment
    }

    const root = document.documentElement;
    if (!root) {
      return; // No document element available
    }

    console.log(`[DEBUG] BEFORE applyTheme(${theme}):`, {
      currentClasses: root.classList.toString(),
      computedBg: getComputedStyle(document.body).backgroundColor,
      computedColor: getComputedStyle(document.body).color
    });

    // Remove all theme classes first
    root.classList.remove('dark', 'light-theme');
    console.log(`[DEBUG] After removing classes:`, root.classList.toString());
    
    // Apply the selected theme class
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.add('light-theme');
    }
    
    // Also apply theme class to body and main app containers
    document.body.classList.remove('dark', 'light-theme');
    if (theme === 'dark') {
      document.body.classList.add('dark');
    } else {
      document.body.classList.add('light-theme');
    }
    
    // Apply to common React root containers
    const appContainers = ['#root', '#app', '.app', '[data-reactroot]'];
    appContainers.forEach(selector => {
      const container = document.querySelector(selector);
      if (container) {
        container.classList.remove('dark', 'light-theme');
        if (theme === 'dark') {
          container.classList.add('dark');
        } else {
          container.classList.add('light-theme');
        }
        console.log(`[DEBUG] Applied ${theme} theme to container:`, selector);
      }
    });
    
    // AGGRESSIVE FIX: Apply theme classes to all major elements that might have backgrounds
    const allElements = document.querySelectorAll('*');
    let elementsUpdated = 0;
    allElements.forEach(el => {
      const styles = getComputedStyle(el);
      // If element has a background color and is likely a major container
      if (styles.backgroundColor !== 'rgba(0, 0, 0, 0)' && 
          styles.backgroundColor !== 'transparent' &&
          (el.tagName === 'DIV' || el.tagName === 'MAIN' || el.tagName === 'SECTION' || el.tagName === 'ARTICLE')) {
        el.classList.remove('dark', 'light-theme');
        if (theme === 'dark') {
          el.classList.add('dark');
        } else {
          el.classList.add('light-theme');
        }
        elementsUpdated++;
      }
    });
    console.log(`[DEBUG] Applied ${theme} theme to ${elementsUpdated} elements with backgrounds`);

    console.log(`[DEBUG] After adding ${theme} class:`, root.classList.toString());

    // Store theme preference with fallback
    storeThemePreference(theme);

    // Mark that initialization has occurred
    themeInitialized = true;

    // Check computed styles after applying theme
    setTimeout(() => {
      const bodyStyles = getComputedStyle(document.body);
      const rootStyles = getComputedStyle(root);
      
      console.log(`[DEBUG] AFTER applyTheme(${theme}) - detailed analysis:`, {
        currentClasses: root.classList.toString(),
        bodyComputedBg: bodyStyles.backgroundColor,
        bodyComputedColor: bodyStyles.color,
        cssVariables: {
          '--notely-bg': rootStyles.getPropertyValue('--notely-bg').trim(),
          '--notely-text-primary': rootStyles.getPropertyValue('--notely-text-primary').trim()
        },
        bodyStyleAttribute: document.body.getAttribute('style'),
        bodyInlineStyles: {
          backgroundColor: document.body.style.backgroundColor,
          color: document.body.style.color
        },
        appliedCSSRules: Array.from(document.styleSheets).map(sheet => {
          try {
            return Array.from(sheet.cssRules).filter(rule => 
              rule.selectorText && (
                rule.selectorText.includes('body') || 
                rule.selectorText.includes('.dark') || 
                rule.selectorText.includes('.light-theme')
              )
            ).map(rule => ({
              selector: rule.selectorText,
              styles: rule.style.cssText
            }));
          } catch (e) {
            return 'Cannot access stylesheet';
          }
        }).flat()
      });
      
      // Check if there are any elements with higher specificity overriding body
      const allElements = document.querySelectorAll('*');
      const elementsWithBgColor = Array.from(allElements).filter(el => {
        const styles = getComputedStyle(el);
        return styles.backgroundColor !== 'rgba(0, 0, 0, 0)' && styles.backgroundColor !== 'transparent';
      }).slice(0, 10); // Limit to first 10 for debugging
      
      elementsWithBgColor.forEach((el, index) => {
        const styles = getComputedStyle(el);
        console.log(`[DEBUG] Element ${index + 1}:`, {
          tagName: el.tagName,
          className: el.className,
          id: el.id,
          backgroundColor: styles.backgroundColor,
          position: styles.position,
          zIndex: styles.zIndex,
          width: styles.width,
          height: styles.height,
          hasThemeClass: el.classList.contains('dark') || el.classList.contains('light-theme'),
          element: el
        });
      });
      
      // Check for full-screen covering elements
      const fullScreenElements = Array.from(allElements).filter(el => {
        const styles = getComputedStyle(el);
        const rect = el.getBoundingClientRect();
        return (
          (styles.position === 'fixed' || styles.position === 'absolute') &&
          (rect.width >= window.innerWidth * 0.8 || rect.height >= window.innerHeight * 0.8)
        );
      });
      
      console.log('[DEBUG] Potential full-screen covering elements:', fullScreenElements.map(el => ({
        tagName: el.tagName,
        className: el.className,
        id: el.id,
        backgroundColor: getComputedStyle(el).backgroundColor,
        position: getComputedStyle(el).position,
        zIndex: getComputedStyle(el).zIndex,
        dimensions: `${el.getBoundingClientRect().width}x${el.getBoundingClientRect().height}`
      })));
    }, 100);

    console.log(`Applied theme: ${theme}, classes:`, root.classList.toString());
    
  } catch (error) {
    console.warn('Theme application failed:', error);
  }
};

/**
 * Store theme preference with multiple storage fallbacks
 */
const storeThemePreference = (theme: Theme): void => {
  try {
    // Try Chrome storage first
    if (typeof chrome !== 'undefined' && chrome?.storage?.local) {
      chrome.storage.local.set({ theme }, () => {
        if (chrome.runtime.lastError) {
          // Chrome storage failed, try localStorage
          trySetLocalStorage(theme);
        }
      });
    } else {
      // Not in Chrome extension, use localStorage
      trySetLocalStorage(theme);
    }
  } catch (error) {
    console.debug('Theme storage failed:', error);
  }
};

/**
 * Try to set theme in localStorage
 */
const trySetLocalStorage = (theme: Theme): void => {
  try {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('theme', theme);
      localStorage.setItem('notely-theme', theme); // Backup key
    }
  } catch (error) {
    console.debug('localStorage write failed:', error);
  }
};

/**
 * Toggle between light and dark themes with fallback logic
 */
export const toggleTheme = (): Promise<Theme> => {
  return new Promise((resolve) => {
    try {
      console.log('[DEBUG] toggleTheme() called');
      getCurrentTheme().then((currentTheme) => {
        console.log('[DEBUG] Current theme detected as:', currentTheme);
        const newTheme: Theme = currentTheme === 'dark' ? 'light' : 'dark';
        console.log('[DEBUG] Toggling to:', newTheme);
        applyTheme(newTheme);
        // Ensure preference is stored immediately
        storeThemePreference(newTheme);
        themeInitialized = true;
        resolve(newTheme);
      }).catch((error) => {
        console.warn('Theme toggle failed:', error);
        // Fallback: assume dark and toggle to light
        const newTheme: Theme = 'light';
        applyTheme(newTheme);
        storeThemePreference(newTheme);
        themeInitialized = true;
        resolve(newTheme);
      });
    } catch (error) {
      console.warn('Theme toggle failed completely:', error);
      // Safe fallback: apply light theme
      applyTheme('light');
      storeThemePreference('light');
      themeInitialized = true;
      resolve('light');
    }
  });
};

/**
 * Get current theme with comprehensive fallback
 * Prioritizes stored user preferences over DOM/system detection
 */
export const getCurrentTheme = (): Promise<Theme> => {
  console.log('[DEBUG] getCurrentTheme() called');
  return getStoredThemePreference().then((storedTheme) => {
    console.log('[DEBUG] getStoredThemePreference() returned:', storedTheme);
    if (storedTheme) {
      console.log('[DEBUG] Using stored theme:', storedTheme);
      return storedTheme;
    }
    // No stored preference, use detection as fallback
    console.log('[DEBUG] No stored preference, using detection fallback');
    return detectCurrentThemeAsync().then((detectedTheme) => {
      console.log('[DEBUG] detectCurrentThemeAsync() returned:', detectedTheme);
      return detectedTheme;
    });
  });
};

/**
 * Listen for theme changes with fallback logic
 * Returns a cleanup function to remove all listeners
 */
export const onThemeChange = (callback: (theme: Theme) => void): (() => void) => {
  const cleanupFunctions: (() => void)[] = [];

  try {
    // Chrome storage listener
    if (typeof chrome !== 'undefined' && chrome?.storage?.onChanged) {
      const chromeListener = (changes: any, areaName: string) => {
        if (areaName === 'local' && changes?.theme) {
          callback(changes.theme.newValue);
        }
      };
      chrome.storage.onChanged.addListener(chromeListener);
      cleanupFunctions.push(() => {
        if (chrome?.storage?.onChanged) {
          chrome.storage.onChanged.removeListener(chromeListener);
        }
      });
    }

    // LocalStorage listener (for web context)
    if (typeof window !== 'undefined' && window.addEventListener) {
      const storageListener = (event: StorageEvent) => {
        if ((event.key === 'theme' || event.key === 'notely-theme') &&
            (event.newValue === 'dark' || event.newValue === 'light')) {
          callback(event.newValue);
        }
      };
      window.addEventListener('storage', storageListener);
      cleanupFunctions.push(() => {
        window.removeEventListener('storage', storageListener);
      });
    }

    // MutationObserver for class changes on document element
    if (typeof document !== 'undefined' && document.documentElement) {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const newTheme = detectCurrentTheme();
            callback(newTheme);
          }
        });
      });

      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });

      cleanupFunctions.push(() => {
        observer.disconnect();
      });
    }
  } catch (error) {
    console.warn('Theme change listener setup failed:', error);
  }

  // Return cleanup function that calls all cleanup functions
  return () => {
    cleanupFunctions.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.warn('Theme listener cleanup failed:', error);
      }
    });
  };
};

// Removed safeInitializeTheme to prevent automatic theme initialization

// Note: Theme initialization is now handled explicitly by components
// to avoid conflicts with manual theme toggles
