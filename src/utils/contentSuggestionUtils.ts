import { Post } from '../types';
import { ContentSuggestion, ContentAction, CONTENT_ACTIONS } from '../types/contentSuggestions';

/**
 * Analyzes posts to determine which content actions are most suitable
 */
export function analyzePostsForContentSuggestions(posts: Post[]): ContentSuggestion[] {
  const suggestions: ContentSuggestion[] = [];
  
  // Filter posts from the last 30 days with sufficient content
  const recentPosts = posts.filter(post => {
    const postDate = new Date(post.savedAt || post.timestamp || Date.now());
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const hasContent = (post.content || post.text || '').length > 50;
    
    return postDate > thirtyDaysAgo && hasContent;
  });

  // Sort by engagement potential (likes, shares, etc.)
  const sortedPosts = recentPosts.sort((a, b) => {
    const aEngagement = (a.interactions?.likes || 0) + (a.interactions?.reposts || 0) + (a.interactions?.replies || 0);
    const bEngagement = (b.interactions?.likes || 0) + (b.interactions?.reposts || 0) + (b.interactions?.replies || 0);
    return bEngagement - aEngagement;
  });

  // Take top 10 posts for analysis
  const topPosts = sortedPosts.slice(0, 10);

  topPosts.forEach(post => {
    const postContent = post.content || post.text || '';
    const contentLength = postContent.length;
    
    // Analyze each content action for suitability
    CONTENT_ACTIONS.forEach(action => {
      const suggestion = evaluateContentAction(post, action, contentLength);
      if (suggestion && suggestion.confidence > 0.6) {
        suggestions.push(suggestion);
      }
    });
  });

  // Sort by confidence and return top suggestions
  return suggestions
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, 12); // Increased limit to 12 suggestions
}

/**
 * Evaluates if a specific content action is suitable for a post
 */
function evaluateContentAction(post: Post, action: ContentAction, contentLength: number): ContentSuggestion | null {
  const postContent = post.content || post.text || '';
  
  // Check length requirements
  if (action.minContentLength && contentLength < action.minContentLength) {
    return null;
  }
  if (action.maxContentLength && contentLength > action.maxContentLength) {
    return null;
  }

  // Check platform compatibility
  if (action.platforms.length > 0 && post.platform && !action.platforms.includes(post.platform)) {
    // Allow cross-platform suggestions with lower confidence
  }

  let confidence = 0.5; // Base confidence
  let title = '';
  let description = '';

  switch (action.id) {
    case 'thread':
      confidence = evaluateThreadSuitability(post, contentLength);
      title = 'Turn into Twitter Thread';
      description = `Break this ${post.platform} post into an engaging Twitter thread`;
      break;
      
    case 'caption':
      confidence = evaluateCaptionSuitability(post, contentLength);
      title = 'Create Social Caption';
      description = `Summarize this content into a compelling social media caption`;
      break;
      
    case 'newsletter':
      confidence = evaluateNewsletterSuitability(post, contentLength);
      title = 'Newsletter Introduction';
      description = `Use this insight as a newsletter intro or featured content`;
      break;
      
    case 'quote':
      confidence = evaluateQuoteSuitability(post, contentLength);
      title = 'Extract Key Quote';
      description = `Pull out the most impactful quotes for sharing`;
      break;
      
    case 'poll':
      confidence = evaluatePollSuitability(post, contentLength);
      title = 'Create Engagement Poll';
      description = `Turn this content into an interactive poll`;
      break;
      
    case 'story':
      confidence = evaluateStorySuitability(post, contentLength);
      title = 'Story Highlight';
      description = `Create story content from key insights`;
      break;
      
    default:
      return null;
  }

  if (confidence < 0.3) {
    return null;
  }

  return {
    id: `${post.id}-${action.id}-${Date.now()}`,
    type: action.id as ContentSuggestion['type'],
    title,
    description,
    originalPost: post,
    confidence,
    platform: action.platforms[0],
    estimatedEngagement: confidence > 0.8 ? 'high' : confidence > 0.6 ? 'medium' : 'low',
    createdAt: new Date()
  };
}

/**
 * Specific evaluation functions for each content type
 */
function evaluateThreadSuitability(post: Post, contentLength: number): number {
  let score = 0.5;
  
  // Longer content is better for threads
  if (contentLength > 200) score += 0.2;
  if (contentLength > 500) score += 0.2;
  
  // Check for list-like content or multiple points
  const content = post.content || post.text || '';
  const hasNumbers = /\d+\./g.test(content);
  const hasBullets = /[•\-\*]/g.test(content);
  const hasMultiplePoints = content.split(/[.!?]/).length > 3;
  
  if (hasNumbers || hasBullets) score += 0.3;
  if (hasMultiplePoints) score += 0.2;
  
  // High engagement content is good for threads
  const engagement = (post.interactions?.likes || 0) + (post.interactions?.reposts || 0);
  if (engagement > 10) score += 0.2;
  
  return Math.min(score, 1.0);
}

function evaluateCaptionSuitability(post: Post, contentLength: number): number {
  let score = 0.6;
  
  // Medium length content is ideal for captions
  if (contentLength > 100 && contentLength < 300) score += 0.2;
  
  // Visual content is great for captions
  if (post.media && post.media.length > 0) score += 0.3;
  
  // Check for engaging elements
  const content = post.content || post.text || '';
  const hasQuestion = /\?/g.test(content);
  const hasEmoji = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu.test(content);
  
  if (hasQuestion) score += 0.1;
  if (hasEmoji) score += 0.1;
  
  return Math.min(score, 1.0);
}

function evaluateNewsletterSuitability(post: Post, contentLength: number): number {
  let score = 0.4;
  
  // Longer, more substantial content
  if (contentLength > 200) score += 0.3;
  if (contentLength > 500) score += 0.2;
  
  // Professional platforms
  if (post.platform === 'LinkedIn') score += 0.3;
  if (post.platform === 'Web') score += 0.2;
  
  // Check for insights, data, or professional content
  const content = post.content || post.text || '';
  const hasInsights = /insight|data|research|study|analysis/gi.test(content);
  const hasNumbers = /\d+%|\$\d+|\d+k|\d+m/gi.test(content);
  
  if (hasInsights) score += 0.2;
  if (hasNumbers) score += 0.1;
  
  return Math.min(score, 1.0);
}

function evaluateQuoteSuitability(post: Post, contentLength: number): number {
  let score = 0.5;
  
  // Check for quotable content
  const content = post.content || post.text || '';
  const hasQuotes = /[""].*[""]|".*"/g.test(content);
  const hasWisdom = /wisdom|advice|lesson|truth|insight/gi.test(content);
  const isInspirational = /inspire|motivate|believe|achieve|success/gi.test(content);
  
  if (hasQuotes) score += 0.3;
  if (hasWisdom) score += 0.2;
  if (isInspirational) score += 0.2;
  
  // Shorter content is often more quotable
  if (contentLength < 200) score += 0.1;
  
  return Math.min(score, 1.0);
}

function evaluatePollSuitability(post: Post, contentLength: number): number {
  let score = 0.4;
  
  // Check for opinion-based or choice-related content
  const content = post.content || post.text || '';
  const hasQuestion = /\?/g.test(content);
  const hasOptions = /vs|versus|or|either|prefer|choose|better/gi.test(content);
  const hasOpinion = /think|believe|opinion|prefer|like|love|hate/gi.test(content);
  
  if (hasQuestion) score += 0.3;
  if (hasOptions) score += 0.3;
  if (hasOpinion) score += 0.2;
  
  // Platforms that support polls
  if (post.platform === 'X/Twitter' || post.platform === 'LinkedIn') score += 0.2;
  
  return Math.min(score, 1.0);
}

function evaluateStorySuitability(post: Post, contentLength: number): number {
  let score = 0.5;
  
  // Visual content is great for stories
  if (post.media && post.media.length > 0) score += 0.4;
  
  // Behind-the-scenes or personal content
  const content = post.content || post.text || '';
  const isPersonal = /behind|process|journey|experience|story|moment/gi.test(content);
  const hasEmoji = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu.test(content);
  
  if (isPersonal) score += 0.2;
  if (hasEmoji) score += 0.1;
  
  // Story-friendly platforms
  if (post.platform === 'Instagram') score += 0.3;
  
  return Math.min(score, 1.0);
}

/**
 * Formats content length for display
 */
export function formatContentLength(length: number): string {
  if (length < 100) return 'Short';
  if (length < 300) return 'Medium';
  if (length < 500) return 'Long';
  return 'Very Long';
}

/**
 * Gets platform-specific content limits
 */
export function getPlatformLimits(platform: string): { maxLength: number; recommendedLength: number } {
  switch (platform) {
    case 'X/Twitter':
      return { maxLength: 280, recommendedLength: 240 };
    case 'Instagram':
      return { maxLength: 2200, recommendedLength: 150 };
    case 'LinkedIn':
      return { maxLength: 3000, recommendedLength: 300 };
    default:
      return { maxLength: 500, recommendedLength: 200 };
  }
}
