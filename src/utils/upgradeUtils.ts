/**
 * Centralized upgrade utilities for Premium subscription flow
 * 
 * This module provides a single source of truth for handling premium upgrades,
 * ensuring consistent behavior across all CTAs and components.
 */

import { getPriceId, type PlanType } from '../config/stripe';
import * as subscriptionService from '../services/subscriptionService';
import analyticsService from '../services/analyticsService';

/**
 * Analytics events for tracking upgrade flow
 */
export interface UpgradeAnalytics {
  upgrade_flow_opened: (data: { source: string }) => void;
  upgrade_plan_selected: (data: { source: string; plan: PlanType }) => void;
  checkout_started: (data: { source: string; plan: PlanType }) => void;
}

/**
 * Upgrade flow sources for analytics tracking
 */
export const UPGRADE_SOURCES = {
  NAV_CTA: 'nav_cta',
  DASHBOARD_CTA: 'dashboard_cta',
  SETTINGS_HEADER: 'settings_header',
  PRICING_CARD: 'pricing_card',
  WEB_DASHBOARD: 'web_dashboard',
} as const;

export type UpgradeSource = typeof UPGRADE_SOURCES[keyof typeof UPGRADE_SOURCES];

/**
 * Error types for upgrade flow
 */
export class UpgradeError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'UpgradeError';
  }
}

/**
 * Analytics tracking (optional - can be injected)
 */
let analytics: Partial<UpgradeAnalytics> = {};

export const setAnalytics = (analyticsInstance: Partial<UpgradeAnalytics>) => {
  analytics = analyticsInstance;
};

/**
 * Navigate to plan chooser (settings page with plan selection focus)
 * 
 * @param source - Source of the upgrade request for analytics
 * @param preselectedPlan - Optional plan to highlight (but still requires user confirmation)
 */
export const navigateToPlanChooser = (source: UpgradeSource, preselectedPlan?: PlanType): void => {
  // Track analytics
  analyticsService.trackUpgradeFlowOpened(source);
  analytics.upgrade_flow_opened?.({ source });

  // Build URL with query parameters
  const params = new URLSearchParams();
  params.set('selectPlan', '1');
  
  if (preselectedPlan) {
    params.set('plan', preselectedPlan);
  }

  // Determine the correct settings URL based on environment
  let settingsUrl: string;
  
  if (typeof chrome !== 'undefined' && chrome.runtime?.id) {
    // Chrome extension environment
    settingsUrl = `chrome-extension://${chrome.runtime.id}/settings.html?${params.toString()}`;
  } else if (window.location.hostname === 'notely.social' || window.location.hostname.includes('notely')) {
    // Web dashboard environment
    settingsUrl = `/dashboard/settings?${params.toString()}`;
  } else {
    // Fallback - try relative path
    settingsUrl = `/settings?${params.toString()}`;
  }

  // Navigate to settings
  if (typeof window !== 'undefined') {
    if (window.location.hostname === 'notely.social' || window.location.hostname.includes('notely')) {
      // Use React Router navigation if available
      try {
        const { useNavigate } = require('react-router-dom');
        const navigate = useNavigate();
        navigate(`/dashboard/settings?${params.toString()}`);
        return;
      } catch {
        // Fallback to window.location
      }
    }
    
    window.location.href = settingsUrl;
  }
};

/**
 * Start upgrade process for a specific plan
 * 
 * @param plan - The plan to upgrade to ('monthly' | 'yearly')
 * @param source - Source of the upgrade request for analytics
 * @returns Promise that resolves when checkout is initiated
 */
export const startUpgrade = async (plan: PlanType, source: UpgradeSource): Promise<void> => {
  try {
    // Validate plan
    if (!plan || (plan !== 'monthly' && plan !== 'yearly')) {
      throw new UpgradeError('Invalid plan specified. Must be "monthly" or "yearly".', 'INVALID_PLAN');
    }

    // Track plan selection
    analyticsService.trackUpgradePlanSelected(source, plan);
    analytics.upgrade_plan_selected?.({ source, plan });

    // Get price ID for the plan
    let priceId: string;
    try {
      priceId = getPriceId(plan);
    } catch (error) {
      console.error('Failed to get price ID:', error);
      throw new UpgradeError(
        `Configuration error: Missing price ID for ${plan} plan. Please contact support.`,
        'MISSING_PRICE_ID'
      );
    }

    // Create checkout session
    const checkoutUrl = await subscriptionService.createCheckoutSession(priceId);
    
    if (!checkoutUrl) {
      throw new UpgradeError(
        'Failed to create checkout session. Please try again or contact support.',
        'CHECKOUT_FAILED'
      );
    }

    // Track checkout start
    analyticsService.trackCheckoutStarted(source, plan);
    analytics.checkout_started?.({ source, plan });

    // Open checkout in new tab
    window.open(checkoutUrl, '_blank');

  } catch (error) {
    console.error('Error during upgrade process:', error);
    
    // Re-throw UpgradeError as-is
    if (error instanceof UpgradeError) {
      throw error;
    }
    
    // Wrap other errors
    throw new UpgradeError(
      'An unexpected error occurred during the upgrade process. Please try again.',
      'UNKNOWN_ERROR'
    );
  }
};

/**
 * Open billing portal for premium users
 * 
 * @param source - Source of the request for analytics
 * @returns Promise that resolves when portal is opened
 */
export const openBillingPortal = async (source: UpgradeSource): Promise<void> => {
  try {
    // Track analytics
    analyticsService.trackBillingPortalOpened(source);

    const portalUrl = await subscriptionService.createPortalSession();

    if (!portalUrl) {
      throw new UpgradeError(
        'Unable to create billing portal session. Please try again or contact support.',
        'PORTAL_FAILED'
      );
    }

    window.open(portalUrl, '_blank');

  } catch (error) {
    console.error('Error opening billing portal:', error);
    
    if (error instanceof UpgradeError) {
      throw error;
    }
    
    throw new UpgradeError(
      'Failed to open billing portal. Please try again.',
      'PORTAL_ERROR'
    );
  }
};

/**
 * Check if user should see upgrade CTAs or manage billing
 * 
 * @param user - User object with plan and subscription status
 * @returns Object indicating what CTAs to show
 */
export const getUpgradeState = (user: any) => {
  const isPremium = user?.plan === 'premium';
  const isTrialing = user?.plan === 'free' && user?.subscriptionStatus === 'trialing';
  const isFree = !isPremium && !isTrialing;

  return {
    isPremium,
    isTrialing,
    isFree,
    showUpgradeCTA: isFree || isTrialing,
    showManageBilling: isPremium,
    ctaText: isPremium ? 'Manage Billing' : 'Upgrade to Premium',
    trialMessage: isTrialing ? `You're on a free trial. Pick a plan to continue.` : null,
  };
};

/**
 * Utility to show user-friendly error messages
 * 
 * @param error - The error to display
 * @param showToast - Optional function to show toast notification
 */
export const handleUpgradeError = (error: unknown, showToast?: (message: string, isError: boolean) => void): void => {
  let message = 'An unexpected error occurred. Please try again.';
  
  if (error instanceof UpgradeError) {
    message = error.message;
  } else if (error instanceof Error) {
    message = error.message;
  }

  console.error('Upgrade error:', error);
  
  if (showToast) {
    showToast(message, true);
  } else {
    // Fallback to alert if no toast function provided
    alert(message);
  }
};
