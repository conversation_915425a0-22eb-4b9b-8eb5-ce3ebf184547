/**
 * Utility functions for formatting text in the Notely Social UI
 */

/**
 * Special mappings for category and tag display names
 */
const DISPLAY_NAME_MAPPINGS: Record<string, string> = {
  // Categories
  'arts_design': 'Arts & Design',
  'web_design': 'Web Design',
  'graphic_design': 'Graphic Design',
  'ui_ux': 'UI/UX',
  'artificial_intelligence': 'Artificial Intelligence',
  'machine_learning': 'Machine Learning',
  'data_science': 'Data Science',
  'software_development': 'Software Development',
  'web_development': 'Web Development',
  'mobile_development': 'Mobile Development',
  'pop_culture': 'Pop Culture',
  'social_media': 'Social Media',
  'digital_marketing': 'Digital Marketing',
  'content_marketing': 'Content Marketing',
  'personal_development': 'Personal Development',
  'mental_health': 'Mental Health',
  'physical_fitness': 'Physical Fitness',
  'food_drink': 'Food & Drink',
  'travel_adventure': 'Travel & Adventure',
  'home_garden': 'Home & Garden',
  'diy_crafts': 'DIY & Crafts',
  'fashion_style': 'Fashion & Style',
  'beauty_skincare': 'Beauty & Skincare',
  'health_wellness': 'Health & Wellness',
  'finance_investing': 'Finance & Investing',
  'real_estate': 'Real Estate',
  'small_business': 'Small Business',
  'career_development': 'Career Development',
  'science_research': 'Science & Research',
  'history_culture': 'History & Culture',
  'philosophy_ethics': 'Philosophy & Ethics',
  'books_literature': 'Books & Literature',
  'movies_tv': 'Movies & TV',
  'music_audio': 'Music & Audio',
  'gaming_esports': 'Gaming & Esports',
  'sports_recreation': 'Sports & Recreation',
  'automotive_transport': 'Automotive & Transport',
  'pets_animals': 'Pets & Animals',
  'parenting_family': 'Parenting & Family',
  'education_learning': 'Education & Learning',
  'news_politics': 'News & Politics',
  'environment_sustainability': 'Environment & Sustainability',

  // Common tags
  'ai': 'AI',
  'ml': 'ML',
  'ux': 'UX',
  'ui': 'UI',
  'seo': 'SEO',
  'api': 'API',
  'css': 'CSS',
  'html': 'HTML',
  'js': 'JS',
  'javascript': 'JavaScript',
  'typescript': 'TypeScript',
  'react': 'React',
  'vue': 'Vue',
  'angular': 'Angular',
  'nodejs': 'Node.js',
  'ios': 'iOS',
  'android': 'Android',
  'saas': 'SaaS',
  'b2b': 'B2B',
  'b2c': 'B2C',
  'roi': 'ROI',
  'kpi': 'KPI',
  'ceo': 'CEO',
  'cto': 'CTO',
  'cfo': 'CFO',
  'hr': 'HR',
  'pr': 'PR',
  'usa': 'USA',
  'uk': 'UK',
  'eu': 'EU',
  'nyc': 'NYC',
  'la': 'LA',
  'sf': 'SF'
};

/**
 * Formats a category or tag string for display in the UI
 * - Handles special mappings (e.g., "arts_design" → "Arts & Design")
 * - Converts underscores to spaces
 * - Applies Title Case (capitalizes each word)
 * - Makes the text user-friendly
 *
 * @param text The raw category or tag text (e.g., "arts_design" or "travel")
 * @returns Formatted text for display (e.g., "Arts & Design" or "Travel")
 */
export function formatForDisplay(text: string): string {
  if (!text) return '';

  // Normalize the input
  const normalized = text.toLowerCase().trim();

  // Check for special mappings first
  if (DISPLAY_NAME_MAPPINGS[normalized]) {
    return DISPLAY_NAME_MAPPINGS[normalized];
  }

  // Replace underscores with spaces
  const withSpaces = normalized.replace(/_/g, ' ');

  // Apply Title Case (capitalize each word)
  return withSpaces
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Formats a system slug back to its raw form for storage
 * - Converts spaces to underscores
 * - Makes text lowercase
 * 
 * @param displayText The display text (e.g., "Artificial Intelligence")
 * @returns Raw slug for storage (e.g., "artificial_intelligence")
 */
export function formatForStorage(displayText: string): string {
  if (!displayText) return '';

  // Convert to lowercase and replace spaces with underscores
  return displayText.toLowerCase().replace(/\s+/g, '_');
}

/**
 * Convert WisdomQuote to PostWithAIData format for use in PostViewerFullScreen
 * This allows wisdom quotes to be displayed using the unified popup component
 */
export function convertWisdomQuoteToPost(quote: any): any {
  return {
    id: quote.id,
    platform: 'wisdom', // Custom platform for wisdom quotes
    mediaType: 'text',
    text: quote.text,
    content: quote.text,
    author: quote.author || 'Daily Wisdom',
    timestamp: quote.createdAt,
    tags: quote.tags || [],
    categories: quote.categories || [],
    notes: quote.source ? `Source: ${quote.source}` : undefined,
    snapNote: quote.extractedFrom === 'post' ? 'Extracted from your saved content' : 'AI-generated wisdom',
    // Add wisdom-specific metadata
    fastTake: quote.isFavorite ? '⭐ Favorite wisdom quote' : undefined,
  };
}
