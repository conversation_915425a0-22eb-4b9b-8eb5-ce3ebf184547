import React, { useState, useEffect } from 'react';
import { formatFileSize } from '../settings/settings';
import { 
  getAuthToken, 
  getStorageUsage, 
  getStorageUsageColor, 
  getStorageUsageMessage, 
  getRecommendedAction,
  type StorageUsageType 
} from '../utils/storageUtils';

interface StorageUsageProps {
  className?: string;
  showDetails?: boolean;
}

const StorageUsage: React.FC<StorageUsageProps> = ({
  className = '',
  showDetails = true
}) => {
  const [storageData, setStorageData] = useState<StorageUsageType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Check authentication status
  const checkAuthStatus = async () => {
    const token = await getAuthToken();
    setIsAuthenticated(!!token);
    return !!token;
  };

  useEffect(() => {
    const initializeComponent = async () => {
      const isAuth = await checkAuthStatus();
      if (isAuth) {
        await fetchStorageUsage();
      } else {
        setLoading(false);
      }
    };

    initializeComponent();

    // Listen for authentication changes and post updates
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes.token || changes.authToken) {
        initializeComponent();
      }
      // Refresh storage when posts are saved or deleted (which might affect cloud storage)
      if (changes.savedPosts || changes.localSavedPosts) {
        if (isAuthenticated) {
          // Debounce the refresh to avoid too many API calls
          setTimeout(() => {
            fetchStorageUsage();
          }, 1000);
        }
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, [isAuthenticated]);

  const fetchStorageUsage = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getStorageUsage();
      setStorageData(data);
    } catch (error: Error | unknown) {
      const handleStorageError = (error: Error | unknown) => {
        console.error('Storage usage error:', error);
        setError('Failed to load storage data');
        setLoading(false);
      };
      handleStorageError(error);
      console.error('Storage usage fetch error:', error);

      // If authentication error, update auth status
      if (error instanceof Error && (error.message.includes('token') || error.message.includes('Authentication'))) {
        setIsAuthenticated(false);
      }
    } finally {
      setLoading(false);
    }
  };

  // Don't render anything if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <div className={`notely-card bg-notely-card border border-gray-200 rounded-notely-lg shadow-[0_1px_3px_rgba(0,0,0,0.06),_0_1px_2px_rgba(0,0,0,0.04)] notely-breathing-md ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <span className="text-lg">💾</span>
              <div className="h-4 bg-notely-surface rounded w-16"></div>
            </div>
            <div className="h-6 bg-notely-surface rounded-full w-12"></div>
          </div>
          <div className="h-2 bg-notely-surface rounded-full w-full mb-2"></div>
          <div className="h-3 bg-notely-surface rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error || !storageData) {
    return (
      <div className={`notely-card bg-notely-card border border-gray-200 rounded-notely-lg shadow-[0_1px_3px_rgba(0,0,0,0.06),_0_1px_2px_rgba(0,0,0,0.04)] px-6 py-4 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <span className="text-lg">💾</span>
            <h3 className="text-xl font-semibold leading-tight text-notely-text-primary">
              Storage
            </h3>
          </div>
          <span className="text-[11px] font-medium rounded-md px-2 py-1 bg-notely-coral/20 text-notely-coral border border-notely-coral/30">
            ERROR
          </span>
        </div>
        <div className="text-sm text-notely-text-muted leading-relaxed mb-4">
          {error || 'Storage usage unavailable'}
        </div>
        <button
          onClick={fetchStorageUsage}
          className="w-full text-xs font-medium py-2 px-3 rounded-notely-md transition-all duration-200 text-notely-sky hover:text-notely-text-primary hover:bg-notely-sky/20 border border-notely-sky/30 hover:border-notely-sky"
        >
          <div className="flex items-center justify-center space-x-2">
            <span>🔄</span>
            <span>Retry</span>
          </div>
        </button>
      </div>
    );
  }

  const progressColor = getStorageUsageColor(storageData.usagePercentage);
  const message = getStorageUsageMessage(storageData);
  const recommendedAction = getRecommendedAction(storageData);

  return (
    <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-lg shadow-notely-sm hover:shadow-notely-md transition-all duration-200 px-4 py-4 ${className}`}>
      {/* Header with enhanced visual hierarchy */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="text-sm">💾</span>
          <h3 className="text-sm font-semibold leading-tight text-notely-text-primary">
            Storage
          </h3>
        </div>
        <span className={`text-xs font-medium rounded px-1.5 py-0.5 ${
          storageData.plan === 'premium'
            ? 'bg-notely-mint/10 text-notely-mint'
            : 'bg-notely-surface text-notely-text-muted'
        }`}>
          {storageData.plan.toUpperCase()}
        </span>
      </div>

      {/* Compact Progress Bar */}
      <div className="space-y-1.5 mb-3">
        <div className="flex justify-between items-center text-xs text-notely-text-muted">
          <span>{formatFileSize(storageData.usedMB * 1024 * 1024)}</span>
          <span>{Math.max(0, storageData.usagePercentage || 0)}%</span>
        </div>

        {/* Minimal progress bar */}
        <div className="relative bg-notely-surface rounded-full overflow-hidden h-1.5">
          <div
            className="transition-all duration-500 ease-out rounded-full h-full"
            style={{
              width: `${Math.max(0, Math.min(storageData.usagePercentage || 0, 100))}%`,
              backgroundColor: progressColor
            }}
          />
        </div>

        <div className="text-xs text-notely-text-muted">
          of {formatFileSize(storageData.limitMB * 1024 * 1024)} total
        </div>
      </div>

      {/* Enhanced Message Section */}
      {showDetails && (
        <div className="space-y-3">
          <p className={`text-sm leading-relaxed ${
            storageData.isOverLimit
              ? 'text-notely-coral'
              : storageData.isNearLimit
                ? 'text-yellow-400'
                : 'text-notely-text-muted'
          }`}>
            {message}
          </p>

          {/* Recommended Action with better styling */}
          {recommendedAction && (
            <div className={`text-sm leading-relaxed px-6 py-4 rounded-notely-md border-l-4 ${
              storageData.isOverLimit
                ? 'bg-notely-coral/10 text-notely-coral border-l-notely-coral border-notely-coral/20'
                : 'bg-notely-sky/10 text-notely-sky border-l-notely-sky border-notely-sky/20'
            }`}>
              <div className="flex items-start space-x-2">
                <span className="text-sm">💡</span>
                <span className="font-medium">{recommendedAction}</span>
              </div>
            </div>
          )}

          {/* Feature highlight */}
          <div className="text-xs text-notely-text-muted bg-notely-surface p-3 rounded-2xl shadow-inner">
            <div className="flex items-center space-x-2">
              <span className="text-notely-sky text-sm">•</span>
              <span className="font-medium">Images auto-preserved for deleted posts</span>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Refresh Button */}
      <button
        onClick={() => {
          if (!loading) {
            fetchStorageUsage();
          }
        }}
        className={`mt-4 w-full text-xs font-medium py-2 px-4 rounded-2xl transition-all duration-200 ${
          loading
            ? 'bg-notely-surface text-notely-text-muted cursor-not-allowed opacity-50'
            : 'text-notely-sky hover:text-notely-text-primary hover:bg-notely-sky/10 border border-notely-sky/30 hover:border-notely-sky shadow-inner'
        }`}
        disabled={loading}
        title={loading ? 'Refreshing storage usage...' : 'Refresh storage usage'}
      >
        {loading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-3 h-3 border-2 border-notely-text-muted border-t-transparent rounded-full animate-spin"></div>
            <span>Refreshing...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <svg className="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M2 12L5 9M2 12L5 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span>Refresh Storage</span>
          </div>
        )}
      </button>
    </div>
  );
};

export default StorageUsage;
