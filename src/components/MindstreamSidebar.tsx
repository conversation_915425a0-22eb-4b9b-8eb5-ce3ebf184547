import React from 'react';
import { Post } from '../types';
import { WisdomQuote } from '../types/wisdom';
import DailyWisdom from './DailyWisdom';
import TagsCategoriesWidget from './TagsCategoriesWidget';
import { convertWisdomQuoteToPost } from '../utils/formatUtils';

interface MindstreamSidebarProps {
  posts: Post[];
  className?: string;
  onOpenPost?: (post: Post) => void;
  onOpenWisdom?: (post: any) => void; // For opening wisdom quotes in PostViewerFullScreen
}

const MindstreamSidebar: React.FC<MindstreamSidebarProps> = ({
  posts,
  className = '',
  onOpenPost,
  onOpenWisdom
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Daily Wisdom */}
      <DailyWisdom
        className="w-full"
        onQuoteClick={(quote: WisdomQuote) => {
          // Convert wisdom quote to post format and display in PostViewerFullScreen
          if (onOpenWisdom) {
            const postData = convertWisdomQuoteToPost(quote);
            onOpenWisdom(postData);
          } else {
            console.log('Quote clicked:', quote);
          }
        }}
        onOpenPost={onOpenPost}
      />
      
      {/* Tags & Categories */}
      <div className="notely-card bg-notely-card rounded-notely-xl shadow-notely-md hover:shadow-notely-lg notely-filter-transition px-6 py-4 overflow-hidden">
        <TagsCategoriesWidget posts={posts} />
      </div>
    </div>
  );
};

export default MindstreamSidebar;
