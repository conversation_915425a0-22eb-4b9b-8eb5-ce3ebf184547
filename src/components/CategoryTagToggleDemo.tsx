import React, { useState } from 'react';
import CategoryTagToggle from './CategoryTagToggle';

const CategoryTagToggleDemo: React.FC = () => {
  const [categories, setCategories] = useState<string[]>(['Technology', 'Design']);
  const [tags, setTags] = useState<string[]>(['React', 'TypeScript', 'UI/UX']);

  const allCategories = [
    'Technology',
    'Design',
    'Business',
    'Marketing',
    'Development',
    'AI/ML',
    'Productivity',
    'Health',
    'Finance',
    'Education'
  ];

  const allTags = [
    'React',
    'TypeScript',
    'JavaScript',
    'UI/UX',
    'Frontend',
    'Backend',
    'API',
    'Database',
    'Testing',
    'Performance',
    'Security',
    'Mobile',
    'Web',
    'CSS',
    'HTML'
  ];

  return (
    <div className="min-h-screen bg-notely-background p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-notely-text-primary mb-8">
          Category Tag Toggle Demo
        </h1>
        
        <div className="bg-white dark:bg-notely-card rounded-xl p-6 shadow-notely-md">
          <CategoryTagToggle
            categories={categories}
            tags={tags}
            allCategories={allCategories}
            allTags={allTags}
            onCategoriesChange={setCategories}
            onTagsChange={setTags}
            maxCategories={5}
            maxTags={10}
          />
        </div>

        <div className="mt-8 space-y-4">
          <div className="bg-notely-surface/60 rounded-lg p-4">
            <h3 className="font-semibold text-notely-text-primary mb-2">Current Categories:</h3>
            <p className="text-notely-text-secondary">
              {categories.length > 0 ? categories.join(', ') : 'None'}
            </p>
          </div>
          
          <div className="bg-notely-surface/60 rounded-lg p-4">
            <h3 className="font-semibold text-notely-text-primary mb-2">Current Tags:</h3>
            <p className="text-notely-text-secondary">
              {tags.length > 0 ? tags.join(', ') : 'None'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryTagToggleDemo;
