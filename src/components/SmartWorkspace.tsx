import React, { useMemo } from 'react';
import { Post } from '../types';

interface SmartWorkspaceProps {
  posts: Post[];
  className?: string;
  onOpenPost?: (postId: string) => void; // Changed to just pass post ID like regular dashboard
}

const SmartWorkspace: React.FC<SmartWorkspaceProps> = ({ posts, className = '', onOpenPost }) => {

  const inspirationPosts = useMemo(() => {
    if (posts.length === 0) return [];
    const postsWithImages = posts.filter(post => (post.media && post.media.length > 0) || post.savedImage);
    if (postsWithImages.length === 0) return [];
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const olderPostsWithImages = postsWithImages.filter(post => new Date(post.savedAt || post.timestamp || '') < sevenDaysAgo);
    if (olderPostsWithImages.length >= 4) {
      return [...olderPostsWithImages].sort(() => Math.random() - 0.5).slice(0, 4);
    } else {
      return postsWithImages.sort((a, b) => new Date(a.savedAt || a.timestamp || '').getTime() - new Date(b.savedAt || b.timestamp || '').getTime()).slice(0, 4);
    }
  }, [posts]);

  return (
    <div className={`notely-card bg-notely-card rounded-notely-xl shadow-notely-md hover:shadow-notely-lg notely-filter-transition p-6 border border-notely-border/10 dark:border-notely-border-dark/20 ${className}`}>
      {/* Inspiration Feed */}
      <div>
        <h2 className="text-xl font-semibold mb-2 notely-heading">✨ Rediscover Your Posts</h2>
        <p className="text-sm text-notely-text-secondary mb-4">Forgotten gems from your saved content</p>
        <div className="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-4 gap-6">
          {inspirationPosts.map(post => {
            const getImageUrl = () => {
              if (post.savedImage) return post.savedImage;
              if (post.media && post.media.length > 0) return post.media[0].url;
              return null;
            };
            const imageUrl = getImageUrl();
            return (
              <div key={post.id} className="notely-card rounded-notely-xl overflow-hidden shadow-notely-md hover:shadow-notely-lg transition-all duration-300 border-0 bg-notely-surface">
                <div className="h-48 relative overflow-hidden">
                  {imageUrl ? (
                    <img src={imageUrl} alt="Post thumbnail" className="w-full h-full object-cover" />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center notely-surface">
                      <div className="text-6xl opacity-20 notely-text-muted">
                        {post.platform === 'X/Twitter' && '🐦'}
                        {post.platform === 'Instagram' && '📸'}
                        {post.platform === 'LinkedIn' && '💼'}
                        {String(post.platform) === 'Pinterest' && '📌'}
                        {post.platform === 'Reddit' && '🔗'}
                        {post.platform === 'Web' && '🌐'}
                        {!post.platform && '💡'}
                      </div>
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-semibold notely-text-primary mb-3 line-clamp-2 leading-tight">
                    {post.title || post.content?.substring(0, 80) + '...'}
                  </h3>
                  <button
                    onClick={() => onOpenPost && onOpenPost(post.id)}
                    className="w-full bg-notely-surface/50 dark:bg-notely-surface/30 text-notely-text-primary border border-notely-border/30 dark:border-notely-border/20 rounded-lg px-4 py-2 text-sm font-medium hover:bg-notely-surface/70 dark:hover:bg-notely-surface/50 hover:border-notely-border/50 dark:hover:border-notely-border/40 transition-all duration-200 flex items-center justify-center gap-2 hover:scale-[1.02]"
                  >
                    <span className="text-lg">+</span>
                    Explore
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default SmartWorkspace;
