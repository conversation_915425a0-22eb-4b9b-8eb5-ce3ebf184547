import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';
import { Post } from '../types';
import { toast } from '../utils/toast';
import {
  ChatMessage,
  ChatConversation,
  ChatHistoryItem,
  getChatHistory,
  getChatConversation,
  saveChatConversation,
  createNewConversation,
  generateConversationTitle,
  deleteChatConversation,
  clearAllChatHistory
} from '../services/chatStorageService';

interface ChatWithPostsProps {
  posts: Post[];
  className?: string;
  onOpenPost?: (postId: string) => void;
}

const ChatWithPosts: React.FC<ChatWithPostsProps> = ({ posts, className = '', onOpenPost }) => {
  const [currentConversation, setCurrentConversation] = useState<ChatConversation>(() => {
    return createNewConversation();
  });
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);
  const [showAllHistory, setShowAllHistory] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Update welcome message when posts are loaded
  useEffect(() => {
    // Only update if this is a new conversation (only has 1 or 0 messages)
    if (currentConversation.messages.length <= 1) {
      const newConv = createNewConversation();
      newConv.messages = [{
        id: '1',
        type: 'ai',
        content: `You've saved ${posts.length} posts. Want to analyze, organize, or create something new? I'm ready.`,
        timestamp: new Date()
      }];
      setCurrentConversation(newConv);
    }
  }, [posts.length]);

  // AI Assistant actions - 10 total with expandable sub-prompts
  const smartActions = [
    // Original 5 actions (keeping existing functionality)
    {
      id: 'analyze-patterns',
      icon: '📊',
      label: 'Analyze patterns',
      subPrompts: [
        { label: 'What patterns do you see in my content?', prompt: 'Analyze the patterns and trends in my saved posts. What themes and topics emerge most frequently?' },
        { label: 'How have my interests evolved?', prompt: 'How have my interests and focus areas changed over time based on my saved content?' },
        { label: 'What are my content habits?', prompt: 'Analyze my content saving habits and patterns. When and what type of content do I save most?' }
      ]
    },
    {
      id: 'extract-text',
      icon: '⚡',
      label: 'Extract text',
      subPrompts: [
        { label: 'Extract key quotes and insights', prompt: 'Extract the most important quotes, insights, and key takeaways from my saved posts.' },
        { label: 'List actionable tips', prompt: 'List all actionable tips, frameworks, and advice from my saved content.' },
        { label: 'Find important statistics', prompt: 'Extract any statistics, data points, or research findings mentioned in my posts.' }
      ]
    },
    {
      id: 'organize-smarter',
      icon: '📚',
      label: 'Organize smarter',
      subPrompts: [
        { label: 'Suggest better categories', prompt: 'Analyze my saved posts and suggest better categories or subcategories to organize them.' },
        { label: 'Recommend new tags', prompt: 'Suggest new tags or labels that would help me organize my content more effectively.' },
        { label: 'Group similar content', prompt: 'Group my saved posts by similar themes, topics, or content types.' }
      ]
    },
    {
      id: 'show-tools-resources',
      icon: '🛠️',
      label: 'Show tools & resources',
      subPrompts: [
        { label: 'Extract all mentioned tools', prompt: 'Extract all tools, platforms, apps, and software mentioned in my saved posts and organize them by category.' },
        { label: 'List useful resources', prompt: 'List the most useful resources, links, and references found in my saved content.' },
        { label: 'Find learning materials', prompt: 'Find all educational resources, courses, books, and learning materials mentioned in my posts.' }
      ]
    },
    {
      id: 'create-content',
      icon: '✍️',
      label: 'Create new content',
      subPrompts: [
        { label: 'Generate post ideas', prompt: 'Help me generate new social media post, tweet, or blog ideas based on my saved content.' },
        { label: 'Draft a Twitter thread', prompt: 'Create a Twitter thread or LinkedIn post using insights from my saved content.' },
        { label: 'Write a summary article', prompt: 'Help me write a summary article or blog post combining insights from multiple saved posts.' }
      ]
    },
    // New 5 actions (expanding the functionality)
    {
      id: 'find-connections',
      icon: '🔗',
      label: 'Find connections',
      subPrompts: [
        { label: 'Connect related posts', prompt: 'Find connections and relationships between my saved posts. Which posts complement each other?' },
        { label: 'Identify knowledge gaps', prompt: 'Based on my saved content, what knowledge gaps or missing pieces should I look for?' },
        { label: 'Suggest follow-up reading', prompt: 'Suggest what I should read or explore next based on my current saved content.' }
      ]
    },
    {
      id: 'track-learning',
      icon: '🎯',
      label: 'Track learning',
      subPrompts: [
        { label: 'Summarize what I\'ve learned', prompt: 'Summarize the key things I\'ve learned from my recent saved posts.' },
        { label: 'Track my progress', prompt: 'How has my knowledge and understanding progressed in different topics based on my saved content?' },
        { label: 'Identify learning goals', prompt: 'Based on my saved content, what learning goals or objectives should I focus on next?' }
      ]
    },
    {
      id: 'generate-insights',
      icon: '💡',
      label: 'Generate insights',
      subPrompts: [
        { label: 'Provide unique perspectives', prompt: 'What unique insights or perspectives can you derive from combining multiple posts in my collection?' },
        { label: 'Spot emerging trends', prompt: 'What emerging trends or patterns do you notice in my recently saved content?' },
        { label: 'Make predictions', prompt: 'Based on the trends in my saved content, what predictions can you make about future developments?' }
      ]
    },
    {
      id: 'compare-analyze',
      icon: '⚖️',
      label: 'Compare & analyze',
      subPrompts: [
        { label: 'Compare different viewpoints', prompt: 'Compare different viewpoints or opinions on similar topics found in my saved posts.' },
        { label: 'Analyze content quality', prompt: 'Analyze the quality and credibility of sources in my saved content.' },
        { label: 'Compare time periods', prompt: 'Compare the topics and themes I saved this month versus previous months.' }
      ]
    },
    {
      id: 'actionable-next-steps',
      icon: '🚀',
      label: 'Actionable next steps',
      subPrompts: [
        { label: 'Suggest immediate actions', prompt: 'Based on my saved content, what immediate actions or steps should I take?' },
        { label: 'Create an action plan', prompt: 'Help me create an action plan based on the insights and advice in my saved posts.' },
        { label: 'Prioritize my to-dos', prompt: 'Help me prioritize tasks and actions based on the content I\'ve been saving.' }
      ]
    }
  ];

  // State for prompt suggestions
  const [showAllSuggestions, setShowAllSuggestions] = useState(false);

  // Get all prompt suggestions from smartActions
  const getAllPromptSuggestions = () => {
    return smartActions.flatMap(action =>
      action.subPrompts.map(sub => sub.prompt)
    );
  };

  // Get initial 3 prompt suggestions
  const getInitialPromptSuggestions = () => {
    return [
      "What topics do I talk about most?",
      "Summarize the most valuable post.",
      "What's my most saved keyword?"
    ];
  };

  const [isLoadingConversation, setIsLoadingConversation] = useState(false);

  const scrollToBottom = () => {
    if (isLoadingConversation || currentConversation.messages.length === 0) return;
    // Prefer scrolling the internal container to the bottom instead of using scrollIntoView
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentConversation.messages]);

  // Load chat history on component mount
  useEffect(() => {
    const loadChatHistory = async () => {
      const history = await getChatHistory();
      setChatHistory(history);
    };
    loadChatHistory();
  }, []);

  // Ensure chat input does not regain focus when posts change (e.g., category switch)
  useEffect(() => {
    if (inputRef.current && document.activeElement === inputRef.current) {
      inputRef.current.blur();
    }
  }, [posts]);

  // Save conversation when messages change
  useEffect(() => {
    if (currentConversation.messages.length > 1) { // Don't save initial AI message only
      const saveConversation = async () => {
        const updatedConversation = {
          ...currentConversation,
          updatedAt: new Date()
        };
        await saveChatConversation(updatedConversation);

        // Refresh chat history
        const history = await getChatHistory();
        setChatHistory(history);
      };
      saveConversation();
    }
  }, [currentConversation.messages]);

  const handleSendMessage = async (messageContent?: string) => {
    const content = messageContent || inputValue.trim();
    if (!content || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: content,
      timestamp: new Date()
    };

    // Capture conversation history before adding the new user message
    const conversationHistory = currentConversation.messages.slice(-20);

    // Update conversation title if this is the first user message
    let updatedConversation = { ...currentConversation };
    if (currentConversation.messages.length === 1) { // Only initial AI message
      updatedConversation.title = generateConversationTitle(content);
    }

    updatedConversation.messages = [...updatedConversation.messages, userMessage];
    setCurrentConversation(updatedConversation);
    setInputValue('');
    setIsLoading(true);

    try {
      const aiResponse = await generateRealAIResponse(content, posts, conversationHistory);

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      };

      updatedConversation.messages = [...updatedConversation.messages, aiMessage];
      setCurrentConversation(updatedConversation);
    } catch (error) {
      console.error('Error generating AI response:', error);
      toast.error('Failed to get AI response. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle smart action button clicks
  const handleSmartAction = (prompt: string) => {
    setInputValue(prompt);
    handleSendMessage(prompt);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };



  const generateRealAIResponse = async (userInput: string, posts: Post[], history: ChatMessage[]): Promise<string> => {
    try {
      // Get authentication token
      let token: string | null = null;
      try {
        const result = await chrome.storage.local.get(['authToken']);
        token = result.authToken || null;
      } catch (error) {
        console.warn('[ChatWithPosts] Could not get auth token:', error);
      }

      if (!token) {
        return "Authentication required. Please log in to use AI chat.";
      }

      // Call secure backend endpoint
      const response = await fetch('https://api.notely.social/api/posts/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          message: userInput,
          posts: posts,
          messages: history
        })
      });

      if (!response.ok) {
        if (response.status === 401) {
          return "Authentication expired. Please log in again.";
        }
        throw new Error(`AI chat request failed: ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.data && result.data.response) {
        return result.data.response;
      } else {
        throw new Error('Invalid response from AI service');
      }
    } catch (error) {
      console.error('AI Response Error:', error);
      return "I'm having trouble connecting to the AI service right now. Please try again later.";
    }
  };

  // Handle chat history item click with smooth transition
  const handleChatHistoryClick = async (id: string) => {
    try {
      const conversation = await getChatConversation(id);
      if (conversation) {
        // Set loading flag to prevent auto-scroll
        setIsLoadingConversation(true);

        // Add a smooth transition effect
        const chatContainer = document.querySelector('.chat-messages-container');
        if (chatContainer) {
          chatContainer.classList.add('opacity-50');

          setTimeout(() => {
            setCurrentConversation(conversation);
            chatContainer.classList.remove('opacity-50');
            // Reset loading flag after transition
            setTimeout(() => {
              setIsLoadingConversation(false);
            }, 100);
          }, 150);
        } else {
          setCurrentConversation(conversation);
          setIsLoadingConversation(false);
        }
      }
    } catch (error) {
      console.error('Error loading conversation:', error);
      toast.error('Failed to load conversation.');
      setIsLoadingConversation(false);
    }
  };

  // Start new conversation
  const handleNewConversation = () => {
    const newConv = createNewConversation();
    newConv.messages = [{
      id: '1',
      type: 'ai',
      content: `You've saved ${posts.length} posts. Want to analyze, organize, or create something new? I'm ready.`,
      timestamp: new Date()
    }];
    setCurrentConversation(newConv);
  };

  // Delete individual conversation
  const handleDeleteConversation = async (id: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent clicking the conversation
    try {
      await deleteChatConversation(id);

      // If we deleted the current conversation, start a new one
      if (currentConversation.id === id) {
        handleNewConversation();
      }

      // Refresh chat history
      const history = await getChatHistory();
      setChatHistory(history);
      toast.success('Conversation deleted');
    } catch (error) {
      console.error('Error deleting conversation:', error);
      toast.error('Failed to delete conversation');
    }
  };

  // Clear all chat history
  const handleClearAllHistory = async () => {
    if (window.confirm('Are you sure you want to delete all chat history? This cannot be undone.')) {
      try {
        await clearAllChatHistory();
        setChatHistory([]);
        handleNewConversation();
        toast.success('All chat history cleared');
      } catch (error) {
        console.error('Error clearing chat history:', error);
        toast.error('Failed to clear chat history');
      }
    }
  };

  // Format AI response for better readability and handle post references
  const formatAIResponse = (content: string): JSX.Element[] => {
    // Special handling for intro message
    if (content.includes("You've saved") && content.includes("posts. Want to analyze, organize, or create something new? I'm ready.")) {
      return [
        <p key="intro" className="text-sm leading-relaxed text-notely-text-primary">
          You've saved <span className="font-bold text-notely-lavender bg-notely-lavender/10 px-2 py-1 rounded-md">{posts.length}</span> posts. Want to analyze, organize, or create something new? I'm ready.
        </p>
      ];
    }

    // Clean up the content by removing asterisks and standardizing formatting
    const cleanContent = content
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove ** markers
      .replace(/\*(.*?)\*/g, '$1')     // Remove * markers
      .replace(/\n{3,}/g, '\n\n');     // Standardize spacing

    // Function to handle post references in text
    const processTextWithPostRefs = (text: string): (string | JSX.Element)[] => {
      const parts: (string | JSX.Element)[] = [];
      const postRefRegex = /\[POST:([^\]]+)\]/g;
      let lastIndex = 0;
      let match;

      while ((match = postRefRegex.exec(text)) !== null) {
        // Add text before the reference
        if (match.index > lastIndex) {
          parts.push(text.slice(lastIndex, match.index));
        }

        // Add the clickable post reference
        const postId = match[1];
        const referencedPost = posts.find(p => p.id === postId);

        if (referencedPost && onOpenPost) {
          parts.push(
            <button
              key={`post-ref-${postId}-${match.index}`}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onOpenPost(referencedPost.id);
              }}
              className="inline-flex items-center mx-0.5 px-1.5 py-0.5 text-xs bg-notely-surface hover:bg-notely-surface/80 dark:bg-notely-surface/60 dark:hover:bg-notely-surface/80 text-notely-text-secondary hover:text-notely-text-primary border border-notely-border/10 dark:border-notely-border-dark/20 hover:border-notely-border/20 dark:hover:border-notely-border-dark/30 rounded transition-colors cursor-pointer group"
              title={`View ${referencedPost.platform} post by ${referencedPost.author || referencedPost.authorName}`}
            >
              <svg className="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              <span className="truncate">Source Post</span>
            </button>
          );
        } else {
          // If post not found, just show text
          parts.push(`[Source Post]`);
        }

        lastIndex = postRefRegex.lastIndex;
      }

      // Add remaining text
      if (lastIndex < text.length) {
        parts.push(text.slice(lastIndex));
      }

      return parts;
    };

    // Split content into sections and format properly
    const sections = cleanContent.split(/\n\n|\d+\.\s|-\s|•\s/).filter(section => section.trim());
    const elements: JSX.Element[] = [];

    sections.forEach((section, index) => {
      const trimmedSection = section.trim();
      if (!trimmedSection) return;

      // Check if this looks like a numbered list item
      if (/^\d+\./.test(content) && content.includes(trimmedSection)) {
        const match = content.match(new RegExp(`(\\d+)\\. ${trimmedSection.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`));
        if (match) {
          elements.push(
            <div key={index} className="flex items-start space-x-3 mb-3">
              <span className="flex-shrink-0 w-6 h-6 bg-notely-surface dark:bg-notely-surface/30 text-notely-text-primary dark:text-notely-text-primary rounded-full flex items-center justify-center text-xs font-medium">
                {match[1]}
              </span>
              <div className="flex-1 text-sm leading-relaxed text-notely-text-primary">
                {processTextWithPostRefs(trimmedSection)}
              </div>
            </div>
          );
          return;
        }
      }

      // Check if this looks like a bullet point
      if (/^[\*\-•]/.test(trimmedSection) || content.includes(`• ${trimmedSection}`) || content.includes(`* ${trimmedSection}`)) {
        elements.push(
          <div key={index} className="flex items-start space-x-3 mb-3">
            <span className="flex-shrink-0 w-1.5 h-1.5 bg-notely-text-primary/70 rounded-full mt-2"></span>
            <div className="flex-1 text-sm leading-relaxed text-notely-text-primary">
              {processTextWithPostRefs(trimmedSection.replace(/^[\*\-•]\s*/, ''))}
            </div>
          </div>
        );
        return;
      }

      // Check if this looks like a heading
      if (trimmedSection.length < 100 && (
        /^(Here are|Key points|Summary|Analysis|Overview|Main themes|Topics|Patterns|Insights):/i.test(trimmedSection) ||
        trimmedSection.endsWith(':')
      )) {
        elements.push(
          <h4 key={index} className="font-medium text-notely-text-primary mb-3 text-sm">
            {processTextWithPostRefs(trimmedSection)}
          </h4>
        );
        return;
      }

      // Regular paragraph
      if (trimmedSection.length > 20) {
        elements.push(
          <p key={index} className="mb-3 text-sm leading-relaxed text-notely-text-primary last:mb-0">
            {processTextWithPostRefs(trimmedSection)}
          </p>
        );
      }
    });

    return elements.length > 0 ? elements : [
      <p key="fallback" className="text-sm leading-relaxed text-notely-text-primary">
        {processTextWithPostRefs(content)}
      </p>
    ];
  };

  // Add typing animation component
  const TypingAnimation = () => (
    <div className="flex space-x-1 py-2">
      <div className="w-1.5 h-1.5 bg-notely-text-muted/70 rounded-full animate-bounce"></div>
      <div className="w-1.5 h-1.5 bg-notely-text-muted/70 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      <div className="w-1.5 h-1.5 bg-notely-text-muted/70 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
    </div>
  );

  return (
    <div className={`${className} w-full`}>
      {/* New Layout: Chat (1/2) + Sidebar (1/2) with equal heights */}
      <div className="flex flex-col lg:flex-row gap-6 h-[600px]">
        {/* Left: Chat Interface - 1/2 width */}
        <div className="w-full lg:w-1/2 flex-shrink-0">
          {/* Clean Chat Interface - Full Height */}
          <div className="notely-card bg-notely-card rounded-notely-xl border border-notely-border/10 dark:border-notely-border-dark/20 overflow-hidden shadow-notely-md hover:shadow-notely-lg notely-filter-transition flex flex-col h-full">
            {/* Messages Area - Takes remaining space */}
            <div ref={messagesContainerRef} className="chat-messages-container flex-1 overflow-y-auto px-6 py-4 space-y-4 notely-filter-transition transition-opacity duration-150">
              {currentConversation.messages.map((message) => (
                <div key={message.id} className="space-y-4">
                  {message.type === 'ai' && (
                    <div className="flex items-start space-x-4">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 border border-notely-border/5 dark:border-purple-500/30 shadow-notely-xs flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-sm">🧠</span>
                      </div>
                      <div className="flex-1 space-y-2 bg-notely-surface/40 rounded-xl p-4">
                        <div className="text-notely-text-primary leading-relaxed">
                          {formatAIResponse(message.content)}
                        </div>
                      </div>
                    </div>
                  )}

                  {message.type === 'user' && (
                    <div className="flex items-start space-x-4 justify-end">
                      <div className="flex-1 space-y-2 text-right max-w-[80%]">
                        <div className="inline-block bg-notely-lavender text-white rounded-xl px-4 py-3 shadow-notely-sm">
                          <p className="leading-relaxed text-sm">{message.content}</p>
                        </div>
                      </div>
                      <div className="w-8 h-8 rounded-full bg-notely-surface border border-notely-border/10 dark:border-notely-border-dark/20 shadow-notely-xs flex items-center justify-center flex-shrink-0">
                        <span className="text-notely-text-primary text-xs font-medium">You</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {isLoading && (
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm">🧠</span>
                  </div>
                  <div className="flex-1">
                    <TypingAnimation />
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input Area - Consistent with Notely Design */}
            <div className="px-6 py-4 border-t border-notely-border/10 dark:border-notely-border-dark/20">
              <div className="flex w-full gap-3 items-center">
                <div className="flex items-center w-full bg-notely-surface/80 hover:bg-notely-surface focus-within:bg-notely-surface border border-notely-border/20 dark:border-notely-border-dark/30 rounded-xl px-4 py-3 text-notely-text-primary placeholder-notely-text-muted/70 focus-within:ring-2 focus-within:ring-notely-lavender/50 focus-within:border-notely-lavender shadow-notely-sm hover:shadow-notely-md notely-filter-transition">
                  <svg className="w-5 h-5 mr-3 text-notely-text-muted shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-4l-4 4-4-4z" />
                  </svg>
                  <input
                    ref={inputRef}
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Hi! Ask me anything about your posts - I'm here to help! 👋"
                    className="w-full bg-transparent focus:outline-none"
                    disabled={isLoading}
                  />
                </div>
                <button
                  onClick={() => handleSendMessage()}
                  disabled={!inputValue.trim() || isLoading}
                  className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-xl px-5 py-3 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-notely-sm hover:shadow-notely-md notely-filter-transition flex items-center justify-center gap-2 font-medium"
                >
                  <span>Send</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right: Sidebar - 1/2 width with two columns */}
        <div className="w-full lg:w-1/2 flex-shrink-0 h-full">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4 h-full">
            {/* AI Assistant Smart Actions */}
            <div className="notely-card bg-notely-card rounded-notely-xl shadow-notely-md hover:shadow-notely-lg notely-filter-transition px-4 py-4 overflow-hidden border border-notely-border/10 dark:border-notely-border-dark/20 h-full flex flex-col">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-semibold text-notely-text-primary flex items-center">
                  <span className="mr-2">🧠</span> Nelo
                </h2>
                <button
                  onClick={handleNewConversation}
                  className="text-notely-text-muted hover:text-notely-lavender transition-colors"
                  title="New Conversation"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>
              <p className="text-notely-text-secondary text-xs mb-4">Hi, I'm Nelo – your assistant inside Notely. I've got access to all {posts.length} of your saved posts. How can I assist you today?</p>

              {/* Start here helper text */}
              <div className="mb-3">
                <p className="text-xs text-notely-text-muted">Start here:</p>
              </div>

              {/* Four Main Action Buttons - Compact 2x2 Grid */}
              <div className="grid grid-cols-2 gap-3 mb-4">
                {smartActions.slice(0, 4).map((action) => (
                  <button
                    key={action.id}
                    onClick={() => handleSmartAction(action.subPrompts[0].prompt)}
                    className="flex flex-col items-center gap-1.5 p-3 rounded-lg bg-notely-surface hover:bg-notely-surface/80 dark:bg-notely-surface/60 dark:hover:bg-notely-surface/80 border border-notely-border/10 dark:border-notely-border-dark/20 hover:border-notely-border/20 dark:hover:border-notely-border-dark/30 text-notely-text-primary transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-notely-lavender/30 focus:ring-offset-1 text-xs font-medium min-h-[60px] justify-center"
                  >
                    <span className="text-sm">{action.icon}</span>
                    <span className="text-center leading-tight">{action.label}</span>
                  </button>
                ))}
              </div>

              {/* Show suggestions by default with option to show more */}
              <div className="flex-1 overflow-y-auto">
                <div className="mb-3">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-sm">💡</span>
                    <span className="text-xs font-medium text-notely-text-primary">Try asking:</span>
                  </div>

                  <div className="space-y-1.5">
                    {(showAllSuggestions ? getAllPromptSuggestions() : getAllPromptSuggestions().slice(0, 3)).map((suggestion, idx) => (
                      <button
                        key={idx}
                        onClick={() => handleSmartAction(suggestion)}
                        className="w-full text-left text-xs p-2 rounded-md bg-notely-surface/40 hover:bg-notely-surface/60 dark:bg-notely-surface/30 dark:hover:bg-notely-surface/50 text-notely-text-secondary hover:text-notely-text-primary transition-colors border border-notely-border/5 hover:border-notely-border/15 dark:border-notely-border-dark/10 dark:hover:border-notely-border-dark/20"
                      >
                        "{suggestion}"
                      </button>
                    ))}
                  </div>
                </div>

                {/* Show more/less button */}
                {getAllPromptSuggestions().length > 3 && (
                  <div className="mt-3 pt-2 border-t border-notely-border/10 dark:border-notely-border-dark/20">
                    <button
                      onClick={() => setShowAllSuggestions(!showAllSuggestions)}
                      className="w-full text-xs text-notely-text-muted hover:text-notely-text-primary text-center py-1 hover:bg-notely-surface/20 rounded transition-colors"
                    >
                      {showAllSuggestions
                        ? 'Show less'
                        : `+${getAllPromptSuggestions().length - 3} more suggestions`
                      }
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Chat History List - Compact */}
            <div className="notely-card bg-notely-card rounded-notely-xl shadow-notely-md hover:shadow-notely-lg notely-filter-transition px-4 py-4 overflow-hidden border border-notely-border/10 dark:border-notely-border-dark/20 h-full flex flex-col">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-sm font-semibold text-notely-text-primary">Chat History</h2>
                {chatHistory.length > 0 && (
                  <button
                    onClick={handleClearAllHistory}
                    className="text-notely-text-muted hover:text-red-500 transition-colors text-xs"
                    title="Clear All History"
                    aria-label="Clear all chat history"
                  >
                    <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                )}
              </div>
              {chatHistory.length > 0 ? (
                <div className="space-y-1 flex-1 overflow-y-auto">
                  {(showAllHistory ? chatHistory : chatHistory.slice(0, 6)).map(item => (
                    <div
                      key={item.id}
                      className={`p-2 rounded-md cursor-pointer group transition-colors border ${currentConversation.id === item.id
                          ? 'bg-notely-lavender/10 border-notely-lavender/20'
                          : 'bg-notely-surface/40 hover:bg-notely-surface/60 dark:bg-notely-surface/30 dark:hover:bg-notely-surface/50 border-notely-border/5 hover:border-notely-border/15 dark:border-notely-border-dark/10 dark:hover:border-notely-border-dark/20'
                        }`}
                    >
                      <div className="flex flex-col" onClick={() => handleChatHistoryClick(item.id)}>
                        <div className="flex items-center justify-between mb-0.5">
                          <span className="text-notely-text-muted text-xs">{item.date}</span>
                          <div className="flex items-center space-x-1">
                            <span className="text-notely-text-muted text-xs">{item.messageCount}</span>
                            <button
                              onClick={(e) => handleDeleteConversation(item.id, e)}
                              className="opacity-0 group-hover:opacity-100 text-notely-text-muted hover:text-red-500 transition-all"
                              title="Delete conversation"
                              aria-label="Delete conversation"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <span className="text-notely-text-secondary group-hover:text-notely-text-primary notely-filter-transition text-xs truncate leading-tight w-full block">
                          {item.title}
                        </span>
                        {item.lastMessage && (
                          <span className="text-notely-text-muted text-xs truncate leading-tight w-full block pr-2 overflow-hidden whitespace-nowrap text-overflow-ellipsis">
                            {item.lastMessage}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                  {chatHistory.length > 6 && (
                    <div className="pt-1 border-t border-notely-border/10 dark:border-notely-border-dark/20 mt-2">
                      <button
                        onClick={() => setShowAllHistory(!showAllHistory)}
                        className="w-full text-xs text-notely-text-muted hover:text-notely-text-primary text-center py-1 hover:bg-notely-surface/20 rounded transition-colors"
                      >
                        {showAllHistory
                          ? 'Show less'
                          : `+${chatHistory.length - 6} more conversations`
                        }
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <p className="text-notely-text-muted text-xs text-center">No conversations yet.<br />Start chatting to see history here.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatWithPosts;
