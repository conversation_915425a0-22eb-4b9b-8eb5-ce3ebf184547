import React, { useState, useEffect, useRef, useCallback } from 'react';
import { getUserPlan } from '../services/authService';
import ProxyImage from './ProxyImage';
import { ImageSwiper } from './ui/image-swiper';
import { PostContentRenderer } from './PostContentRenderer';
import MultiItemInput from './MultiItemInput';
import CategoryTagToggle from './CategoryTagToggle';
// Import formatForDisplay for proper tag and category formatting
import { formatForDisplay, formatForStorage } from '../utils/formatUtils';
import { updatePostDetails, getAllCategories, getAllTags, saveAllCategories, saveAllTags } from '../storage';
import { getCurrentTheme, onThemeChange } from '../utils/themeUtils';
import { useAIEnrichment } from '../hooks/useAIEnrichment';
import { useTranslation } from '../hooks/useTranslation';
import { MediaItem } from '../types';
import '../styles/platformIcons.css';

// --- Icon Stubs (replace with actual icons or imports) ---
const IconX: React.FC<{ className?: string }> = ({ className = "w-6 h-6" }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);
const IconClipboard: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={1.5}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
  </svg>
);
// Removed unused IconPlus component

const IconSave: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
  </svg>
);

// Removed unused IconSparkles component

const IconRefresh: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={1.5}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
  </svg>
);

// Engagement Icons - Same as dashboard
const CommentIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
  </svg>
);

const ShareIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M17 1l4 4-4 4"></path>
    <path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
    <path d="M7 23l-4-4 4-4"></path>
    <path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
  </svg>
);

const HeartIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
  </svg>
);



// --- Platform Specific SVG Icons ---
const XLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
  </svg>
);

const LinkedInLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M20.5 2h-17A1.5 1.5 0 002 3.5v17A1.5 1.5 0 003.5 22h17a1.5 1.5 0 001.5-1.5v-17A1.5 1.5 0 0020.5 2zM8 19H5v-9h3zM6.5 8.25A1.75 1.75 0 118.25 6.5 1.75 1.75 0 016.5 8.25zM19 19h-3v-4.74c0-1.42-.6-1.93-1.38-1.93-.78 0-1.22.52-1.42 1.02-.08.18-.09.42-.09.66V19h-3V10h3v1.32c.41-.64 1.14-1.32 2.67-1.32 1.98 0 3.23 1.29 3.23 4.01z"></path>
  </svg>
);

const RedditLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-current ${className}`} viewBox="0 0 16 16">
    <path d="M6.167 8a.83.83 0 0 0-.83.83c0 .459.372.84.83.831a.831.831 0 0 0 0-1.661m1.843 3.647c.315 0 1.403-.038 1.976-.611a.23.23 0 0 0 0-.306.213.213 0 0 0-.306 0c-.353.363-1.126.487-1.67.487-.545 0-1.308-.124-1.671-.487a.213.213 0 0 0-.306 0 .213.213 0 0 0 0 .306c.564.563 1.652.61 1.977.61zm.992-2.807c0 .458.373.83.831.83s.83-.381.83-.83a.831.831 0 0 0-1.66 0z"/>
    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0m-3.828-1.165c-.315 0-.602.124-.812.325-.801-.573-1.9-.945-3.121-.993l.534-2.501 1.738.372a.83.83 0 1 0 .83-.869.83.83 0 0 0-.744.468l-1.938-.41a.2.2 0 0 0-.153.028.2.2 0 0 0-.086.134l-.592 2.788c-1.24.038-2.358.41-3.17.992-.21-.2-.496-.324-.81-.324a1.163 1.163 0 0 0-.478 2.224q-.03.17-.029.353c0 1.795 2.091 3.256 4.669 3.256s4.668-1.451 4.668-3.256c0-.114-.01-.238-.029-.353.401-.181.688-.592.688-1.069 0-.65-.525-1.165-1.165-1.165"/>
  </svg>
);

const InstagramLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <div className={`${className} notely-platform-icon notely-platform-icon-instagram`}>
    {/* Light mode version with gradient */}
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="w-full h-full instagram-light-mode">
      <defs>
        <linearGradient id="instagramGradient" x1="0%" y1="100%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#feda75" />
          <stop offset="20%" stopColor="#fa7e1e" />
          <stop offset="40%" stopColor="#d62976" />
          <stop offset="60%" stopColor="#962fbf" />
          <stop offset="80%" stopColor="#4f5bd5" />
        </linearGradient>
      </defs>
      <rect width="20" height="20" x="2" y="2" rx="5" fill="url(#instagramGradient)"></rect>
      <path d="M12 7.5C9.5 7.5 7.5 9.5 7.5 12C7.5 14.5 9.5 16.5 12 16.5C14.5 16.5 16.5 14.5 16.5 12C16.5 9.5 14.5 7.5 12 7.5ZM12 15C10.3 15 9 13.7 9 12C9 10.3 10.3 9 12 9C13.7 9 15 10.3 15 12C15 13.7 13.7 15 12 15Z" fill="white"></path>
      <circle cx="17" cy="7" r="1" fill="white"></circle>
    </svg>
    {/* Dark mode version with outline */}
    <svg className="w-full h-full instagram-dark-mode" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
      <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
      <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
      <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
    </svg>
  </div>
);

const FacebookLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
 <svg viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-1.7c-.64 0-1.3.27-1.3.7V12h3l-.5 3h-2.5v6.8c4.56-.93 8-4.96 8-9.8z"></path>
  </svg>
);

const PinterestLogo: React.FC<{ className?: string }> = ({ className = "w-5 h-5" }) => (
  <svg className={`fill-current ${className}`} viewBox="0 0 579.148 579.148" xmlns="http://www.w3.org/2000/svg" xmlSpace="preserve">
      <path d="M434.924,38.847C390.561,12.954,342.107,0.01,289.574,0.01c-52.54,0-100.992,12.944-145.356,38.837
        C99.854,64.741,64.725,99.87,38.837,144.228C12.944,188.597,0,237.049,0,289.584c0,58.568,15.955,111.732,47.883,159.486
        c31.922,47.768,73.771,83.08,125.558,105.949c-1.01-26.896,0.625-49.137,4.902-66.732l37.326-157.607
        c-6.285-12.314-9.425-27.645-9.425-45.999c0-21.365,5.404-39.217,16.212-53.538c10.802-14.333,24.003-21.5,39.59-21.5
        c12.564,0,22.246,4.143,29.034,12.448c6.787,8.292,10.184,18.727,10.184,31.292c0,7.797-1.451,17.289-4.334,28.47
        c-2.895,11.187-6.665,24.13-11.31,38.837c-4.651,14.701-7.98,26.451-9.994,35.252c-3.525,15.33-0.63,28.463,8.672,39.4
        c9.295,10.936,21.616,16.4,36.952,16.4c26.898,0,48.955-14.951,66.176-44.865c17.217-29.914,25.826-66.236,25.826-108.973
        c0-32.925-10.617-59.701-31.859-80.312c-21.242-20.606-50.846-30.918-88.795-30.918c-42.486,0-76.862,13.642-103.123,40.906
        c-26.267,27.277-39.401,59.896-39.401,97.84c0,22.625,6.414,41.609,19.229,56.941c4.272,5.029,5.655,10.428,4.149,16.205
        c-0.508,1.512-1.511,5.281-3.017,11.309c-1.505,6.029-2.515,9.934-3.017,11.689c-2.014,8.049-6.787,10.564-14.333,7.541
        c-19.357-8.043-34.064-21.99-44.113-41.85c-10.055-19.854-15.08-42.852-15.08-68.996c0-16.842,2.699-33.685,8.103-50.527
        c5.404-16.842,13.819-33.115,25.264-48.832c11.432-15.698,25.135-29.596,41.102-41.659c15.961-12.069,35.38-21.738,58.256-29.04
        c22.871-7.283,47.51-10.93,73.904-10.93c35.693,0,67.744,7.919,96.146,23.751c28.402,15.839,50.086,36.329,65.043,61.463
        c14.951,25.135,22.436,52.026,22.436,80.692c0,37.705-6.541,71.641-19.607,101.807c-13.072,30.166-31.549,53.855-55.43,71.072
        c-23.887,17.215-51.035,25.826-81.445,25.826c-15.336,0-29.664-3.58-42.986-10.748c-13.33-7.166-22.503-15.648-27.528-25.453
        c-11.31,44.486-18.097,71.018-20.361,79.555c-4.78,17.852-14.584,38.457-29.413,61.836c26.897,8.043,54.296,12.062,82.198,12.062
        c52.534,0,100.987-12.943,145.35-38.83c44.363-25.895,79.492-61.023,105.387-105.393c25.887-44.365,38.838-92.811,38.838-145.352
        c0-52.54-12.951-100.985-38.838-145.355C514.422,99.87,479.287,64.741,434.924,38.847z"/>
  </svg>
);

// --- PlatformLogo Stub (replace with your actual PlatformLogo component) ---
type Platform = 'instagram' | 'x' | 'reddit' | 'linkedin' | 'pinterest' | 'X/Twitter' | 'facebook' | 'wisdom' | string; // Extend as needed

// Updated PlatformLogo component using SVGs
const PlatformLogo: React.FC<{ platform: Platform; className?: string }> = ({ platform, className = "w-6 h-6" }) => {
  const platformString = String(platform).toLowerCase();

  if (platformString.includes('x') || platformString.includes('twitter')) {
    return <XLogo className={`${className} notely-platform-icon`} />;
  }
  if (platformString.includes('linkedin')) {
    return <div className="text-[#0077b5] notely-platform-icon-linkedin"><LinkedInLogo className={`${className} notely-platform-icon`} /></div>;
  }
  if (platformString.includes('reddit')) {
    return <div className="text-[#FF4500] notely-platform-icon-reddit"><RedditLogo className={`${className} notely-platform-icon`} /></div>;
  }
  if (platformString.includes('instagram')) {
    return <InstagramLogo className={className} />;
  }
  if (platformString.includes('facebook')) {
    return <FacebookLogo className={className} />;
  }
  if (platformString.includes('pinterest')) {
    return <div className="text-[#E60023] notely-platform-icon-pinterest"><PinterestLogo className={`${className} notely-platform-icon`} /></div>;
  }
  if (platformString.includes('wisdom')) {
    return (
      <div className="text-purple-600 dark:text-purple-400" title="Daily Wisdom">
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
        </svg>
      </div>
    );
  }

  // Fallback for unknown platforms
  const logoChar = platformString.charAt(0).toUpperCase() || 'P';
  return (
    <div className={`flex items-center justify-center text-xs font-semibold rounded-sm bg-notely-surface text-notely-text-secondary ${className}`} title={String(platform)}>
      {logoChar}
    </div>
  );
};

// --- Data Types based on user's JSON schema ---
interface AIInSightData {
  sentiment: 'positive' | 'neutral' | 'negative';
  emoji: string;
  contextTags: string[];
}

export interface PostWithAIData {
  id: string;
  platform: Platform;
  mediaType: 'image' | 'video' | 'text';
  mediaUrl?: string;
  text?: string; // Content for text posts, or caption for media posts
  content?: string; // Alternative content field
  author: string;
  timestamp: string; // ISO
  stats?: { likes?: number; comments?: number; shares?: number };
  snapNote?: string | null; // AI-generated note
  notes?: string | null; // Personal user notes
  inSight?: AIInSightData | null;
  fastTake?: string | null;
  tags?: string[] | null;
  categories?: string[] | null;
  // Media array for carousel support
  media?: MediaItem[];
  // Thread-specific fields
  isThread?: boolean;
  threadId?: string;
  threadPosition?: number;
  threadLength?: number;
}

// --- Component Props ---
interface PostViewerFullScreenProps {
  post: PostWithAIData | null;
  onClose: () => void;
  onAddCategory: (postId: string, category: string) => void;
  onAddTag?: (postId: string, tag: string) => void;
  onRemoveTag?: (postId: string, tag: string) => void;
  onRemoveCategory?: (postId: string, category: string) => void; // Added for consistency if categories are removable
  onUpdateNotes?: (postId: string, notes: string) => void; // Added for notes functionality
  onUpdateCategories?: (postId: string, categories: string[]) => void; // Added for bulk category updates
  onUpdateTags?: (postId: string, tags: string[]) => void; // Added for bulk tag updates
  // Thread-specific props
  threadPosts?: PostWithAIData[]; // All posts in the thread (if this is a thread post)
  currentThreadIndex?: number; // Current post index in the thread
  onThreadNavigate?: (index: number) => void; // Navigate to different post in thread
}

// --- Chip Component ---
interface ChipProps {
  text: string;
  onRemove?: () => void;
  color?: string;
  className?: string;
  interactive?: boolean;
}

const Chip: React.FC<ChipProps> = ({ text, onRemove, color = 'bg-notely-surface text-notely-text-secondary', className = '', interactive = true }) => {
  // We can't use the hook here since this is a standalone component, so we'll keep the English text
  // In a real implementation, you'd want to pass the translation function as a prop or use a context
  return (
    <div
      className={`px-1.5 py-0.5 text-xs font-medium rounded flex items-center ${color} ${className} ${interactive && onRemove ? 'cursor-pointer hover:opacity-80' : 'cursor-default'}`}
      onClick={interactive && onRemove ? (e) => { e.stopPropagation(); onRemove(); } : undefined}
      title={interactive && onRemove ? `Remove ${text}` : text}
    >
      <span className="truncate">{text}</span>
      {interactive && onRemove && (
        <button
          type="button"
          className="ml-1 -mr-0.5 p-0.5 rounded-full hover:bg-notely-text-primary/10 focus:outline-none"
          aria-label={`Remove ${text}`}
        >
          <IconX className="w-3 h-3" />
        </button>
      )}
    </div>
  );
};

// --- Helper Functions ---
const formatTimestamp = (isoString: string): string => {
  if (!isoString) return "Unknown date";
  try {
    const date = new Date(isoString);
    if (isNaN(date.getTime())) return "Invalid date";
    const now = new Date();
    const diffInSeconds = Math.round((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s ago`;
    const diffInMinutes = Math.round(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.round(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.round(diffInHours / 24);
    if (diffInDays === 1) return `Yesterday`;
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  } catch (e) {
    console.error("Error formatting timestamp:", e);
    return "Date error";
  }
};

const formatStatNumber = (num?: number): string => {
  if (num === undefined || num === null) return '0';
  if (num < 1000) return num.toString();
  if (num < 1000000) return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
  return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
};

// --- Helper Functions ---
const isBase64Image = (url: string): boolean => {
  return url?.startsWith('data:image/');
};

// --- Main Component ---
const PostViewerFullScreen: React.FC<PostViewerFullScreenProps> = ({
  post,
  onClose,
  onAddTag, // Add this back
  onUpdateNotes,
  onUpdateCategories,
  onUpdateTags,
  threadPosts,
}) => {
  const isThreadMode = !!(threadPosts && threadPosts.length > 1);
  const displayPost = isThreadMode && threadPosts ? threadPosts[0] : post;
  
  const [localPost, setLocalPost] = useState<PostWithAIData | null>(displayPost);
  const [notes, setNotes] = useState<string>('');


  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveNotification, setSaveNotification] = useState<string>('');
  const [allCategories, setAllCategories] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);
  const [categoriesMessage, setCategoriesMessage] = useState<{text: string, type: 'success' | 'error'} | null>(null);
  const [tagsMessage, setTagsMessage] = useState<{text: string, type: 'success' | 'error'} | null>(null);
  const [userPlan, setUserPlan] = useState<{plan?: string}>({});
  const [copyNotification, setCopyNotification] = useState<string>('');
  const [isAnimating, setIsAnimating] = useState<boolean>(true);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const notesTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Translation hook
  const { t } = useTranslation();

  // AI Enrichment hook - using 'free' as fallback to avoid type errors
  const aiEnrichment = useAIEnrichment(userPlan?.plan === 'premium' ? 'premium' : 'free');
  const [isAIProcessing, setIsAIProcessing] = useState(false);
  const [aiError, setAIError] = useState<string | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(true); // Default to dark, will be updated by useEffect

  // Monitor theme changes using centralized utilities
  useEffect(() => {
    // Get initial theme using async method to respect stored preferences
    getCurrentTheme().then((theme) => {
      setIsDarkMode(theme === 'dark');
    });

    // Listen for theme changes
    const cleanup = onThemeChange((theme) => {
      setIsDarkMode(theme === 'dark');
    });

    return cleanup;
  }, []);

  // Handle close with animation - moved up and wrapped in useCallback
  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
    }, 300); // Match animation duration
  }, [onClose]);

  // Initialize user plan for usage stats display and load categories/tags
  useEffect(() => {
    const initializeData = async () => {
      try {
        const plan = await getUserPlan();
        // Set user plan data
        setUserPlan(plan ? { plan: plan } : {});

        // Load categories and tags for autocomplete
        const categories = await getAllCategories();
        const tags = await getAllTags();
        setAllCategories(categories);
        setAllTags(tags);

        // Initialize AI enrichment after user plan is loaded
        // Always check AI availability, even for free/no-plan users
        await aiEnrichment.checkAIAvailability();
      } catch (error) {
        console.error('Error initializing data:', error);
      }
    };

    if (displayPost) {
      initializeData();
      // Initialize notes with existing personal notes (not AI snapNote)
      const initialNotes = displayPost.notes || '';
      console.log('[NOTES] Loading notes for post:', displayPost.id, 'Notes:', initialNotes);
      setNotes(initialNotes);

      // Update the textarea value directly to avoid React state issues
      if (notesTextareaRef.current) {
        notesTextareaRef.current.value = initialNotes;
      }

      // Update local post state when prop changes
      setLocalPost(displayPost);

      // Debug: Log post media information
      console.log('[POST_VIEWER] Post data:', {
        id: displayPost.id,
        mediaType: displayPost.mediaType,
        mediaUrl: displayPost.mediaUrl,
        savedImage: (displayPost as any).savedImage,
        media: (displayPost as any).media,
        hasMediaUrl: !!displayPost.mediaUrl,
        hasSavedImage: !!(displayPost as any).savedImage,
        hasMedia: !!((displayPost as any).media && (displayPost as any).media.length > 0),
        platform: displayPost.platform
      });
    }
  }, [displayPost?.id]); // Only depend on post ID to avoid resetting notes when aiEnrichment changes

  // Remove the problematic useEffect that was resetting notes
  // The initialization is already handled in the first useEffect

  useEffect(() => {
    if (!displayPost) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't handle keyboard shortcuts if user is typing in an input/textarea
      const target = event.target as HTMLElement;
      const isInputElement = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;

      if (isInputElement) {
        // Only handle Escape key when in input elements
        if (event.key === 'Escape') {
          handleClose();
        }
        return;
      }

      // Handle navigation shortcuts only when not in input elements
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.body.style.overflow = 'hidden'; // Prevent background scroll

    // Focus trapping - improved to handle textarea properly
    const focusableElementsString = 'a[href]:not([disabled]), button:not([disabled]), textarea:not([disabled]), input[type="text"]:not([disabled]), input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), select:not([disabled])';
    const modal = modalRef.current;
    if (!modal) return;

    const focusableElements = Array.from(modal.querySelectorAll<HTMLElement>(focusableElementsString));
    if (focusableElements.length > 0) {
      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      // Don't auto-focus the first element to avoid interfering with user interaction
      // firstElement?.focus();

      const handleTabKeyPress = (event: KeyboardEvent) => {
        // Only handle tab trapping, don't interfere with other keys
        if (event.key === 'Tab') {
          if (event.shiftKey) { // Shift + Tab
            if (document.activeElement === firstElement) {
              lastElement?.focus();
              event.preventDefault();
            }
          } else { // Tab
            if (document.activeElement === lastElement) {
              firstElement?.focus();
              event.preventDefault();
            }
          }
        }
      };
      modal.addEventListener('keydown', handleTabKeyPress);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        modal.removeEventListener('keydown', handleTabKeyPress);
        document.body.style.overflow = 'auto';
      };
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto';
    };

  }, [displayPost, onClose, handleClose]);

  // Animation effects
  useEffect(() => {
    // Start entrance animation
    setIsAnimating(true);
    const timer = setTimeout(() => {
      setIsAnimating(false);
    }, 50); // Small delay to ensure initial state is rendered

    return () => clearTimeout(timer);
  }, []);

  // handleClose function moved up and wrapped in useCallback

  const handleCopySnapNote = useCallback(() => {
    const snapNote = displayPost?.snapNote;
    if (snapNote) {
      navigator.clipboard.writeText(snapNote)
        .then(() => {
          setCopyNotification(t('postViewer.snapNoteCopied'));
          setTimeout(() => setCopyNotification(''), 2000);
        })
        .catch(err => console.error('Failed to copy SnapNote: ', err));
    }
  }, [displayPost]);

  const handleSaveNotes = useCallback(async () => {
    if (!displayPost || isSaving || !notesTextareaRef.current) {
      return;
    }

    // Get the current value from the textarea
    const currentNotes = notesTextareaRef.current.value;
    console.log('[NOTES] Saving notes for post:', displayPost.id, 'Notes:', currentNotes);

    setIsSaving(true);
    try {
      // Update notes in storage
      await updatePostDetails(displayPost.id, { notes: currentNotes });
      console.log('[NOTES] Save successful');

      // Update local state
      setNotes(currentNotes);
      setLocalPost(prevPost => prevPost ? { ...prevPost, notes: currentNotes } : null);

      // Call parent handler if provided
      if (onUpdateNotes) {
        onUpdateNotes(displayPost.id, currentNotes);
      }

      // Show success message
      setSaveNotification(t('postViewer.notesSaved'));
      setTimeout(() => setSaveNotification(''), 3000);
    } catch (error) {
      console.error('[NOTES] Save failed:', error);
      setSaveNotification(t('postViewer.saveFailed'));
      setTimeout(() => setSaveNotification(''), 3000);
    } finally {
      setIsSaving(false);
    }
  }, [displayPost, onUpdateNotes, isSaving, t]);

  const handleCategoryChange = useCallback(async (newCategories: string[]) => {
    if (!displayPost) return;

    try {
      // Save to storage - use type assertion for now to handle potential type mismatches
      await updatePostDetails(displayPost.id, { categories: newCategories } as any);
      
      // Update global categories list
      const updatedCategories = [...new Set([...allCategories, ...newCategories])];
      setAllCategories(updatedCategories);
      await saveAllCategories(updatedCategories);

      // Update local post state immediately for UI
      setLocalPost(prevPost => prevPost ? { ...prevPost, categories: newCategories } : null);

      // Update parent component state
      if (onUpdateCategories) {
        onUpdateCategories(displayPost.id, newCategories);
      }
      
      // Show success message
      setCategoriesMessage({ text: t('postViewer.categoriesUpdated'), type: 'success' });
      setTimeout(() => setCategoriesMessage(null), 3000);
    } catch (error) {
      console.error('Error updating categories:', error);
      setCategoriesMessage({ text: t('postViewer.updateFailed'), type: 'error' });
      setTimeout(() => setCategoriesMessage(null), 3000);
    }
  }, [displayPost, allCategories, onUpdateCategories, t]);

  const handleTagChange = useCallback(async (newTags: string[]) => {
    if (!displayPost) return;
    
    try {
      // Update tags in storage
      await updatePostDetails(displayPost.id, { tags: newTags });
      
      // Update local state
      setLocalPost(prevPost => prevPost ? { ...prevPost, tags: newTags } : null);
      
      // Update global tags list if there are new tags
      const newTagsSet = new Set([...allTags, ...newTags]);
      if (newTagsSet.size > allTags.length) {
        const updatedTags = Array.from(newTagsSet);
        setAllTags(updatedTags);
        await saveAllTags(updatedTags);
      }

      // Update parent component state if provided
      if (onUpdateTags) {
        onUpdateTags(displayPost.id, newTags);
      }

      // Show success message
      setTagsMessage({ text: t('postViewer.tagsUpdated'), type: 'success' });
      setTimeout(() => setTagsMessage(null), 3000);
    } catch (error) {
      console.error('Failed to update tags:', error);
      setTagsMessage({ text: t('postViewer.updateFailed'), type: 'error' });
      setTimeout(() => setTagsMessage(null), 3000);
    }
  }, [displayPost, allTags, t, onUpdateTags]);

  // AI Enrichment Functions
  const handleAIEnrichment = useCallback(async (forceRegenerate = false) => {
    if (!displayPost || isAIProcessing) {
      return;
    }

    // Check if AI data already exists and we're not forcing regeneration
    if (!forceRegenerate && displayPost.snapNote && displayPost.inSight && displayPost.fastTake) {
      return;
    }

    setIsAIProcessing(true);
    setAIError(null);
    
    try {
      // Check AI availability first
      await aiEnrichment.checkAIAvailability();
      
      if (!aiEnrichment.canUseAI) {
        const errorMsg = aiEnrichment.usageInfo?.reason || 'AI features not available';
        setAIError(errorMsg);
        return;
      }

      // Prepare content for AI analysis
      const postContent = displayPost.text || displayPost.content || '';
      const imageUrl = displayPost.mediaUrl && displayPost.mediaType === 'image' ? displayPost.mediaUrl : undefined;

      // Call AI enrichment
      const result = await aiEnrichment.enrichPost(postContent, imageUrl);

      if (result) {
        // Update post with AI data - only update fields that are actually missing
        const aiData: any = {};

        // Only update snapNote if it's missing
        if (!displayPost.snapNote && result.snapNote) {
          aiData.snapNote = result.snapNote;
        }

        // Only update inSight if it's missing
        if (!displayPost.inSight && result.insight) {
          aiData.inSight = result.insight;
        }

        // Only update fastTake if it's missing
        if (!displayPost.fastTake && result.fastTake) {
          aiData.fastTake = result.fastTake;
        }

        // Don't override user-set categories and tags, only add if empty
        if ((!displayPost.categories || displayPost.categories.length === 0) && result.aiCategory) {
          aiData.categories = [result.aiCategory];
        }

        if ((!displayPost.tags || displayPost.tags.length === 0) && result.aiTags.length > 0) {
          aiData.tags = result.aiTags;
        }

        // Only update storage and state if there's actually new data to save
        if (Object.keys(aiData).length > 0) {
          // Use type assertion to handle potential type mismatches
          await updatePostDetails(displayPost.id, aiData as any);

          // Update local post state
          setLocalPost(prevPost => prevPost ? { ...prevPost, ...aiData } : null);

          console.log(`[PostViewer] Updated ${Object.keys(aiData).length} AI fields for post ${displayPost.id}:`, Object.keys(aiData));
        } else {
          console.log(`[PostViewer] No AI fields needed updating for post ${displayPost.id} - all fields already populated`);
        }
      }
    } catch (error) {
      console.error('AI enrichment failed:', error);
      setAIError(error instanceof Error ? error.message : 'AI enrichment failed');
    } finally {
      setIsAIProcessing(false);
    }
  }, [displayPost, isAIProcessing, aiEnrichment]);

  // Check if AI data is missing and trigger enrichment
  const shouldTriggerAI = useCallback(() => {
    if (!displayPost) return false;
    // Allow AI for both logged in users (free/premium) and logged out users (treated as free)

    // Check if ALL AI fields are missing (only trigger if post has no AI data at all)
    // This prevents unnecessary token usage for posts that already have some AI enrichment
    const hasNoAIData = !displayPost.snapNote && !displayPost.inSight && !displayPost.fastTake;

    return hasNoAIData;
  }, [displayPost]);

  // Auto-trigger AI enrichment when AI becomes available and post needs enrichment
  useEffect(() => {
    // Conditions to auto-trigger AI:
    // 1. AI usage info is available
    // 2. User can use AI (either free or premium)
    // 3. Post needs AI enrichment
    // 4. Not currently processing
    if (aiEnrichment.usageInfo && aiEnrichment.canUseAI && shouldTriggerAI() && !isAIProcessing) {
      handleAIEnrichment(false);
    }
  }, [aiEnrichment.usageInfo, aiEnrichment.canUseAI, shouldTriggerAI, isAIProcessing, handleAIEnrichment, displayPost?.id]);

  if (!displayPost || !localPost) return null;

  return (
    <div
      ref={modalRef}
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-300 ease-out animate-fadeIn ${
        isClosing
          ? 'bg-black/0 backdrop-blur-none'
          : isAnimating
            ? 'bg-black/0 backdrop-blur-none'
            : 'bg-black/70 backdrop-blur-sm notely-modal-overlay'
      }`}
      onClick={(e) => {
        // Only close if clicking on the overlay itself, not on child elements
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}
      aria-modal="true"
      role="dialog"
      aria-labelledby="post-viewer-title"
    >
      <div
        className={`notely-card bg-notely-card dark:bg-notely-card-dark border border-[#2F2F2F] rounded-notely-xl shadow-notely-md hover:shadow-notely-lg w-full max-w-6xl h-full max-h-[95vh] flex flex-col md:flex-row overflow-hidden transition-all duration-300 ease-out transform ${
          isClosing
            ? 'scale-95 opacity-0 translate-y-4'
            : isAnimating
              ? 'scale-95 opacity-0 translate-y-4'
              : 'scale-100 opacity-100 translate-y-0'
        }`}
        onClick={(e) => e.stopPropagation()} // Prevent close on content click
      >
        {/* Left Panel: Media or Text Content */}
        {isThreadMode && threadPosts ? (
          <div className="flex-1 overflow-y-auto bg-notely-bg dark:bg-notely-bg-dark p-4">
            <div className="relative px-6 py-4 max-w-xl mx-auto">
              {/* Thread Header - Author info shown once */}
              <div className="flex items-start gap-4 mb-6 pb-4 border-b border-notely-border/30">
                {threadPosts[0]?.authorAvatar && (
                  <img
                    src={threadPosts[0].authorAvatar}
                    alt={threadPosts[0].author}
                    className="w-10 h-10 rounded-full object-cover flex-shrink-0 border border-notely-border"
                  />
                )}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 flex-wrap">
                    <span className="text-lg font-semibold leading-tight text-notely-text-primary">
                      {threadPosts[0]?.authorName || threadPosts[0]?.author}
                    </span>
                    {threadPosts[0]?.authorHandle && (
                      <span className="text-notely-text-secondary text-sm">
                        @{threadPosts[0].authorHandle}
                      </span>
                    )}
                    <span className="text-notely-text-tertiary text-sm">
                      · {formatTimestamp(threadPosts[0]?.timestamp)}
                    </span>
                  </div>
                  <div className="text-notely-text-secondary text-sm mt-1">
                    Thread of {threadPosts.length} posts
                  </div>
                </div>
              </div>

              {/* Thread Line */}
              <div className="absolute left-[2.5rem] top-20 bottom-8 w-0.5 bg-notely-border opacity-30" />
              
              {/* Posts */}
              <div className="space-y-6">
                {threadPosts.map((p, index) => {
                  const postText = p.content || p.text || '';
                  return (
                    <div key={p.id} className="relative">
                      {/* Thread indicator dot */}
                      <div className="absolute left-[2.3rem] top-2 w-1 h-1 bg-notely-text-secondary rounded-full" />
                      
                      {/* Post Content */}
                      <div className="pl-[4rem]">
                        {/* Text Content */}
                        {postText && (
                          <div className="mb-4 text-notely-text-primary text-base leading-relaxed">
                            <PostContentRenderer
                              source={p.platform}
                              text={postText}
                              hasImages={!!(p.media && p.media.length > 0)}
                            />
                          </div>
                        )}
                        
                        {/* Media Content */}
                        {p.media && p.media.length > 0 && (
                          <div className="mb-4">
                            <ImageSwiper
                              images={p.media.map((m: any) => m.url)}
                              className="w-full h-auto rounded-lg"
                              aspectRatio="auto"
                              showControls={p.media.length > 1}
                              showDots={p.media.length > 1}
                            />
                          </div>
                        )}
                        
                        {/* Visual separator between posts (except last) */}
                        {index < threadPosts.length - 1 && (
                          <div className="mt-6 pt-6 border-t border-notely-border/20" />
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        ) : localPost.mediaType === 'text' && localPost.platform !== 'pinterest' ? (
          <div className="flex-1 flex flex-col overflow-hidden bg-notely-bg dark:bg-notely-bg-dark">
            <div className="flex-1 overflow-y-auto p-6 md:p-12 lg:p-20">
              <div className="max-w-3xl mx-auto reading-mode" style={{ backgroundColor: isDarkMode ? 'var(--notely-card-dark)' : 'var(--notely-card)', borderRadius: '20px', padding: '50px', boxShadow: 'var(--notely-shadow-lg)' }}>
                <style dangerouslySetInnerHTML={{ __html: `
                  .reading-mode { 
                    font-family: Georgia, serif; 
                    color: ${isDarkMode ? 'var(--notely-text-primary-dark)' : 'var(--notely-text-primary)'}; /* Theme-aware text color */
                    line-height: 1.8;
                    font-size: 1.875rem; /* Increased by 50% from 1.25rem */
                  }
                  .reading-mode h1, .reading-mode h2, .reading-mode h3 { 
                    font-family: Georgia, serif; 
                    margin-top: 1.5em;
                    margin-bottom: 0.75em;
                    font-weight: 600;
                    line-height: 1.3;
                    color: ${isDarkMode ? 'var(--notely-text-primary-dark)' : 'var(--notely-text-primary)'};
                  }
                  .reading-mode h1 { font-size: 3rem; } /* Increased by 50% */
                  .reading-mode h2 { font-size: 2.625rem; } /* Increased by 50% */
                  .reading-mode h3 { font-size: 2.25rem; } /* Increased by 50% */
                  .reading-mode p { margin-bottom: 1.5em; }
                  .reading-mode a { 
                    color: ${isDarkMode ? '#93c5fd' : '#3b82f6'}; /* Theme-aware link color */
                    text-decoration: underline; 
                    text-decoration-thickness: 1px; 
                    text-underline-offset: 2px; 
                  }
                  .reading-mode blockquote { 
                    border-left: 4px solid ${isDarkMode ? '#60a5fa' : '#3b82f6'}; /* Theme-aware border */
                    padding-left: 1.5rem; 
                    margin: 1.5em 0;
                    font-style: italic; 
                    color: ${isDarkMode ? '#e5e7eb' : '#4b5563'}; /* Theme-aware text */
                    background-color: ${isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.05)'}; /* Theme-aware background */
                    padding: 1rem 1.5rem;
                    border-radius: 8px;
                  }
                  .reading-mode ul, .reading-mode ol {
                    margin: 1.5em 0;
                    padding-left: 1.5em;
                  }
                  .reading-mode li {
                    margin-bottom: 0.75em;
                  }
                `}} />
                <PostContentRenderer
                  source={localPost.platform}
                  text={localPost.text || localPost.content || ''}
                  className="text-2xl md:text-3xl leading-relaxed"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 bg-notely-bg dark:bg-notely-bg-dark flex items-center justify-center h-full overflow-hidden p-2 relative">
            {(() => {
              // Check if we have video content first (prioritize video over images)
              const hasVideo = (localPost.mediaType === 'video' && localPost.mediaUrl) || 
                              (localPost.media && localPost.media.some(item => item.type === 'video'));
              
              // Only show images if there's no video content
              const shouldShowImages = !hasVideo && 
                                      (((localPost.mediaType === 'image' && localPost.mediaUrl) || 
                                        (localPost as any).savedImage || 
                                        (localPost.media && localPost.media.length > 0)));

              if (!shouldShowImages) return null;

              // Prepare image data for multi-image support
              const hasMultipleImages = localPost.media && localPost.media.length > 1;
              const allImageUrls = hasMultipleImages
                ? localPost.media.map((mediaItem: any) => mediaItem.url).filter(Boolean)
                : [];

              const singleImageUrl = (localPost as any).savedImage || localPost.mediaUrl || (localPost.media && localPost.media[0]?.url);
              const shouldUseSwiper = hasMultipleImages && allImageUrls.length > 1;
              const finalImageUrls = shouldUseSwiper ? allImageUrls : (singleImageUrl ? [singleImageUrl] : []);

              if (finalImageUrls.length === 0) return null;

              return (
                <>
                  {shouldUseSwiper ? (
                    // Multi-image carousel using ImageSwiper
                    <ImageSwiper
                      images={finalImageUrls}
                      className="w-full h-full"
                      aspectRatio="auto"
                      showControls={true}
                      showDots={true}
                    />
                  ) : (
                    // Single image display (existing logic)
                    <>
                      {/* Pinterest-specific image handling */}
                      {localPost.platform === 'pinterest' ? (
                        <>
                          {isBase64Image(singleImageUrl) ? (
                            // Directly render base64 image data for Pinterest
                            <div className="w-full h-full flex items-center justify-center">
                              <img
                                src={singleImageUrl}
                                alt={`Post by ${localPost.author}`}
                                className="max-h-full max-w-full object-contain"
                                onError={(e) => {
                                  console.error('Pinterest base64 image failed to load');
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                            </div>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <img
                                src={singleImageUrl}
                                alt={`Post by ${localPost.author}`}
                                className="max-h-full max-w-full object-contain"
                                onError={(e) => {
                                  console.error('Pinterest direct image failed, trying ProxyImage');
                                  // Hide the failed direct image and show ProxyImage
                                  e.currentTarget.style.display = 'none';
                                  const proxyImgContainer = document.getElementById(`pinterest-proxy-${localPost.id}`);
                                  if (proxyImgContainer) {
                                    proxyImgContainer.style.display = 'block';
                                  }
                                }}
                              />
                            </div>
                          )}
                          {/* Fallback ProxyImage for Pinterest */}
                          <div
                            id={`pinterest-proxy-${localPost.id}`}
                            style={{ display: 'none' }}
                            className="w-full h-full flex items-center justify-center"
                          >
                            <ProxyImage
                              src={singleImageUrl}
                              alt={`Post by ${localPost.author}`}
                              className="max-h-full max-w-full object-contain"
                              postId={localPost.id}
                              onError={(error) => console.error('Pinterest ProxyImage error:', error)}
                            />
                          </div>
                        </>
                      ) : (
                        // Standard image handling for other platforms
                        <div className="w-full h-full flex items-center justify-center">
                          <img
                            src={singleImageUrl}
                            alt={`Post by ${localPost.author}`}
                            className="max-h-full max-w-full object-contain"
                            onError={(e) => {
                              console.error('Image failed to load:', e.currentTarget.src);
                            }}
                          />
                        </div>
                      )}
                    </>
                  )}
                </>
              );
            })()}
            {(localPost.mediaType === 'video' && localPost.mediaUrl) || (localPost.media && localPost.media.some(item => item.type === 'video')) ? (
              (() => {
                const videoRef = useRef<HTMLVideoElement>(null);
                const [isPlaying, setIsPlaying] = useState(false);

                const handlePlay = () => {
                  if (videoRef.current) {
                    videoRef.current.play();
                    setIsPlaying(true);
                  }
                };
                
                // Get video URL from mediaUrl or first video in media array
                const videoUrl = localPost.mediaUrl || (localPost.media && localPost.media.find(item => item.type === 'video')?.url);
                
                return videoUrl ? (
                  <div className="w-full h-full flex items-center justify-center relative group">
                    <video 
                      ref={videoRef}
                      src={videoUrl} 
                      controls={isPlaying} // Show controls only when playing
                      className="max-h-full max-w-full object-contain transition-opacity duration-300"
                      preload={videoUrl.startsWith('blob:') ? 'auto' : 'metadata'}
                      onPlay={() => setIsPlaying(true)}
                      onPause={() => setIsPlaying(false)}
                      onEnded={() => setIsPlaying(false)}
                      onError={(e) => {
                        console.error(`[PostViewer] 🎥 Video failed to load: ${videoUrl.substring(0, 50)}...`);
                        const target = e.currentTarget;
                        
                        // For blob URLs, try to reload once before showing error
                        if (videoUrl.startsWith('blob:')) {
                          console.log(`[PostViewer] 🔄 Attempting to reload blob video...`);
                          setTimeout(() => {
                            target.load();
                          }, 1000);
                        }
                      }}
                    >
                      <div className="flex items-center justify-center h-full bg-notely-surface dark:bg-notely-surface-dark text-notely-text-secondary dark:text-notely-text-secondary-dark">
                        🎥 Video not supported by your browser
                      </div>
                    </video>
                    
                    {/* Custom Play Button Overlay */}
                    {!isPlaying && (
                      <div 
                        className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-black/20 to-black/40 cursor-pointer transition-all duration-300 group-hover:from-black/30 group-hover:to-black/60"
                        onClick={handlePlay}
                      >
                        <div className="w-16 h-16 bg-notely-bg/25 dark:bg-notely-bg-dark/25 backdrop-blur-md rounded-full flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 shadow-2xl border border-notely-border/20 dark:border-notely-border-dark/20">
                           <svg className="w-8 h-8 text-notely-text-primary dark:text-notely-text-primary-dark ml-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z"/>
                          </svg>
                        </div>
                      </div>
                    )}

                    {/* Video indicator overlay */}
                    <div className="absolute top-4 left-4 bg-black/50 text-white text-sm px-3 py-1 rounded-full flex items-center gap-2 z-10">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 16 16"><path d="M0 12V4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm6.79-6.907a.5.5 0 0 0-.79.407v5a.5.5 0 0 0 .79.407l3.5-2.5a.5.5 0 0 0 0-.814z"/></svg>
                      <span>Video</span>
                    </div>
                  </div>
                ) : null;
              })()
            ) : null}
            {localPost.mediaType === 'text' && localPost.platform === 'pinterest' && (
              <div className="h-full w-full overflow-y-auto p-6 bg-notely-bg dark:bg-notely-bg-dark">
                <div className="text-notely-text-primary dark:text-notely-text-primary-dark">
                  <PostContentRenderer
                    source={localPost.platform}
                    text={localPost.text || localPost.content || ''}
                    className="text-notely-text-primary dark:text-notely-text-primary-dark"
                    hasImages={false}
                  />
                </div>
              </div>
            )}
            {!localPost.mediaUrl && !(localPost as any).savedImage && !(localPost.media && localPost.media.length > 0) && localPost.mediaType !== 'text' && (
              <div className="text-notely-text-secondary dark:text-notely-text-secondary-dark">{t('postViewer.noMediaAvailable')}</div>
            )}
          </div>
        )}

        {/* Right Panel: Details - Adjusted for text-only posts */}
        <div 
          className={`${localPost.mediaType === 'text' && localPost.platform !== 'pinterest' ? 'md:w-[300px]' : 'md:w-[360px]'} w-full p-5 flex-shrink-0 overflow-y-auto border-l border-[#2F2F2F] bg-notely-card dark:bg-notely-card-dark`}
          style={{
            backdropFilter: 'blur(10px)'
          }}
        >
          {/* Thread Header (if in thread mode) */}
          {isThreadMode && threadPosts && (
             <div className="mb-4 p-3 bg-notely-accent/10 border border-notely-accent/20 rounded-lg">
               <div className="flex items-center justify-between mb-2">
                 <div className="flex items-center space-x-2">
                   <span className="text-notely-accent font-semibold text-sm">🧵 {t('postViewer.thread')}</span>
                   <span className="text-xs text-notely-text-secondary">
                     Thread of {threadPosts.length} posts
                   </span>
                 </div>
               </div>
               <div className="text-xs text-notely-text-tertiary">
                 {t('postViewer.threadId').replace('{id}', localPost.threadId?.slice(-8) || '')}
               </div>
             </div>
           )}

          {/* Header - Simplified for text-only posts */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-2">
              <PlatformLogo platform={localPost.platform} className="w-6 h-6" />
              <div>
                <h2 id="post-viewer-title" className="text-sm font-semibold text-notely-text-primary leading-tight">{localPost.author}</h2>
                <p className="text-xs text-notely-text-secondary">
                  {formatTimestamp(localPost.timestamp)}
                  {isThreadMode && (
                    <span className="ml-2 text-notely-accent">
                      • First post
                    </span>
                  )}
                </p>
              </div>
            </div>
            {/* Close button - X icon */}
            <button
              onClick={handleClose}
              className="p-1 text-notely-text-secondary hover:text-notely-text-primary rounded-full hover:bg-notely-surface focus:outline-none focus:ring-2 focus:ring-notely-accent"
              aria-label={t('postViewer.closePostViewer')}
            >
              <IconX />
            </button>
          </div>

          {/* Stats - Hide for Pinterest as they don't provide meaningful engagement data */}
          {localPost.platform !== 'pinterest' && (
            <div className="flex items-center space-x-4 text-xs text-notely-text-secondary mb-4 pb-3 border-b border-[#2F2F2F]">
              {/* Comment Icon */}
              <div className="flex items-center space-x-1">
                <CommentIcon className="w-4 h-4" />
                {(localPost.stats?.comments ?? 0) > 0 && (
                  <span className="text-notely-text-primary font-medium">{formatStatNumber(localPost.stats.comments)}</span>
                )}
              </div>

              {/* Share/Repost Icon */}
              <div className="flex items-center space-x-1">
                <ShareIcon className="w-4 h-4" />
                {(localPost.stats?.shares ?? 0) > 0 && (
                  <span className="text-notely-text-primary font-medium">{formatStatNumber(localPost.stats.shares)}</span>
                )}
              </div>

              {/* Like Icon */}
              <div className="flex items-center space-x-1">
                <HeartIcon className="w-4 h-4" />
                {(localPost.stats?.likes ?? 0) > 0 && (
                  <span className="text-notely-text-primary font-medium">{formatStatNumber(localPost.stats.likes)}</span>
                )}
              </div>
            </div>
          )}

          {/* Original Text if available and not 'text' media type (useful for captions) */}
          {localPost.mediaType !== 'text' && (localPost.text || localPost.content) && (
             <div className="mb-4">
                <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-1">{t('postViewer.originalText')}</h3>
                <PostContentRenderer
                  source={localPost.platform}
                  text={localPost.text || localPost.content || ''}
                  hasImages={!!(localPost.mediaUrl || (localPost as any).savedImage || (localPost.media && localPost.media.length > 0))}
                />
             </div>
          )}

          {/* SnapNote */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-1">
              <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider">{t('postViewer.snapNote')}</h3>
              <div className="flex items-center space-x-1">

                {localPost.snapNote && (
                  <button onClick={handleCopySnapNote} className="p-1 text-notely-text-secondary hover:text-notely-accent rounded focus:outline-none focus:ring-1 focus:ring-notely-accent" title={t('postViewer.copySnapNote')}>
                    <IconClipboard />
                  </button>
                )}
              </div>
            </div>

            {/* AI Error Display */}
            {aiError && (
              <div className="mb-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200/50 dark:border-red-800/30 rounded text-xs text-red-700 dark:text-red-300">
                {aiError}
              </div>
            )}

            {/* SnapNote Content */}
            <div className="relative">
              {isAIProcessing && !localPost.snapNote && (
                <div className="flex items-center space-x-2 text-sm text-notely-text-secondary">
                  <IconRefresh className="w-4 h-4 animate-spin" />
                  <span>{t('postViewer.generatingSnapNote')}</span>
                </div>
              )}
              <p className={`text-sm text-notely-text-primary leading-relaxed ${isAIProcessing && !localPost.snapNote ? 'opacity-50' : ''}`}>
                {localPost.snapNote || (isAIProcessing ? 'Analyzing content...' : 'AI SnapNote will be generated automatically when you save posts')}
              </p>
            </div>
            {copyNotification && <p className="text-xs text-green-600 dark:text-green-400 mt-1">{copyNotification}</p>}
          </div>

          {/* InSight */}
          <div className="mb-4">
            <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-2">{t('postViewer.insight')}</h3>
            {isAIProcessing && !localPost.inSight ? (
              <div className="flex items-center space-x-2 text-sm text-notely-text-secondary mb-2">
                <IconRefresh className="w-4 h-4 animate-spin" />
                <span>{t('postViewer.analyzingSentiment')}</span>
              </div>
            ) : localPost.inSight ? (
              <>
                <div className="flex items-center mb-2">
                  <span className="text-3xl mr-2">{localPost.inSight.emoji}</span>
                  <span className={`text-sm font-medium capitalize px-2 py-0.5 rounded-full ${
                    localPost.inSight.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' :
                    localPost.inSight.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300' :
                    'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                  }`}>{localPost.inSight.sentiment}</span>
                </div>
                {localPost.inSight.contextTags && localPost.inSight.contextTags.length > 0 && (
                  <div className="flex flex-wrap gap-1.5">
                    {localPost.inSight.contextTags.map(tag => <Chip key={tag} text={formatForDisplay(tag)} interactive={false} />)}
                  </div>
                )}
              </>
            ) : (
              <div className="flex items-center mb-2">
                <span className="text-3xl mr-2">🤔</span>
                <span className="text-sm font-medium capitalize px-2 py-0.5 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300">
                  AI analysis will be generated automatically
                </span>
              </div>
            )}
          </div>

          {/* FastTake */}
          <div className="mb-4">
            <h3 className="text-xs font-semibold text-notely-text-secondary uppercase tracking-wider mb-1">{t('postViewer.fastTake')}</h3>
            {isAIProcessing && !localPost.fastTake ? (
              <div className="flex items-center space-x-2 text-sm text-notely-text-secondary">
                <IconRefresh className="w-4 h-4 animate-spin" />
                <span>{t('postViewer.generatingQuickInsight')}</span>
              </div>
            ) : (
              <p className="text-sm text-notely-text-primary italic">
                "{localPost.fastTake || t('postViewer.aiFastTakeAutomatic')}"
              </p>
            )}
          </div>

          {/* Categories & Tags Toggle Component */}
          <div className="mb-4">
            <CategoryTagToggle
              categories={localPost.categories?.map(cat => formatForDisplay(cat)) || []}
              tags={localPost.tags?.map(tag => formatForDisplay(tag)) || []}
              allCategories={allCategories.map(cat => formatForDisplay(cat))}
              allTags={allTags.map(tag => formatForDisplay(tag))}
              onCategoriesChange={(newCategories) => {
                const formattedCategories = newCategories.map(cat => formatForStorage(cat));
                handleCategoryChange(formattedCategories);
              }}
              onTagsChange={(newTags) => {
                const formattedTags = newTags.map(tag => formatForStorage(tag));
                handleTagChange(formattedTags);
              }}
              categoriesMessage={categoriesMessage}
              tagsMessage={tagsMessage}
              maxCategories={3}
              maxTags={5}
              canEdit={!!onAddTag}
              isDarkMode={isDarkMode}
            />
          </div>

                      <hr className={`my-5 ${isDarkMode ? 'border-[#2F2F2F]' : 'border-gray-200'}`} />

          {/* My Notes Section */}
          <div className="mt-2">
            <h3 className={`text-xs font-semibold uppercase tracking-wider mb-2 ${isDarkMode ? 'text-notely-text-secondary' : 'text-gray-600'}`}>{t('postViewer.myNotes')}</h3>

            <textarea
              ref={notesTextareaRef}
              id="my-notes-textarea"
              rows={3}
              defaultValue={notes}
              onChange={(e) => {
                // Update state for consistency, but the textarea manages its own value
                setNotes(e.target.value);
              }}
              onKeyDown={(e) => {
                // Prevent event bubbling for normal typing
                e.stopPropagation();
              }}
              placeholder={t('postViewer.addNotesPlaceholder')}
                className={`w-full p-2 rounded-md text-sm resize-none ${isDarkMode ? 'border border-[#2F2F2F] bg-notely-surface text-notely-text-primary shadow-notely-sm' : 'border border-gray-300 bg-white text-gray-800 shadow-sm'} focus:ring-1 focus:ring-notely-accent focus:border-notely-accent`}
              style={{ minHeight: '80px' }}
            />
            <div className="flex justify-between items-center mt-2">
              <div>
                {saveNotification && (
                  <p className={`text-xs ${saveNotification.includes('Failed') ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                    {saveNotification}
                  </p>
                )}
              </div>
              <button
                onClick={handleSaveNotes}
                disabled={isSaving}
                className={`flex items-center space-x-1 px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${isDarkMode ? 'border border-[#2F2F2F] shadow-notely-sm' : 'border border-blue-700 shadow-sm'}`}
                title={t('postViewer.saveNotes')}
              >
                <IconSave className="w-4 h-4" />
                <span>{isSaving ? t('common.saving') : t('common.save')}</span>
              </button>
            </div>
          </div>

        </div> {/* End Right Panel */}
      </div> {/* End Main Content Box */}
    </div> // End Modal Overlay
  );
};

export default PostViewerFullScreen;