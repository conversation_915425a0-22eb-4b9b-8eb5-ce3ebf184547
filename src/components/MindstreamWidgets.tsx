import React from 'react';
import { Post } from '../types';
import ChatWithPosts from './ChatWithPosts';
import SmartWorkspace from './SmartWorkspace';
import SavedBookmarksWidget from './SavedBookmarksWidget';

interface MindstreamWidgetsProps {
  posts: Post[];
  className?: string;
  onOpenPost?: (postId: string) => void; // Changed to match SmartWorkspace
}

const MindstreamWidgets: React.FC<MindstreamWidgetsProps> = ({ posts, className = '', onOpenPost }) => {
  return (
    <div className={`space-y-8 ${className}`}>
      {/* AI Chat Interface */}
      <ChatWithPosts posts={posts} onOpenPost={onOpenPost} />

      {/* All the original SmartWorkspace content */}
      <SmartWorkspace posts={posts} onOpenPost={onOpenPost} />

      {/* Saved Bookmarks */}
      <div className="notely-card bg-notely-card rounded-notely-xl shadow-[0_1px_3px_rgba(0,0,0,0.06),_0_1px_2px_rgba(0,0,0,0.04)] hover:shadow-[0_2px_4px_rgba(0,0,0,0.08),_0_1px_2px_rgba(0,0,0,0.06)] notely-filter-transition p-6 border border-gray-200 dark:border-notely-border-dark/20">
        <h2 className="text-xl font-semibold mb-4 notely-heading">📚 Saved Bookmarks</h2>
        <SavedBookmarksWidget
          posts={posts}
          isDragging={false}
          onRemove={() => {}}
        />
      </div>
    </div>
  );
};

export default MindstreamWidgets;
