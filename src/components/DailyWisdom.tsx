import React, { useState, useEffect } from 'react';
import { getTodaysWisdom, addSampleQuote, clearWisdomStorage, forceNewWisdomQuote } from '../services/wisdomService';
import { getSavedPosts } from '../storage';
import { formatForDisplay } from '../utils/formatUtils';
import type { WisdomQuote } from '../types/wisdom';
import type { Post } from '../types';

interface DailyWisdomProps {
  className?: string;
  onQuoteClick?: (quote: WisdomQuote) => void;
  onOpenPost?: (post: Post) => void;
}

const DailyWisdom: React.FC<DailyWisdomProps> = ({ className = '', onQuoteClick, onOpenPost }) => {
  const [quote, setQuote] = useState<WisdomQuote | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);



  useEffect(() => {
    const loadTodaysWisdom = async () => {
      try {
        console.log('DailyWisdom: Starting to load today\'s wisdom...');
        setIsLoading(true);
        const todaysQuote = await getTodaysWisdom();
        console.log('DailyWisdom: Received quote:', todaysQuote);
        setQuote(todaysQuote);
      } catch (err) {
        console.error('DailyWisdom: Failed to load daily wisdom:', err);
        setError('Failed to load daily wisdom. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    loadTodaysWisdom();
  }, []);

  const handleClick = async () => {

    if (!quote) return;

    // If wisdom is from a post and we have onOpenPost, try to open the original post
    if (quote.extractedFrom === 'post' && onOpenPost) {
      try {
        const savedPosts = await getSavedPosts();
        
        // Try to find the post using all available IDs and links
        let originalPost = null;

        // First try to find by relatedPostIds
        if (quote.relatedPostIds && quote.relatedPostIds.length > 0) {
          for (const postId of quote.relatedPostIds) {
            if (!postId) continue;
            
            // Try to find by exact ID match first
            originalPost = savedPosts.find(post => 
              post.id === postId || 
              post.originalPostId === postId
            );
            
            if (originalPost) break;
            
            // If not found, try to find by ID in permalink
            originalPost = savedPosts.find(post =>
              post.permalink?.includes(postId)
            );
            
            if (originalPost) break;
          }
        }

        // If still not found and we have a source_link, try to match by that
        if (!originalPost && quote.source_link) {
          originalPost = savedPosts.find(post =>
            post.permalink === quote.source_link ||
            (quote.source_link && post.permalink?.includes(quote.source_link))
          );
        }

        if (originalPost) {
          onOpenPost(originalPost);
          return;
        } else {
        }
      } catch (error) {
        console.error('Failed to find original post:', error);
      }
    }

    // Fallback to quote click handler
    if (onQuoteClick) {
      onQuoteClick(quote);
    }
  };

  if (isLoading) {
    return (
      <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-xl shadow-notely-md notely-breathing-lg ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-notely-surface rounded w-3/4"></div>
          <div className="h-4 bg-notely-surface rounded w-1/2"></div>
          <div className="h-4 bg-notely-surface rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`notely-card bg-notely-card border border-gray-200 rounded-notely-xl shadow-[0_1px_3px_rgba(0,0,0,0.06),_0_1px_2px_rgba(0,0,0,0.04)] notely-breathing-lg text-red-500 ${className}`}>
        {error}
      </div>
    );
  }

  if (!quote) {
    return (
      <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-xl shadow-notely-md notely-breathing-lg ${className}`}>
        <div className="text-center space-y-3">
          <p className="text-sm text-notely-text-muted leading-relaxed mb-4">No wisdom quotes available.</p>
          <button
            onClick={async () => {
              try {
                const newQuote = await addSampleQuote();
                setQuote(newQuote);
              } catch (error) {
                console.error('Failed to add sample quote:', error);
                setError('Failed to add sample quote. Please try again.');
              }
            }}
            className="notely-btn-primary text-sm px-4 py-2 notely-filter-transition hover:scale-105 mr-2"
          >
            Add Sample Quote
          </button>
          <button
            onClick={async () => {
              if (isRefreshing) return;

              try {
                console.log('DailyWisdom: Regenerate button clicked');
                setIsRefreshing(true);
                setError(null);

                // Use the new force refresh function for immediate variety
                const newQuote = await forceNewWisdomQuote();
                console.log('DailyWisdom: New quote generated:', newQuote);

                setQuote(newQuote);
              } catch (error) {
                console.error('Failed to regenerate wisdom:', error);
                setError('Failed to regenerate wisdom. Please try again.');
              } finally {
                setIsRefreshing(false);
              }
            }}
            className="notely-btn-primary text-sm px-4 py-2 notely-filter-transition hover:scale-105"
            disabled={isRefreshing}
          >
            {isRefreshing ? 'Generating...' : 'Regenerate Wisdom'}
          </button>
        </div>
      </div>
    );
  }

  // Only make the card clickable if the quote is from a post (has a source)
  const isClickable = quote.extractedFrom === 'post';

  return (
    <div
      className={`notely-card bg-notely-card border border-gray-200 rounded-notely-lg shadow-[0_1px_3px_rgba(0,0,0,0.06),_0_1px_2px_rgba(0,0,0,0.04)] hover:shadow-[0_2px_4px_rgba(0,0,0,0.08),_0_1px_2px_rgba(0,0,0,0.06)] group break-inside-avoid mb-6 overflow-hidden notely-filter-transition ${
        isClickable ? 'cursor-pointer hover:border-gray-300 dark:hover:border-notely-border-dark/30' : ''
      } ${className}`}
      onClick={isClickable ? handleClick : undefined}
      aria-label="Daily wisdom quote"
    >
      <div className="px-4 py-4">
        <div className="flex flex-col h-full">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-3">
              <div className="text-xs text-notely-text-muted font-medium">
                💭 Daily Wisdom
              </div>
              <button
                onClick={async (e) => {
                  e.stopPropagation();
                  if (isRefreshing) return; // Prevent multiple clicks

                  try {
                    console.log('DailyWisdom: Refresh button clicked');
                    setIsRefreshing(true);
                    setError(null); // Clear any previous errors

                    // Use the new force refresh function for immediate variety
                    const newQuote = await forceNewWisdomQuote();
                    console.log('DailyWisdom: New quote generated:', newQuote);

                    setQuote(newQuote);
                  } catch (error) {
                    console.error('Failed to regenerate wisdom:', error);
                    setError('Failed to regenerate wisdom. Please try again.');
                  } finally {
                    setIsRefreshing(false);
                  }
                }}
                className={`text-xs transition-colors ${
                  isRefreshing
                    ? 'text-notely-lavender animate-spin'
                    : 'text-notely-text-muted hover:text-notely-text-primary'
                }`}
                title="Regenerate wisdom"
                aria-label="Regenerate wisdom quote"
                disabled={isRefreshing}
              >
                🔄
              </button>
            </div>
            <blockquote className="text-notely-text-primary text-sm font-medium leading-relaxed mb-4 italic">
              "{quote.text}"
            </blockquote>
            {quote.author && (
              <div className="text-right text-xs text-notely-text-muted leading-relaxed">
                — {quote.author}
              </div>
            )}
          </div>
          {(quote.tags?.length > 0 || quote.extractedFrom === 'post') && (
            <div className="mt-3 pt-2 border-t border-notely-border/10 dark:border-notely-border-dark/20">
              <div className="flex flex-wrap gap-1">
                {quote.tags?.slice(0, 2).map((tag: string) => (
                  <span
                    key={tag}
                    className="inline-flex items-center text-xs font-medium rounded px-1.5 py-0.5 bg-notely-surface text-notely-text-secondary"
                  >
                    {formatForDisplay(tag)}
                  </span>
                ))}
                {quote.extractedFrom === 'post' && (
                  <span className="inline-flex items-center text-xs font-medium rounded px-1.5 py-0.5 bg-notely-lavender/10 text-notely-lavender">
                    From Post
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Export as both default and named export for flexibility
export { DailyWisdom };
export default DailyWisdom;
