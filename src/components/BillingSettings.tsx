import React, { useState, useEffect } from 'react';
import * as subscriptionService from '../services/subscriptionService';

interface SubscriptionDetails {
  id: string;
  status: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  cancelAt: number | null;
  trialEnd: number | null;
}

interface BillingSettingsProps {
  user?: any;
  getToken?: () => Promise<string | null> | string | null;
}

export const BillingSettings: React.FC<BillingSettingsProps> = ({
  user: propUser,
  getToken: propGetToken
}) => {
  // Try to use Chrome extension auth context if props not provided
  let user = propUser;
  let getToken = propGetToken;

  if (!user || !getToken) {
    try {
      const { useAuth } = require('../context/AuthContext');
      const auth = useAuth();
      user = user || auth.user;
      getToken = getToken || auth.getToken;
    } catch {
      // Fallback if Chrome extension context not available
    }
  }
  const [subscription, setSubscription] = useState<SubscriptionDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.plan === 'premium') {
      fetchSubscriptionDetails();
    }
  }, [user]);

  const fetchSubscriptionDetails = async () => {
    try {
      setLoading(true);
      const data = await subscriptionService.getSubscriptionDetails();
      setSubscription(data);
    } catch (err) {
      console.error('Error fetching subscription details:', err);
      setError('Failed to load subscription details');
    } finally {
      setLoading(false);
    }
  };

  const handleManageSubscription = async () => {
    try {
      setLoading(true);
      setError(null); // Clear previous errors

      const url = await subscriptionService.createPortalSession();
      if (url) {
        window.open(url, '_blank');
      } else {
        throw new Error('Unable to create billing portal session. Please try again or contact support.');
      }
    } catch (err) {
      console.error('Error opening billing portal:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to open billing portal';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
      return;
    }

    try {
      setLoading(true);
      const token = await getToken();
      const response = await fetch('/billing/cancel-subscription', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        await fetchSubscriptionDetails();
        alert('Subscription will be canceled at the end of your billing period.');
      } else {
        throw new Error('Failed to cancel subscription');
      }
    } catch (err) {
      console.error('Error canceling subscription:', err);
      setError('Failed to cancel subscription');
    } finally {
      setLoading(false);
    }
  };

  const handleReactivateSubscription = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      const response = await fetch('/billing/reactivate-subscription', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        await fetchSubscriptionDetails();
        alert('Subscription reactivated successfully!');
      } else {
        throw new Error('Failed to reactivate subscription');
      }
    } catch (err) {
      console.error('Error reactivating subscription:', err);
      setError('Failed to reactivate subscription');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp: number | null | undefined) => {
    if (!timestamp || timestamp === 0) {
      return 'N/A';
    }

    try {
      const date = new Date(timestamp * 1000);
      if (isNaN(date.getTime())) {
        console.warn('Invalid timestamp received:', timestamp);
        return 'Invalid Date';
      }
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', error, 'timestamp:', timestamp);
      return 'Invalid Date';
    }
  };

  if (!user) {
    return <div>Please log in to manage your billing settings.</div>;
  }

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Billing & Subscription</h3>
        
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 mb-4">
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Current Plan
            </label>
            <p className="mt-1 text-sm text-gray-900 dark:text-gray-100 capitalize">
              {user.plan} {user.subscriptionStatus && `(${user.subscriptionStatus})`}
            </p>
          </div>

          {user.plan === 'premium' && subscription && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Billing Period
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-gray-100">
                  {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
                </p>
              </div>

              {subscription.cancelAtPeriodEnd && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3">
                  <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                    Your subscription will be canceled on {formatDate(subscription.currentPeriodEnd)}
                  </p>
                </div>
              )}

              {subscription.trialEnd && (
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
                  <p className="text-blue-800 dark:text-blue-200 text-sm">
                    Trial ends on {formatDate(subscription.trialEnd)}
                  </p>
                </div>
              )}
            </div>
          )}

          <div className="flex flex-wrap gap-3 pt-4">
            {user.plan === 'premium' && (
              <>
                <button
                  onClick={handleManageSubscription}
                  disabled={loading}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Loading...' : 'Manage Subscription'}
                </button>

                {subscription?.cancelAtPeriodEnd ? (
                  <button
                    onClick={handleReactivateSubscription}
                    disabled={loading}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Reactivate Subscription
                  </button>
                ) : (
                  <button
                    onClick={handleCancelSubscription}
                    disabled={loading}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Cancel Subscription
                  </button>
                )}
              </>
            )}

            {user.plan === 'free' && (
              <button
                onClick={() => {
                  try {
                    import('../utils/upgradeUtils').then(({ navigateToPlanChooser, UPGRADE_SOURCES }) => {
                      navigateToPlanChooser(UPGRADE_SOURCES.SETTINGS_HEADER);
                    });
                  } catch (error) {
                    console.error('Error navigating to plan chooser:', error);
                    // Fallback - try to navigate to settings with plan selection
                    if (typeof window !== 'undefined') {
                      window.location.href = '/settings?selectPlan=1';
                    }
                  }
                }}
                className="px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-md hover:from-purple-600 hover:to-indigo-700"
              >
                Upgrade to Premium
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingSettings;
