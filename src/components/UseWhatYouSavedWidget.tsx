import React, { useState, useEffect, useMemo } from 'react';
import { Post } from '../types';
import { ContentSuggestion } from '../types/contentSuggestions';
import { PlatformLogo } from './PlatformLogo';
import { analyzePostsForContentSuggestions } from '../utils/contentSuggestionUtils';
import { generateContentSuggestion } from '../services/aiService';
import { trackContentConversion } from '../services/contentConversionService';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';

interface UseWhatYouSavedWidgetProps {
  posts: Post[];
  isDragging: boolean;
  onRemove: () => void;
}

interface GeneratedContent {
  suggestionId: string;
  content: string;
  isGenerating: boolean;
}

const UseWhatYouSavedWidget: React.FC<UseWhatYouSavedWidgetProps> = ({
  posts,
  isDragging,
  onRemove
}) => {
  const { t } = useTranslation();
  const [suggestions, setSuggestions] = useState<ContentSuggestion[]>([]);
  const [generatedContent, setGeneratedContent] = useState<Map<string, GeneratedContent>>(new Map());
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [lastAnalyzedCount, setLastAnalyzedCount] = useState(0);

  // Analyze posts for content suggestions
  const analyzePosts = useMemo(() => {
    if (posts.length === 0 || posts.length === lastAnalyzedCount) {
      return suggestions;
    }

    setIsAnalyzing(true);
    const newSuggestions = analyzePostsForContentSuggestions(posts);
    setSuggestions(newSuggestions);
    setLastAnalyzedCount(posts.length);
    setIsAnalyzing(false);
    
    return newSuggestions;
  }, [posts.length, lastAnalyzedCount]);

  // Generate content for a suggestion
  const handleGenerate = async (suggestion: ContentSuggestion) => {
    const postContent = suggestion.originalPost.content || suggestion.originalPost.text || '';
    
    if (!postContent || postContent.length < 20) {
      toast.error('Post content is too short to generate suggestions');
      return;
    }

    // Set generating state
    setGeneratedContent(prev => new Map(prev.set(suggestion.id, {
      suggestionId: suggestion.id,
      content: '',
      isGenerating: true
    })));

    try {
      const content = await generateContentSuggestion(
        postContent,
        suggestion.originalPost.platform || 'Web',
        suggestion.type
      );

      setGeneratedContent(prev => new Map(prev.set(suggestion.id, {
        suggestionId: suggestion.id,
        content,
        isGenerating: false
      })));

      // Track the content conversion
      await trackContentConversion(
        suggestion.type,
        suggestion.originalPost.id || 'unknown',
        content
      );

      toast.success('Content generated successfully!');
    } catch (error) {
      console.error('Error generating content:', error);
      toast.error('Failed to generate content. Please try again.');
      
      // Remove generating state on error
      setGeneratedContent(prev => {
        const newMap = new Map(prev);
        newMap.delete(suggestion.id);
        return newMap;
      });
    }
  };

  // Save content as draft
  const handleSaveAsDraft = (suggestion: ContentSuggestion) => {
    const generated = generatedContent.get(suggestion.id);
    if (!generated?.content) {
      toast.error('No content to save. Generate content first.');
      return;
    }

    // For now, copy to clipboard as we don't have a draft system
    navigator.clipboard.writeText(generated.content).then(() => {
      toast.success('Content copied to clipboard!');
    }).catch(() => {
      toast.error('Failed to copy content');
    });
  };

  // Schedule content (placeholder functionality)
  const handleSchedule = (suggestion: ContentSuggestion) => {
    const generated = generatedContent.get(suggestion.id);
    if (!generated?.content) {
      toast.error('No content to schedule. Generate content first.');
      return;
    }

    // Placeholder for scheduling functionality
    toast.info('Scheduling feature coming soon! Content copied to clipboard.');
    navigator.clipboard.writeText(generated.content);
  };

  // Format post preview text
  const getPostPreview = (post: Post): string => {
    const content = post.content || post.text || '';
    return content.length > 100 ? content.substring(0, 100) + '...' : content;
  };

  // Get confidence color
  const getConfidenceColor = (confidence: number): string => {
    if (confidence > 0.8) return 'text-green-500';
    if (confidence > 0.6) return 'text-yellow-500';
    return 'text-orange-500';
  };

  // Get engagement color
  const getEngagementColor = (engagement?: string): string => {
    switch (engagement) {
      case 'high': return 'bg-green-500/20 text-green-400';
      case 'medium': return 'bg-yellow-500/20 text-yellow-400';
      case 'low': return 'bg-orange-500/20 text-orange-400';
      default: return 'bg-notely-surface text-notely-text-secondary';
    }
  };

  if (isAnalyzing) {
    return (
      <div className="flex-1 flex items-center justify-center text-center py-8">
        <div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent mx-auto mb-2"></div>
          <p className="text-sm text-notely-text-secondary">Analyzing your saved content...</p>
          <p className="text-xs text-notely-text-tertiary mt-1">Finding repurposing opportunities</p>
        </div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return (
      <div className="text-center py-8">
        <span className="text-4xl mb-2 block">💡</span>
        <p className="text-sm text-notely-text-muted leading-relaxed mb-2">No content suggestions available</p>
        <p className="text-sm text-notely-text-muted leading-relaxed">
          Save more posts to get AI-powered content repurposing ideas
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="uppercase text-xs text-notely-text-muted tracking-wide font-semibold mb-1">
            Content Opportunities
          </h4>
          <p className="text-sm text-notely-text-muted leading-relaxed">
            {suggestions.length} suggestions from your saved posts
          </p>
        </div>
      </div>

      {/* Suggestions Grid */}
      <div className="space-y-3 max-h-[500px] overflow-y-auto">
        {suggestions.map((suggestion) => {
          const generated = generatedContent.get(suggestion.id);
          const isGenerating = generated?.isGenerating || false;
          const hasGenerated = generated?.content && !isGenerating;

          return (
            <div
              key={suggestion.id}
              className="notely-card bg-notely-card border border-notely-border rounded-xl px-6 py-4 hover:scale-[1.02] hover:shadow-notely-lg hover:-translate-y-1 transition-all shadow-notely-md"
            >
              {/* Original Post Preview */}
              <div className="flex items-start space-x-3 mb-3">
                <div className="flex-shrink-0">
                  <PlatformLogo 
                    platform={suggestion.originalPost.platform || 'Web'} 
                    className="w-4 h-4 text-notely-text-secondary" 
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm opacity-70 leading-relaxed line-clamp-2">
                    {getPostPreview(suggestion.originalPost)}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={`text-sm leading-relaxed ${getConfidenceColor(suggestion.confidence)}`}>
                      {Math.round(suggestion.confidence * 100)}% match
                    </span>
                    {suggestion.estimatedEngagement && (
                      <span className={`text-[11px] font-medium rounded-xl px-2 py-1 bg-notely-surface border border-notely-border text-notely-text-secondary`}>
                        {suggestion.estimatedEngagement}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Suggestion */}
              <div className="mb-3">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm">{suggestion.type === 'thread' ? '🧵' :
                                            suggestion.type === 'caption' ? '📝' :
                                            suggestion.type === 'newsletter' ? '📧' :
                                            suggestion.type === 'quote' ? '💬' :
                                            suggestion.type === 'poll' ? '📊' : '⭐'}</span>
                  <h5 className="text-xl font-semibold leading-tight">{suggestion.title}</h5>
                </div>
                <p className="text-sm opacity-70 leading-relaxed">{suggestion.description}</p>
              </div>

              {/* Generated Content Preview */}
              {hasGenerated && (
                <div className="mb-3 px-6 py-4 bg-notely-surface border border-notely-border rounded-lg">
                  <p className="uppercase text-xs text-notely-text-muted tracking-wide mb-1 font-medium">Generated Content:</p>
                  <p className="text-sm leading-relaxed line-clamp-3 text-notely-text-primary">
                    {generated.content}
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleGenerate(suggestion)}
                  disabled={isGenerating}
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-full px-3 py-2 text-[11px] font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 transition-transform"
                >
                  {isGenerating ? (
                    <span className="flex items-center space-x-1">
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                      <span>Generating...</span>
                    </span>
                  ) : hasGenerated ? 'Regenerate' : 'Generate'}
                </button>

                {hasGenerated && (
                  <>
                    <button
                      onClick={() => handleSaveAsDraft(suggestion)}
                      className="notely-card bg-notely-card border border-notely-border rounded-full px-3 py-2 text-[11px] font-medium hover:scale-105 transition-transform text-notely-text-primary"
                    >
                      Save Draft
                    </button>
                    <button
                      onClick={() => handleSchedule(suggestion)}
                      className="notely-card bg-notely-card border border-notely-border rounded-full px-3 py-2 text-[11px] font-medium hover:scale-105 transition-transform text-notely-text-primary"
                    >
                      Schedule
                    </button>
                  </>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      {suggestions.length > 0 && (
        <div className="text-center pt-4">
          <p className="text-sm opacity-70 leading-relaxed">
            💡 Tip: High-confidence suggestions work best for repurposing
          </p>
        </div>
      )}
    </div>
  );
};

export default UseWhatYouSavedWidget;
