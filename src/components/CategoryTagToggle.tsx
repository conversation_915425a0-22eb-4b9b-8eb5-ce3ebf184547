import React, { useState } from 'react';
import MultiItemInput from './MultiItemInput';
import { useTranslation } from '../hooks/useTranslation';

interface CategoryTagToggleProps {
  categories: string[];
  tags: string[];
  allCategories: string[];
  allTags: string[];
  onCategoriesChange: (categories: string[]) => void;
  onTagsChange: (tags: string[]) => void;
  maxCategories?: number;
  maxTags?: number;
  className?: string;
}

const CategoryTagToggle: React.FC<CategoryTagToggleProps> = ({
  categories,
  tags,
  allCategories,
  allTags,
  onCategoriesChange,
  onTagsChange,
  maxCategories = 5,
  maxTags = 10,
  className = ''
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'categories' | 'tags'>('categories');

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Tab Toggle */}
      <div className="flex items-center justify-start">
        <div className="bg-notely-surface/60 rounded-xl p-1 flex shadow-notely-sm">
          <button
            onClick={() => setActiveTab('categories')}
            className={`
              px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200
              ${activeTab === 'categories'
                ? 'bg-gradient-to-r from-notely-sky to-notely-lavender text-white shadow-notely-sm'
                : 'text-notely-text-muted hover:text-notely-text-secondary hover:bg-notely-card/80'
              }
            `}
          >
            {t('categories.title', 'Categories')} ({categories.length})
          </button>
          <button
            onClick={() => setActiveTab('tags')}
            className={`
              px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200
              ${activeTab === 'tags'
                ? 'bg-gradient-to-r from-notely-sky to-notely-lavender text-white shadow-notely-sm'
                : 'text-notely-text-muted hover:text-notely-text-secondary hover:bg-notely-card/80'
              }
            `}
          >
            {t('tags.title', 'Tags')} ({tags.length})
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="space-y-4">
        {activeTab === 'categories' ? (
          <MultiItemInput
            label={t('categories.label', 'Categories')}
            items={categories}
            allItems={allCategories}
            maxItems={maxCategories}
            placeholder={t('categories.placeholder', 'Add categories...')}
            onChange={onCategoriesChange}
          />
        ) : (
          <MultiItemInput
            label={t('tags.label', 'Tags')}
            items={tags}
            allItems={allTags}
            maxItems={maxTags}
            placeholder={t('tags.placeholder', 'Add tags...')}
            onChange={onTagsChange}
          />
        )}
      </div>

      {/* Info Text */}
      <div className="text-xs text-notely-text-muted">
        {activeTab === 'categories' 
          ? t('categories.info', `Add up to ${maxCategories} categories to organize your content`)
          : t('tags.info', `Add up to ${maxTags} tags to make your content searchable`)
        }
      </div>
    </div>
  );
};

export default CategoryTagToggle;
