import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

const TermsOfService: React.FC = () => {
  const [activeSection, setActiveSection] = useState<string>('');

  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll('section[id]');
      
      sections.forEach(section => {
        const sectionTop = (section as HTMLElement).offsetTop;
        const sectionHeight = (section as HTMLElement).offsetHeight;
        const scrollY = window.scrollY;
        
        if (scrollY >= sectionTop - 100 && scrollY < sectionTop + sectionHeight - 100) {
          setActiveSection(section.id);
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Call once on mount
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="bg-notely-bg min-h-screen">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-notely-surface border-b border-notely-border backdrop-blur-lg bg-opacity-80">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link to="/" className="flex items-center">
                <img src="/notely-dark.svg" alt="Notely" className="h-8 w-auto" />
              </Link>
            </div>
            <nav className="flex space-x-8">
              <Link to="/" className="text-notely-text-secondary hover:text-notely-accent transition-colors">
                Home
              </Link>
              <Link to="/terms" className="text-notely-accent font-medium">
                Terms
              </Link>
              <Link to="/privacy" className="text-notely-text-secondary hover:text-notely-accent transition-colors">
                Privacy
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="lg:grid lg:grid-cols-12 lg:gap-8">
          {/* Sidebar */}
          <aside className="hidden lg:block lg:col-span-3">
            <div className="sticky top-24">
              <h3 className="text-lg font-semibold text-notely-text-primary mb-4">On this page</h3>
              <nav className="space-y-2">
                <button 
                  onClick={() => scrollToSection('acceptance')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'acceptance' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Acceptance of Terms
                </button>
                <button 
                  onClick={() => scrollToSection('changes')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'changes' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Changes to Terms
                </button>
                <button 
                  onClick={() => scrollToSection('access')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'access' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Access to Service
                </button>
                <button 
                  onClick={() => scrollToSection('account')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'account' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Account Terms
                </button>
                <button 
                  onClick={() => scrollToSection('content')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'content' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Content & Conduct
                </button>
                <button 
                  onClick={() => scrollToSection('copyright')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'copyright' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Copyright Policy
                </button>
                <button 
                  onClick={() => scrollToSection('subscription')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'subscription' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Subscription Terms
                </button>
                <button 
                  onClick={() => scrollToSection('disclaimer')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'disclaimer' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Disclaimer
                </button>
                <button 
                  onClick={() => scrollToSection('limitation')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'limitation' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Limitation of Liability
                </button>
                <button 
                  onClick={() => scrollToSection('termination')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'termination' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Termination
                </button>
                <button 
                  onClick={() => scrollToSection('contact')}
                  className={`block w-full text-left px-3 py-2 rounded-lg transition-colors ${activeSection === 'contact' ? 'bg-notely-accent/10 text-notely-accent' : 'text-notely-text-secondary hover:bg-notely-hover'}`}
                >
                  Contact Us
                </button>
              </nav>
            </div>
          </aside>

          {/* Main Content */}
          <main className="lg:col-span-9">
            <div className="prose prose-invert max-w-none">
              <h1 className="text-4xl font-bold mb-8 text-notely-text-primary">Terms of Service</h1>
              
              <p className="text-notely-text-secondary mb-8">
                Last updated: June 1, 2023
              </p>

              <section id="acceptance" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Acceptance of Terms</h2>
                <p className="text-notely-text-secondary mb-4">
                  By accessing or using Notely's services, including our browser extension and website (collectively, the "Service"), you agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, you may not access or use the Service.
                </p>
                <p className="text-notely-text-secondary">
                  These Terms constitute a legally binding agreement between you and Notely regarding your use of the Service. Please read them carefully.
                </p>
              </section>

              <section id="changes" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Changes to Terms</h2>
                <p className="text-notely-text-secondary mb-4">
                  We reserve the right to modify these Terms at any time. If we make changes, we will provide notice by updating the date at the top of these Terms and by maintaining a current version of the Terms at https://notely.social/terms.
                </p>
                <p className="text-notely-text-secondary">
                  Your continued use of our Service after any such change constitutes your acceptance of the new Terms. If you do not agree to any of these Terms or any future Terms, do not use or access (or continue to access) the Service.
                </p>
              </section>

              <section id="access" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Access to Service</h2>
                <p className="text-notely-text-secondary mb-4">
                  Notely grants you a personal, non-transferable, non-exclusive, revocable, limited license to use the Service for your own personal, non-commercial purposes, subject to these Terms.
                </p>
                <p className="text-notely-text-secondary mb-4">
                  We reserve the right to modify, suspend, or discontinue the Service (in whole or in part) at any time, with or without notice to you. You agree that we will not be liable to you or to any third party for any modification, suspension, or discontinuation of the Service.
                </p>
                <p className="text-notely-text-secondary">
                  We reserve the right to refuse access to the Service to anyone for any reason at any time.
                </p>
              </section>

              <section id="account" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Account Terms</h2>
                <p className="text-notely-text-secondary mb-4">
                  To use certain features of the Service, you may be required to create an account. You are responsible for:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">Providing accurate and complete information when creating your account</li>
                  <li className="mb-2">Maintaining the security of your account credentials</li>
                  <li className="mb-2">All activities that occur under your account</li>
                  <li className="mb-2">Notifying us immediately of any unauthorized use of your account</li>
                </ul>
                <p className="text-notely-text-secondary">
                  We reserve the right to suspend or terminate your account if any information provided during the registration process or thereafter proves to be inaccurate, false, or misleading, or if you violate any provision of these Terms.
                </p>
              </section>

              <section id="content" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Content & Conduct</h2>
                <p className="text-notely-text-secondary mb-4">
                  Our Service allows you to save, store, and organize content from social media platforms. You are solely responsible for the content you save using our Service.
                </p>
                <p className="text-notely-text-secondary mb-4">
                  You agree not to use the Service to:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">Save, store, or share content that violates any applicable law or regulation</li>
                  <li className="mb-2">Infringe upon or violate the intellectual property rights or any other rights of others</li>
                  <li className="mb-2">Transmit any material that is defamatory, offensive, or otherwise objectionable</li>
                  <li className="mb-2">Interfere with or disrupt the Service or servers or networks connected to the Service</li>
                  <li className="mb-2">Attempt to gain unauthorized access to any portion of the Service</li>
                </ul>
                <p className="text-notely-text-secondary">
                  We reserve the right to remove any content that violates these Terms or that we find objectionable for any reason, without prior notice.
                </p>
              </section>

              <section id="copyright" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Copyright Policy</h2>
                <p className="text-notely-text-secondary mb-4">
                  We respect the intellectual property rights of others and expect our users to do the same. We will respond to notices of alleged copyright infringement that comply with applicable law.
                </p>
                <p className="text-notely-text-secondary mb-4">
                  If you believe that your copyrighted work has been copied in a way that constitutes copyright infringement, please provide us with the following information:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">A physical or electronic signature of the copyright owner or a person authorized to act on their behalf</li>
                  <li className="mb-2">Identification of the copyrighted work claimed to have been infringed</li>
                  <li className="mb-2">Identification of the material that is claimed to be infringing and where it is located on the Service</li>
                  <li className="mb-2">Your contact information, including your address, telephone number, and email</li>
                  <li className="mb-2">A statement that you have a good faith belief that use of the material in the manner complained of is not authorized by the copyright owner, its agent, or law</li>
                  <li className="mb-2">A statement, made under penalty of perjury, that the above information is accurate, and that you are the copyright owner or are authorized to act on behalf of the owner</li>
                </ul>
                <p className="text-notely-text-secondary">
                  We reserve the right to remove content alleged to be infringing without prior notice, at our sole discretion, and without liability to you.
                </p>
              </section>

              <section id="subscription" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Subscription Terms</h2>
                <p className="text-notely-text-secondary mb-4">
                  Some features of the Service may require a subscription. By subscribing to these features, you agree to the following terms:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">Subscriptions are billed in advance on a recurring basis</li>
                  <li className="mb-2">You authorize us to charge your payment method for the subscription fees</li>
                  <li className="mb-2">Subscriptions automatically renew unless canceled before the renewal date</li>
                  <li className="mb-2">You can cancel your subscription at any time through your account settings</li>
                  <li className="mb-2">No refunds will be provided for partial subscription periods</li>
                </ul>
                <p className="text-notely-text-secondary">
                  We reserve the right to change subscription fees upon reasonable notice. Such notice may be provided at any time by posting the changes to the Notely website or via email.
                </p>
              </section>

              <section id="disclaimer" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Disclaimer of Warranties</h2>
                <p className="text-notely-text-secondary mb-4">
                  THE SERVICE IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE, AND NON-INFRINGEMENT.
                </p>
                <p className="text-notely-text-secondary mb-4">
                  NOTELY DOES NOT WARRANT THAT THE SERVICE WILL BE UNINTERRUPTED OR ERROR-FREE, THAT DEFECTS WILL BE CORRECTED, OR THAT THE SERVICE OR THE SERVERS THAT MAKE IT AVAILABLE ARE FREE OF VIRUSES OR OTHER HARMFUL COMPONENTS.
                </p>
                <p className="text-notely-text-secondary">
                  NOTELY MAKES NO WARRANTIES ABOUT THE ACCURACY, RELIABILITY, COMPLETENESS, OR TIMELINESS OF THE SERVICE.
                </p>
              </section>

              <section id="limitation" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Limitation of Liability</h2>
                <p className="text-notely-text-secondary mb-4">
                  TO THE MAXIMUM EXTENT PERMITTED BY LAW, IN NO EVENT SHALL NOTELY, ITS AFFILIATES, OFFICERS, DIRECTORS, EMPLOYEES, OR AGENTS BE LIABLE FOR ANY INDIRECT, PUNITIVE, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR EXEMPLARY DAMAGES, INCLUDING WITHOUT LIMITATION DAMAGES FOR LOSS OF PROFITS, GOODWILL, USE, DATA, OR OTHER INTANGIBLE LOSSES, THAT RESULT FROM THE USE OF, OR INABILITY TO USE, THE SERVICE.
                </p>
                <p className="text-notely-text-secondary mb-4">
                  UNDER NO CIRCUMSTANCES WILL NOTELY BE RESPONSIBLE FOR ANY DAMAGE, LOSS, OR INJURY RESULTING FROM HACKING, TAMPERING, OR OTHER UNAUTHORIZED ACCESS OR USE OF THE SERVICE OR YOUR ACCOUNT OR THE INFORMATION CONTAINED THEREIN.
                </p>
                <p className="text-notely-text-secondary">
                  NOTELY ASSUMES NO LIABILITY OR RESPONSIBILITY FOR ANY CONTENT THAT YOU OR OTHER USERS SAVE OR SHARE THROUGH THE SERVICE.
                </p>
              </section>

              <section id="termination" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Termination</h2>
                <p className="text-notely-text-secondary mb-4">
                  We may terminate or suspend your access to the Service immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach these Terms.
                </p>
                <p className="text-notely-text-secondary mb-4">
                  Upon termination, your right to use the Service will immediately cease. If you wish to terminate your account, you may simply discontinue using the Service or delete your account through the account settings.
                </p>
                <p className="text-notely-text-secondary">
                  All provisions of these Terms which by their nature should survive termination shall survive termination, including, without limitation, ownership provisions, warranty disclaimers, indemnity, and limitations of liability.
                </p>
              </section>

              <section id="contact" className="mb-12">
                <h2 className="text-2xl font-semibold mb-4 text-notely-text-primary">Contact Us</h2>
                <p className="text-notely-text-secondary mb-4">
                  If you have any questions about these Terms, please contact us:
                </p>
                <ul className="list-disc pl-6 mb-4 text-notely-text-secondary">
                  <li className="mb-2">By email: <EMAIL></li>
                  <li className="mb-2">By visiting the contact page on our website: https://notely.social/contact</li>
                </ul>
              </section>
            </div>
          </main>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-notely-surface border-t border-notely-border py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <img src="/notely-dark.svg" alt="Notely" className="h-6 w-auto" />
            </div>
            <div className="flex items-center space-x-6">
              <Link to="/privacy" className="text-notely-text-secondary hover:text-notely-accent transition-colors">Privacy Policy</Link>
              <Link to="/terms" className="text-notely-accent transition-colors">Terms of Service</Link>
              <a href="mailto:<EMAIL>" className="text-notely-text-secondary hover:text-notely-accent transition-colors">Support</a>
            </div>
          </div>
          <div className="mt-8 text-center text-sm text-notely-text-muted">
            © 2023 Notely. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TermsOfService;
