import React, { useState, useEffect } from 'react';
import { formatForDisplay } from '../utils/formatUtils';
import { getTagStyles } from '../styles/categoryTagConfig';
import { getCurrentTheme, onThemeChange } from '../utils/themeUtils';

interface SimpleTagSelectorProps {
  tags: string[];
  selectedTag: string | null;
  onTagSelect: (tag: string | null) => void;
  className?: string;
}

const SimpleTagSelector: React.FC<SimpleTagSelectorProps> = ({
  tags,
  selectedTag,
  onTagSelect,
  className = ''
}) => {
  const [isDarkMode, setIsDarkMode] = useState(true); // Default to dark, will be updated by useEffect

  // Listen for theme changes and update state
  useEffect(() => {
    // Get initial theme using async method to respect stored preferences
    getCurrentTheme().then((theme) => {
      setIsDarkMode(theme === 'dark');
    });

    // Listen for theme changes
    const unsubscribe = onThemeChange((newTheme) => {
      setIsDarkMode(newTheme === 'dark');
    });

    return unsubscribe;
  }, []);

  if (tags.length === 0) {
    return null;
  }

  const getButtonStyles = (isSelected: boolean) => {
    // Use the reactive isDarkMode state instead of calling detectCurrentTheme() every time
    return getTagStyles(isSelected, isDarkMode);
  };

  return (
    <div className={`notely-breathing-lg ${className}`}>
      <div className="flex flex-wrap gap-2 py-1.5">
        {tags.map((tag, index) => (
          <button
            key={tag}
            onClick={() => onTagSelect(selectedTag === tag ? null : tag)}
            className={`${getButtonStyles(selectedTag === tag)} notely-tag-button`}
            style={{ animationDelay: `${index * 0.05 + 0.1}s` }}
          >
            <span className="truncate">
              {formatForDisplay(tag)}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default SimpleTagSelector;
