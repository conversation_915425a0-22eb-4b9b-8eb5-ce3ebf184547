import React, { useState, useEffect } from 'react';
import { BsSun, BsMoon } from 'react-icons/bs';
import { getCurrentTheme, toggleTheme as utilsToggleTheme, onThemeChange, type Theme } from '../utils/themeUtils';

interface ThemeToggleProps {
  className?: string;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const [isDark, setIsDark] = useState(true); // Default to dark mode

  useEffect(() => {
    // Load current theme using centralized utilities
    getCurrentTheme().then((theme: Theme) => {
      setIsDark(theme === 'dark');
    });

    // Listen for theme changes from other sources
    const cleanup = onThemeChange((theme: Theme) => {
      setIsDark(theme === 'dark');
    });

    return cleanup;
  }, []);

  const handleToggleTheme = async () => {
    try {
      const newTheme = await utilsToggleTheme();
      setIsDark(newTheme === 'dark');
    } catch (error) {
      console.error('Failed to toggle theme:', error);
    }
  };

  return (
    <button
      onClick={handleToggleTheme}
      className={`
        relative inline-flex items-center justify-center
        w-12 h-6 rounded-full
        transition-all duration-300 ease-in-out
        hover:shadow-notely-lg hover:scale-105
        focus:outline-none focus:ring-2 focus:ring-notely-lavender/30 focus:ring-offset-2
        ${isDark
          ? 'bg-gradient-to-r from-indigo-500/90 to-purple-500/90 border border-indigo-400/20 shadow-notely-md'
          : 'bg-gradient-to-r from-yellow-400 to-orange-400 border border-orange-300/30 shadow-notely-sm'
        }
        ${className}
      `}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
    >
      {/* Toggle circle */}
      <div
        className={`
          relative z-10 flex items-center justify-center
          w-5 h-5 rounded-full
          transition-all duration-300 ease-in-out
          transform ${isDark ? 'translate-x-3' : '-translate-x-3'}
          ${isDark
            ? 'bg-notely-surface/95 shadow-notely-sm border border-white/10'
            : 'bg-white shadow-notely-md border border-orange-200/50'
          }
        `}
      >
        {isDark ? (
          <BsMoon className="w-3 h-3 text-indigo-300" />
        ) : (
          <BsSun className="w-3 h-3 text-orange-600" />
        )}
      </div>
    </button>
  );
};

export default ThemeToggle;
