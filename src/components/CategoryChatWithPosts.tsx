import React, { useState, useEffect, useRef } from 'react';
import { Post } from '../types';
import { formatForDisplay } from '../utils/formatUtils';
import { toast } from '../utils/toast';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
}

interface CategoryChatWithPostsProps {
  posts: Post[];
  category: string;
  className?: string;
  onOpenPost?: (postId: string) => void;
}

const CategoryChatWithPosts: React.FC<CategoryChatWithPostsProps> = ({
  posts,
  category,
  className = '',
  onOpenPost
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string>('');
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Add flag to prevent duplicate conversation creation during clear
  const [isClearingHistory, setIsClearingHistory] = useState(false);
  // Use ref to track if we just completed a clear to prevent useEffect from creating duplicate
  const justCompletedClear = useRef(false);

  // Generate storage key for this category
  const storageKey = `category-chat-${category.toLowerCase().replace(/\s+/g, '-')}`;

  // Debug logging
  useEffect(() => {
    console.log(`[CategoryChat] Initialized for category: ${category}, storage key: ${storageKey}, posts: ${posts.length}`);
  }, [category, storageKey, posts.length]);

  // Track category changes specifically
  useEffect(() => {
    console.log(`[CategoryChat] Category or storage key changed - category: ${category}, storageKey: ${storageKey}`);
    console.log(`[CategoryChat] Current conversations count: ${conversations.length}`);
  }, [category, storageKey]);

  // Get current conversation
  const currentConversation = conversations.find(c => c.id === currentConversationId) || {
    id: '',
    title: '',
    messages: [],
    createdAt: new Date()
  };

  // Category-specific smart action prompts
  const categorySmartActions = [
    {
      label: 'Analyze trends',
      prompt: `Analyze the trends and patterns in my ${formatForDisplay(category)} posts. What themes emerge?`
    },
    {
      label: 'Extract insights',
      prompt: `What are the key insights and takeaways from my ${formatForDisplay(category)} content?`
    },
    {
      label: 'Find connections',
      prompt: `How do the posts in my ${formatForDisplay(category)} category connect to each other?`
    },
    {
      label: 'Suggest actions',
      prompt: `Based on my ${formatForDisplay(category)} posts, what actions or next steps would you recommend?`
    },
    {
      label: 'Create summary',
      prompt: `Create a comprehensive summary of my ${formatForDisplay(category)} posts and their main themes.`
    }
  ];

  // Load conversations from localStorage on mount
  useEffect(() => {
    const loadConversations = async () => {
      try {
        console.log(`[CategoryChat] Loading conversations for category: ${category}, storage key: ${storageKey}`);
        console.log(`[CategoryChat] isClearingHistory flag: ${isClearingHistory}`);
        console.log(`[CategoryChat] justCompletedClear ref: ${justCompletedClear.current}`);
        const savedConversations = localStorage.getItem(storageKey);
        console.log(`[CategoryChat] Raw localStorage data: ${savedConversations ? 'exists' : 'null'}`);

        if (savedConversations) {
          const parsed = JSON.parse(savedConversations);
          console.log(`[CategoryChat] Parsed ${parsed.length} conversations from localStorage for ${category}`);

          // Convert date strings back to Date objects
          const conversationsWithDates = parsed.map((conv: any) => ({
            ...conv,
            createdAt: new Date(conv.createdAt),
            messages: conv.messages.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }))
          }));

          setConversations(conversationsWithDates);
          if (conversationsWithDates.length > 0) {
            setCurrentConversationId(conversationsWithDates[0].id);
          }
          console.log(`[CategoryChat] Loaded ${conversationsWithDates.length} conversations for ${category}`);
        } else if (!isClearingHistory && !justCompletedClear.current) {
          // Only create new conversation if we're not in the middle of clearing and didn't just clear
          console.log(`[CategoryChat] No saved conversations found for ${category}, creating new conversation`);
          handleNewConversation();
        } else {
          console.log(`[CategoryChat] No conversations found but clearing in progress or just completed, skipping new conversation creation`);
        }
        
        // Reset the clear completion flag after this load attempt
        if (justCompletedClear.current) {
          justCompletedClear.current = false;
          console.log(`[CategoryChat] Reset justCompletedClear flag`);
        }
      } catch (error) {
        console.error(`[CategoryChat] Error loading conversations for ${category}:`, error);
        if (!isClearingHistory && !justCompletedClear.current) {
          handleNewConversation();
        }
      }
    };

    loadConversations();
  }, [storageKey, category]);

  // Save conversations to localStorage whenever they change
  useEffect(() => {
    // Don't save empty conversations array (it means we're clearing)
    if (conversations.length > 0) {
      try {
        // Limit to last 20 conversations to prevent storage bloat
        const limitedConversations = conversations.slice(0, 20);
        
        // DEBUG: Log what conversations we're about to save
        console.log(`[CategoryChat] About to save ${limitedConversations.length} conversations for ${category}:`);
        limitedConversations.forEach((conv, index) => {
          console.log(`[CategoryChat] Conversation ${index}: ID=${conv.id}, title="${conv.title}", messages=${conv.messages.length}`);
        });
        
        localStorage.setItem(storageKey, JSON.stringify(limitedConversations));
        console.log(`[CategoryChat] Saved ${limitedConversations.length} conversations for ${category}`);
      } catch (error) {
        console.error('Error saving conversations:', error);
      }
    } else {
      console.log(`[CategoryChat] Skipping save - conversations array is empty (likely cleared) for ${category}`);
    }
  }, [conversations, storageKey, category]);

  // Auto-scroll to bottom when new messages arrive (but not on initial mount)
  useEffect(() => {
    // Only scroll if there are messages and it's not the initial empty state
    if (currentConversation.messages.length > 0) {
      // Scroll the internal messages container instead of using scrollIntoView
      // to prevent page-level scrolling
      const messagesContainer = messagesEndRef.current?.closest('.overflow-y-auto');
      if (messagesContainer) {
        messagesContainer.scrollTo({
          top: messagesContainer.scrollHeight,
          behavior: 'smooth'
        });
      }
    }
  }, [currentConversation.messages]);

  const handleNewConversation = () => {
    console.log(`[CategoryChat] handleNewConversation called for ${category}`);
    console.log(`[CategoryChat] Current conversations before adding new: ${conversations.length}`);
    console.log(`[CategoryChat] Current conversations IDs:`, conversations.map(c => c.id));
    
    // Add stack trace to see where this is being called from
    console.log(`[CategoryChat] handleNewConversation called from:`, new Error().stack);
    
    const newConversation: Conversation = {
      id: Date.now().toString(),
      title: `${formatForDisplay(category)} Chat`,
      messages: [{
        id: '1',
        type: 'ai',
        content: `Hi, I'm Nelo – your assistant inside Notely for the ${formatForDisplay(category)} category. I have access to your ${posts.length} posts in this category and can help you analyze patterns, extract insights, find connections, or answer questions about this specific content area. What would you like to explore?`,
        timestamp: new Date()
      }],
      createdAt: new Date()
    };
    
    console.log(`[CategoryChat] Creating new conversation with ID: ${newConversation.id}`);
    const newConversations = [newConversation, ...conversations];
    console.log(`[CategoryChat] New conversations array will have ${newConversations.length} items`);
    
    setConversations(newConversations);
    setCurrentConversationId(newConversation.id);
  };

  const handleDeleteConversation = (conversationId: string, e?: React.MouseEvent) => {
    e?.stopPropagation();
    const updatedConversations = conversations.filter(c => c.id !== conversationId);
    setConversations(updatedConversations);

    if (currentConversationId === conversationId) {
      if (updatedConversations.length > 0) {
        setCurrentConversationId(updatedConversations[0].id);
      } else {
        handleNewConversation();
      }
    }
  };

  const handleClearAllHistory = () => {
    try {
      console.log(`[CategoryChat] Clearing all history for ${category}, storage key: ${storageKey}`);
      console.log(`[CategoryChat] Current conversations before clear: ${conversations.length}`);
      
      // Set flag to prevent loadConversations from creating new conversation
      setIsClearingHistory(true);
      justCompletedClear.current = true; // Set ref flag to prevent useEffect duplicate
      
      // Clear conversations state first
      setConversations([]);
      // Remove from localStorage
      localStorage.removeItem(storageKey);
      console.log(`[CategoryChat] LocalStorage cleared for key: ${storageKey}`);
      
      // Verify localStorage is actually cleared
      const afterClear = localStorage.getItem(storageKey);
      console.log(`[CategoryChat] LocalStorage after clear: ${afterClear}`);
      
      // Use setTimeout to ensure the clear operation completes before adding new conversation
      // Use functional state update to avoid closure issues with stale state
      setTimeout(() => {
        console.log(`[CategoryChat] Creating new conversation after clear for ${category}`);
        
        const newConversation: Conversation = {
          id: Date.now().toString(),
          title: `${formatForDisplay(category)} Chat`,
          messages: [{
            id: '1',
            type: 'ai',
            content: `Hi, I'm Nelo – your assistant inside Notely for the ${formatForDisplay(category)} category. I have access to your ${posts.length} posts in this category and can help you analyze patterns, extract insights, find connections, or answer questions about this specific content area. What would you like to explore?`,
            timestamp: new Date()
          }],
          createdAt: new Date()
        };
        
        console.log(`[CategoryChat] Setting conversations to single new conversation with ID: ${newConversation.id}`);
        // Use functional update to ensure we're working with the latest (cleared) state
        setConversations(prevConversations => {
          console.log(`[CategoryChat] Previous conversations length in functional update: ${prevConversations.length}`);
          return [newConversation];
        });
        setCurrentConversationId(newConversation.id);
        
        // Clear the flag after we're done
        setIsClearingHistory(false);
        console.log(`[CategoryChat] Clear operation completed, flag reset`);
      }, 50);
      toast.success(`Cleared all ${formatForDisplay(category)} chat history`);
    } catch (error) {
      console.error('Error clearing chat history:', error);
      setIsClearingHistory(false); // Reset flag on error
      justCompletedClear.current = false; // Reset ref flag on error
      toast.error('Failed to clear chat history');
    }
  };



  const generateCategoryAIResponse = async (userInput: string, categoryPosts: Post[], category: string): Promise<string> => {
    try {
      // Get authentication token
      let token: string | null = null;
      try {
        const result = await chrome.storage.local.get(['authToken']);
        token = result.authToken || null;
      } catch (error) {
        console.warn('[CategoryChatWithPosts] Could not get auth token:', error);
      }

      if (!token) {
        return "Authentication required. Please log in to use AI chat.";
      }

      // Call secure backend endpoint
      const response = await fetch('https://api.notely.social/api/posts/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          message: `[CATEGORY: ${category}] ${userInput}`,
          posts: categoryPosts
        })
      });

      if (!response.ok) {
        if (response.status === 401) {
          return "Authentication expired. Please log in again.";
        }
        throw new Error(`AI chat request failed: ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.data && result.data.response) {
        return result.data.response;
      } else {
        throw new Error('Invalid response from AI service');
      }
    } catch (error) {
      console.error('Category AI Response Error:', error);
      return "I'm having trouble connecting to the AI service right now. Please try again later.";
    }
  };

  const generateConversationTitle = (firstMessage: string): string => {
    const words = firstMessage.split(' ').slice(0, 4);
    return words.join(' ') + (firstMessage.split(' ').length > 4 ? '...' : '');
  };

  const handleSendMessage = async (messageContent: string) => {
    if (!messageContent.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: messageContent.trim(),
      timestamp: new Date()
    };

    // Update conversation title if this is the first user message
    let updatedConversation = { ...currentConversation };
    if (currentConversation.messages.length === 1) { // Only initial AI message
      updatedConversation.title = generateConversationTitle(messageContent.trim());
    }

    updatedConversation.messages = [...updatedConversation.messages, userMessage];

    // Update conversations state
    const updatedConversations = conversations.map(conv => {
      if (conv.id === currentConversationId) {
        return updatedConversation;
      }
      return conv;
    });
    setConversations(updatedConversations);
    setInputMessage('');
    setIsLoading(true);

    try {
      const aiResponse = await generateCategoryAIResponse(messageContent.trim(), posts, category);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      };

      updatedConversation.messages = [...updatedConversation.messages, aiMessage];

      // Update conversations with AI response
      setConversations(prevConversations =>
        prevConversations.map(conv => {
          if (conv.id === currentConversationId) {
            return updatedConversation;
          }
          return conv;
        })
      );
    } catch (error) {
      console.error('Error generating AI response:', error);
      toast.error('Failed to get AI response. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSmartAction = (prompt: string) => {
    setInputMessage(prompt);
    handleSendMessage(prompt);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(inputMessage);
    }
  };

  const formatAIResponse = (content: string) => {
    // Function to handle post references in text
    const processTextWithPostRefs = (text: string): (string | JSX.Element)[] => {
      const parts: (string | JSX.Element)[] = [];
      const postRefRegex = /\[POST:([^\]]+)\]/g;
      let lastIndex = 0;
      let match;

      while ((match = postRefRegex.exec(text)) !== null) {
        // Add text before the reference
        if (match.index > lastIndex) {
          parts.push(text.slice(lastIndex, match.index));
        }

        // Add the clickable post reference
        const postId = match[1];
        const referencedPost = posts.find(p => p.id === postId);
        
        if (referencedPost && onOpenPost) {
          parts.push(
            <button
              key={`post-ref-${postId}-${match.index}`}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onOpenPost(referencedPost.id);
              }}
              className="inline-flex items-center mx-0.5 px-1.5 py-0.5 text-xs bg-notely-surface hover:bg-notely-surface/80 dark:bg-notely-surface/60 dark:hover:bg-notely-surface/80 text-notely-text-secondary hover:text-notely-text-primary border border-notely-border/10 dark:border-notely-border-dark/20 hover:border-notely-border/20 dark:hover:border-notely-border-dark/30 rounded transition-colors cursor-pointer group"
              title={`View ${referencedPost.platform} post by ${referencedPost.author || referencedPost.authorName}`}
            >
              <svg className="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              <span className="truncate">Source Post</span>
            </button>
          );
        } else {
          // If post not found, just show text
          parts.push(`[Source Post]`);
        }

        lastIndex = postRefRegex.lastIndex;
      }

      // Add remaining text
      if (lastIndex < text.length) {
        parts.push(text.slice(lastIndex));
      }

      return parts;
    };

    return content.split('\n').map((paragraph, index) => (
      <p key={index} className="mb-2 last:mb-0">
        {processTextWithPostRefs(paragraph)}
      </p>
    ));
  };

  // Generate chat history for display - only category-specific conversations
  const chatHistory = conversations.map(conv => ({
    id: conv.id,
    title: conv.title.includes(formatForDisplay(category)) ?
      conv.title :
      (conv.messages.length > 1 ?
        (conv.messages[1].content.length > 30 ?
          conv.messages[1].content.substring(0, 30) + '...' :
          conv.messages[1].content) :
        conv.title),
    date: conv.createdAt.toLocaleDateString(),
    messageCount: conv.messages.length
  }));

  return (
    <div className={`${className} w-full`}>
      {/* Category AI Assistant - Compact Layout */}
      <div className="space-y-4">
        {/* Main Chat Interface */}
        <div className="notely-card bg-notely-card rounded-notely-xl border border-notely-border/10 dark:border-notely-border-dark/20 overflow-hidden shadow-notely-md hover:shadow-notely-lg notely-filter-transition">
          {/* Header */}
          <div className="px-4 py-3 border-b border-notely-border/10 dark:border-notely-border-dark/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-notely-text-primary">
                    Nelo - {formatForDisplay(category)}
                  </h3>
                  <p className="text-sm text-notely-text-secondary">
                    🤖 Ready to analyze your {posts.length} posts
                  </p>
                </div>
              </div>
              <button
                onClick={handleNewConversation}
                className="text-notely-text-muted hover:text-notely-lavender transition-colors p-1 rounded-md hover:bg-notely-hover"
                title="New conversation"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="px-4 py-3">
            <p className="text-sm text-notely-text-primary leading-relaxed">
              Hi, I'm Nelo – your assistant inside Notely for the <strong>{formatForDisplay(category)}</strong> category. I have access to your <strong>{posts.length} posts</strong> in this category and can help you analyze patterns, extract insights, find connections, or answer questions about this specific content area.
            </p>
          </div>

          {/* CTA Button */}
          <div className="px-4 pb-4">
            <button
              onClick={() => handleSendMessage('What are the key insights and patterns in my posts?')}
              className="w-full px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 shadow-notely-sm hover:shadow-notely-md flex items-center justify-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>Start Exploring Insights</span>
            </button>
          </div>

          {/* Chat Interface Container */}
          <div className="mt-6 border-t border-gray-200 pt-4">

          {/* Messages Area */}
          <div className="h-48 overflow-y-auto px-4 py-3 space-y-3">
            {currentConversation.messages.length === 0 ? (
              <div className="text-center py-6">
                <div className="w-8 h-8 mx-auto mb-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">🧠</span>
                </div>
                <h4 className="text-sm font-semibold text-notely-text-primary mb-2">
                  Ask about your {formatForDisplay(category)} posts
                </h4>
                <p className="text-xs text-notely-text-muted leading-relaxed">
                  I can help analyze patterns and extract insights from your {formatForDisplay(category)} content.
                </p>
              </div>
            ) : (
              currentConversation.messages.map((message) => (
                <div key={message.id} className="space-y-3">
                  {message.type === 'ai' && (
                    <div className="flex items-start space-x-2">
                      <div className="w-6 h-6 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 border border-notely-border/5 dark:border-purple-500/30 shadow-notely-xs flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-xs">🧠</span>
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="text-notely-text-primary text-xs leading-relaxed">
                          {formatAIResponse(message.content)}
                        </div>
                      </div>
                    </div>
                  )}
                  {message.type === 'user' && (
                    <div className="flex justify-end">
                      <div className="max-w-[80%] px-3 py-2 bg-gradient-to-br from-indigo-500 to-purple-600 text-white rounded-lg rounded-br-sm shadow-notely-sm">
                        <p className="text-xs leading-relaxed">{message.content}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
            {isLoading && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 border border-notely-border/5 dark:border-purple-500/30 shadow-notely-xs flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xs">🧠</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-indigo-500"></div>
                    <span className="text-xs text-notely-text-muted">Thinking...</span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Smart Actions */}
          <div className="px-4 py-3 border-t border-notely-border/10 dark:border-notely-border-dark/20">
            <div className="flex flex-wrap gap-1.5 mb-3">
              {categorySmartActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => handleSmartAction(action.prompt)}
                  disabled={isLoading}
                  className="px-2 py-1 text-[10px] font-medium rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-notely-sm hover:shadow-notely-md"
                >
                  {action.label}
                </button>
              ))}
            </div>

            {/* Input Area */}
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={`Ask about ${formatForDisplay(category)}...`}
                disabled={isLoading}
                className="flex-1 px-3 py-2 text-xs bg-notely-surface border border-notely-border/20 dark:border-notely-border-dark/30 rounded-lg text-notely-text-primary placeholder-notely-text-muted focus:outline-none focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500/30 transition-all disabled:opacity-50"
              />
              <button
                onClick={() => handleSendMessage(inputMessage)}
                disabled={isLoading || !inputMessage.trim()}
                className="px-3 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-notely-sm hover:shadow-notely-md"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Chat History - Compact */}
        {conversations.length > 0 && (
          <div className="notely-card bg-notely-card rounded-notely-xl border border-notely-border/10 dark:border-notely-border-dark/20 overflow-hidden shadow-notely-md hover:shadow-notely-lg notely-filter-transition">
            <div className="px-4 py-3 border-b border-notely-border/10 dark:border-notely-border-dark/20">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-semibold text-notely-text-primary">{formatForDisplay(category)} Chat History</h4>
                <button
                  onClick={handleClearAllHistory}
                  className="text-notely-text-muted hover:text-red-500 transition-colors text-xs"
                  title="Clear All History"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="max-h-32 overflow-y-auto px-4 py-2 space-y-1">
              {chatHistory.map(item => (
                <div
                  key={item.id}
                  className={`p-2 hover:bg-notely-hover rounded-lg cursor-pointer notely-filter-transition group border border-transparent hover:border-notely-border/5 dark:hover:border-notely-border-dark/10 hover:shadow-notely-xs ${
                    currentConversation.id === item.id ? 'bg-notely-lavender/10 border-notely-lavender/20' : ''
                  }`}
                >
                  <div className="flex flex-col" onClick={() => setCurrentConversationId(item.id)}>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-notely-text-muted text-[10px]">{item.date}</span>
                      <div className="flex items-center space-x-1">
                        <span className="text-notely-text-muted text-[10px]">{item.messageCount} msgs</span>
                        <button
                          onClick={(e) => handleDeleteConversation(item.id, e)}
                          className="opacity-0 group-hover:opacity-100 text-notely-text-muted hover:text-red-500 transition-all"
                          title="Delete conversation"
                        >
                          <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <span className="text-notely-text-primary group-hover:text-notely-lavender notely-filter-transition text-[10px] font-medium truncate leading-tight">
                      {item.title}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      </div>
    </div>
  );
};

export default CategoryChatWithPosts;
