import React, { useRef, useEffect, useState } from 'react';
import { formatForDisplay } from '../utils/formatUtils';
import { getCategoryTagStyles, getCountBadgeStyles } from '../styles/categoryTagConfig';
import { getCurrentTheme, onThemeChange } from '../utils/themeUtils';

interface SimpleCategorySelectorProps {
  categories: string[];
  selectedCategory: string | null;
  onCategorySelect: (category: string | null) => void;
  className?: string;
  categoryCounts?: Record<string, number>;
}

const SimpleCategorySelector: React.FC<SimpleCategorySelectorProps> = ({
  categories,
  selectedCategory,
  onCategorySelect,
  className = '',
  categoryCounts = {}
}) => {
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true); // Default to dark, will be updated by useEffect
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkScroll = () => {
      if (containerRef.current) {
        const { scrollWidth, clientWidth } = containerRef.current;
        setShowScrollButtons(scrollWidth > clientWidth);
      }
    };

    checkScroll();
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, [categories]);

  // Listen for theme changes and update state
  useEffect(() => {
    // Get initial theme using async method to respect stored preferences
    getCurrentTheme().then((theme) => {
      setIsDarkMode(theme === 'dark');
    });

    // Listen for theme changes
    const unsubscribe = onThemeChange((newTheme) => {
      setIsDarkMode(newTheme === 'dark');
    });

    return unsubscribe;
  }, []);

  const scroll = (direction: 'left' | 'right') => {
    if (containerRef.current) {
      const scrollAmount = direction === 'left' ? -200 : 200;
      containerRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  if (categories.length === 0) {
    return null;
  }

  const getButtonStyles = (isSelected: boolean) => {
    // Use the reactive isDarkMode state instead of calling detectCurrentTheme() every time
    const styles = getCategoryTagStyles(isSelected, isDarkMode);

    // Add checkmark for selected state with theme-aware color
    if (isSelected) {
      const checkmarkColor = isDarkMode ? 'after:text-white/80' : 'after:text-purple-600';
      return `${styles} pr-5 after:content-['✓'] after:absolute after:top-1/2 after:right-1.5 after:text-[10px] ${checkmarkColor} after:-translate-y-1/2`;
    }

    return styles;
  };

  const getScrollButtonStyles = (direction: 'left' | 'right') => {
    // Use the reactive isDarkMode state instead of calling detectCurrentTheme() every time
    if (isDarkMode) {
      // Dark mode: light arrows with solid dark background
      return `bg-gray-800/90 text-white border border-gray-700 hover:bg-gray-700/90 hover:text-white shadow-lg backdrop-blur-sm`;
    } else {
      // Light mode: dark arrows with solid light background
      return `bg-white/90 text-gray-700 border border-gray-300 hover:bg-gray-50/90 hover:text-gray-900 shadow-lg backdrop-blur-sm`;
    }
  };

  return (
    <div className={`notely-breathing-lg ${className}`}>
      <div className="relative">
        {/* Scroll Left Button */}
      {showScrollButtons && (
        <button
          onClick={() => scroll('left')}
          className={`absolute left-1 top-1/2 -translate-y-1/2 z-20 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-200 ease-out hover:scale-110 active:scale-95 ${getScrollButtonStyles('left')}`}
          aria-label="Scroll left"
        >
          ◀
        </button>
      )}
      
      {/* Categories Container */}
      <div
        ref={containerRef}
        className={`flex gap-2 py-1.5 overflow-x-auto scrollbar-hide scroll-smooth ${showScrollButtons ? 'px-10' : ''}`}
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => onCategorySelect(selectedCategory === category ? null : category)}
            className={getButtonStyles(selectedCategory === category)}
          >
            <span className="truncate">
              {formatForDisplay(category)}
            </span>
            {categoryCounts[category] > 0 && (
              <span className={getCountBadgeStyles(selectedCategory === category, isDarkMode)}>
                {categoryCounts[category]}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Scroll Right Button */}
      {showScrollButtons && (
        <button
          onClick={() => scroll('right')}
          className={`absolute right-1 top-1/2 -translate-y-1/2 z-20 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-200 ease-out hover:scale-110 active:scale-95 ${getScrollButtonStyles('right')}`}
          aria-label="Scroll right"
        >
          ▶
        </button>
      )}
      </div>
    </div>
  );
};

export default SimpleCategorySelector;
