import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LocaleProvider } from './contexts/LocaleContext';
import { Settings } from './settings/settings';
import CategoryTagToggleDemo from './components/CategoryTagToggleDemo';
import PrivacyPolicy from './components/PrivacyPolicy';
import TermsOfService from './components/TermsOfService';
import './App.css';

const App: React.FC = () => {
  return (
    <LocaleProvider>
      <Router>
        <div className="app">
          <main>
            <Routes>
              <Route path="/settings" element={<Settings />} />
              <Route path="/demo/category-tag-toggle" element={<CategoryTagToggleDemo />} />
              <Route path="/privacy" element={<PrivacyPolicy />} />
              <Route path="/terms" element={<TermsOfService />} />
              {/* Add other routes here */}
            </Routes>
          </main>
        </div>
      </Router>
    </LocaleProvider>
  );
};

export default App;
