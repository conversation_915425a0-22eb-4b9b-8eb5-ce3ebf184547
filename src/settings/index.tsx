import React from 'react';
import ReactDOM from 'react-dom/client';
import { Settings } from './settings';
import { LocaleProvider } from '../contexts/LocaleProvider';
import { AuthProvider } from '../context/AuthContext';
import '../index.css';
import { initializeTheme } from '../utils/themeUtils';

// Theme initialization is handled by components to avoid conflicts

const root = document.getElementById('root');
if (root) {
  ReactDOM.createRoot(root).render(
    <React.StrictMode>
      <AuthProvider>
        <LocaleProvider>
          <Settings />
        </LocaleProvider>
      </AuthProvider>
    </React.StrictMode>
  );
}
