import { v4 as uuidv4 } from 'uuid';
import type {
  WisdomQuote,
  UserWisdomPreferences,
  WisdomInsight,
  WisdomStorage,
  WisdomBatch
} from '../types/wisdom';
import { extractWisdomFromPosts } from './wisdomExtractionService';
import { generateAIWisdom } from './wisdomGenerationService';
import { getSavedPosts } from '../storage';

const WISDOM_STORAGE_KEY = 'wisdomStorage';

// Default storage structure
const DEFAULT_WISDOM_STORAGE: WisdomStorage = {
  quotes: [],
  preferences: {
    lastShownDate: undefined,
    preferredCategories: [],
    preferredAuthors: <AUTHORS>
  },
  insights: {
    topCategories: [],
    commonTags: [],
    lastAnalyzedAt: new Date(0).toISOString()
  },
  currentBatch: undefined,
  batchHistory: []
};

export const getWisdomStorage = async (): Promise<WisdomStorage> => {
  try {
    const result = await chrome.storage.local.get(WISDOM_STORAGE_KEY);

    if (!result[WISDOM_STORAGE_KEY]) {
      return { ...DEFAULT_WISDOM_STORAGE };
    }

    return result[WISDOM_STORAGE_KEY];
  } catch (error) {
    console.error('wisdomService: Error getting storage:', error);
    // Return default storage on error
    return { ...DEFAULT_WISDOM_STORAGE };
  }
};

export const saveWisdomStorage = async (storage: Partial<WisdomStorage>): Promise<void> => {
  try {
    const currentStorage = await getWisdomStorage();

    const updatedStorage = {
      ...currentStorage,
      ...storage,
      // Ensure we don't lose any existing data when updating partial storage
      preferences: {
        ...currentStorage.preferences,
        ...(storage.preferences || {})
      },
      insights: {
        ...currentStorage.insights,
        ...(storage.insights || {})
      }
    };

    // Check if we're in a Chrome extension context
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
      await chrome.storage.local.set({
        [WISDOM_STORAGE_KEY]: updatedStorage
      });
    } else {
      // Fallback to localStorage for web context
      localStorage.setItem(WISDOM_STORAGE_KEY, JSON.stringify(updatedStorage));
    }

  } catch (error) {
    console.error('wisdomService: Error saving to storage:', error);
    throw error;
  }
};

// Quote Operations
export const addWisdomQuote = async (quote: Omit<WisdomQuote, 'id' | 'createdAt'>): Promise<WisdomQuote> => {
  const newQuote: WisdomQuote = {
    ...quote,
    id: uuidv4(),
    createdAt: new Date().toISOString(),
  };

  const { quotes } = await getWisdomStorage();
  const updatedQuotes = [...quotes, newQuote];
  
  await saveWisdomStorage({
    quotes: updatedQuotes
  });
  
  // Verify the quote was saved
  const storageAfterSave = await getWisdomStorage();
  const savedQuote = storageAfterSave.quotes.find(q => q.id === newQuote.id);

  if (!savedQuote) {
    console.error('wisdomService: Failed to verify quote was saved');
  }
  
  return newQuote;
};

export const getWisdomQuote = async (id: string): Promise<WisdomQuote | undefined> => {
  const { quotes } = await getWisdomStorage();
  return quotes.find(quote => quote.id === id);
};

export const updateWisdomQuote = async (id: string, updates: Partial<Omit<WisdomQuote, 'id' | 'createdAt'>>): Promise<WisdomQuote | null> => {
  const { quotes } = await getWisdomStorage();
  const quoteIndex = quotes.findIndex(q => q.id === id);
  
  if (quoteIndex === -1) return null;
  
  const updatedQuote = { 
    ...quotes[quoteIndex], 
    ...updates,
    // Don't allow updating these fields through this method
    id: quotes[quoteIndex].id,
    createdAt: quotes[quoteIndex].createdAt
  };
  
  const updatedQuotes = [...quotes];
  updatedQuotes[quoteIndex] = updatedQuote;
  
  await saveWisdomStorage({ quotes: updatedQuotes });
  return updatedQuote;
};

export const deleteWisdomQuote = async (id: string): Promise<boolean> => {
  const { quotes } = await getWisdomStorage();
  const newQuotes = quotes.filter(quote => quote.id !== id);
  
  if (newQuotes.length === quotes.length) return false;
  
  await saveWisdomStorage({ quotes: newQuotes });
  return true;
};

export const getAllWisdomQuotes = async (): Promise<WisdomQuote[]> => {
  const { quotes } = await getWisdomStorage();
  return [...quotes].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

// Preferences Operations
export const getUserWisdomPreferences = async (): Promise<UserWisdomPreferences> => {
  const { preferences } = await getWisdomStorage();
  return { ...preferences };
};

export const updateUserWisdomPreferences = async (
  updates: Partial<UserWisdomPreferences>
): Promise<UserWisdomPreferences> => {
  const currentStorage = await getWisdomStorage();
  const updatedPreferences = {
    ...currentStorage.preferences,
    ...updates
  };
  
  await saveWisdomStorage({ preferences: updatedPreferences });
  return updatedPreferences;
};

// Insights Operations
export const getWisdomInsights = async (): Promise<WisdomInsight> => {
  const { insights } = await getWisdomStorage();
  return { ...insights };
};

export const updateWisdomInsights = async (
  updates: Partial<WisdomInsight>
): Promise<WisdomInsight> => {
  const currentStorage = await getWisdomStorage();
  const updatedInsights = {
    ...currentStorage.insights,
    ...updates,
    lastAnalyzedAt: new Date().toISOString()
  };
  
  await saveWisdomStorage({ insights: updatedInsights });
  return updatedInsights;
};

// Daily Wisdom Feature with 5-day rotation
export const getTodaysWisdom = async (): Promise<WisdomQuote | null> => {
  try {
    console.log('wisdomService: getTodaysWisdom called');

    // Ensure storage is initialized
    await ensureStorageInitialized();
    console.log('wisdomService: Storage initialized');

    const today = new Date().toISOString().split('T')[0];
    console.log('wisdomService: Today is', today);

    const storage = await getWisdomStorage();
    console.log('wisdomService: Retrieved storage:', storage);

    const { currentBatch } = storage;

    // Check if we need a new batch or if current batch is still valid
    const needsNewBatch = await checkIfNeedsNewBatch(currentBatch, today);
    console.log('wisdomService: Needs new batch?', needsNewBatch);

    if (needsNewBatch) {
      console.log('wisdomService: Creating new batch...');
      const newBatch = await createNewWisdomBatch();

      if (!newBatch) {
        console.log('wisdomService: Failed to create new batch');
        return null;
      }

      console.log('wisdomService: Created new batch with', newBatch.quotes.length, 'quotes');

      // Save the new batch
      await saveWisdomStorage({ currentBatch: newBatch });

      // Return today's quote from the new batch
      const todaysQuote = getCurrentDayQuote(newBatch, today);
      console.log('wisdomService: Returning today\'s quote from new batch:', todaysQuote);
      return todaysQuote;
    }

    // Use existing batch
    if (currentBatch) {
      const todaysQuote = getCurrentDayQuote(currentBatch, today);
      console.log('wisdomService: Returning today\'s quote from existing batch:', todaysQuote);
      return todaysQuote;
    }

    console.log('wisdomService: No batch available, returning null');
    return null;

  } catch (error) {
    console.error('wisdomService: Error in getTodaysWisdom:', error);
    return null;
  }
};

// Initialize storage if not exists
export const initializeStorage = async (): Promise<void> => {
  try {
    const currentStorage = await getWisdomStorage();

    // If no quotes exist, initialize with some default quotes
    if (currentStorage.quotes.length === 0) {
      const defaultQuotes: Omit<WisdomQuote, 'id' | 'createdAt'>[] = [
        {
          text: "The only way to do great work is to love what you do.",
          author: "Steve Jobs",
          categories: ["Motivation", "Work"],
          tags: ["inspiration", "career"],
          source: "Stanford Commencement Speech 2005"
        },
        {
          text: "Innovation distinguishes between a leader and a follower.",
          author: "Steve Jobs",
          categories: ["Leadership", "Innovation"],
          tags: ["business", "strategy"]
        },
        {
          text: "Your time is limited, don't waste it living someone else's life.",
          author: "Steve Jobs",
          categories: ["Life", "Motivation"],
          tags: ["wisdom", "self-improvement"]
        }
      ];

      // Add default quotes
      for (const quote of defaultQuotes) {
        await addWisdomQuote(quote);
      }
    }
  } catch (error) {
    console.error('wisdomService: Failed to initialize wisdom storage:', error);
    throw error;
  }
};

// Function to ensure storage is initialized
export const ensureStorageInitialized = async (): Promise<void> => {
  try {
    await initializeStorage();
  } catch (error) {
    console.error('Storage initialization failed:', error);
  }
};

// Initialize on import
ensureStorageInitialized().catch(console.error);

/**
 * Checks if we need to create a new 5-day wisdom batch
 */
async function checkIfNeedsNewBatch(currentBatch: WisdomBatch | undefined, today: string): Promise<boolean> {
  if (!currentBatch) {
    console.log('wisdomService: No current batch exists, need new batch');
    return true;
  }

  const batchStartDate = new Date(currentBatch.startDate);
  const todayDate = new Date(today);
  const daysDiff = Math.floor((todayDate.getTime() - batchStartDate.getTime()) / (1000 * 60 * 60 * 24));

  // If more than 5 days have passed, we need a new batch
  if (daysDiff >= 5) {
    console.log(`wisdomService: Batch expired (${daysDiff} days old), need new batch`);
    return true;
  }

  // If batch doesn't have 5 quotes, it's incomplete
  if (!currentBatch.quotes || currentBatch.quotes.length < 5) {
    console.log('wisdomService: Current batch incomplete, need new batch');
    return true;
  }

  return false;
}

/**
 * Creates a new 5-day wisdom batch by extracting from posts and generating AI content
 */
async function createNewWisdomBatch(forceRefresh: boolean = false): Promise<WisdomBatch | null> {
  try {
    console.log('wisdomService: Starting new batch creation...', forceRefresh ? '(forced refresh)' : '');

    // Step 1: Extract wisdom from user's saved posts
    // If forcing refresh, try to get more variety by requesting more quotes initially
    const requestCount = forceRefresh ? 10 : 5;
    const extractedWisdom = await extractWisdomFromPosts(requestCount);
    const validExtractedWisdom = extractedWisdom ?? [];
    console.log(`wisdomService: Extracted ${validExtractedWisdom.length} wisdom quotes from posts`);

    if (validExtractedWisdom.length === 0) {
      console.log('wisdomService: No wisdom extracted from posts, falling back to other sources');
    }

    // Step 2: If we need more quotes, generate with AI
    const neededCount = 5 - Math.min(validExtractedWisdom.length, 5);
    let aiWisdom: WisdomQuote[] = [];

    if (neededCount > 0) {
      console.log(`wisdomService: Need ${neededCount} more quotes, generating with AI...`);
      const savedPosts = await getSavedPosts();
      aiWisdom = await generateAIWisdom(neededCount, savedPosts, validExtractedWisdom);
      console.log(`wisdomService: Generated ${aiWisdom.length} AI wisdom quotes`);
    }

    // Step 3: Combine and create batch
    // If we have more than 5 extracted quotes, shuffle and take 5 for variety
    let selectedExtracted = validExtractedWisdom;
    if (validExtractedWisdom.length > 5) {
      selectedExtracted = [...validExtractedWisdom].sort(() => Math.random() - 0.5).slice(0, 5);
      console.log(`wisdomService: Shuffled and selected ${selectedExtracted.length} from ${validExtractedWisdom.length} extracted quotes`);
    }

    const allWisdom = [...selectedExtracted, ...aiWisdom];

    if (allWisdom.length === 0) {
      console.log('wisdomService: No wisdom quotes available for batch');
      return null;
    }

    // Ensure we have exactly 5 quotes (pad with fallback if needed)
    const fallbackWisdoms = [
      "What we resist in others often reflects what we haven't yet accepted in ourselves.",
      "The space between stimulus and response is where our freedom lies.",
      "Every master was once a beginner who refused to give up.",
      "The quality of our questions determines the quality of our lives.",
      "We cannot direct the wind, but we can adjust our sails."
    ];

    let fallbackIndex = 0;
    while (allWisdom.length < 5) {
      const fallbackQuote: WisdomQuote = {
        id: uuidv4(),
        text: fallbackWisdoms[fallbackIndex % fallbackWisdoms.length],
        author: "Generated",
        categories: ["Growth"],
        tags: ["Wisdom", "Growth", "Life"],
        createdAt: new Date().toISOString(),
        extractedFrom: 'ai-generated'
      };
      allWisdom.push(fallbackQuote);
      fallbackIndex++;
    }

    // Take only first 5 quotes
    const batchQuotes = allWisdom.slice(0, 5);

    // Assign day indices
    batchQuotes.forEach((quote, index) => {
      quote.dayIndex = index;
    });

    const batch: WisdomBatch = {
      id: uuidv4(),
      quotes: batchQuotes,
      createdAt: new Date().toISOString(),
      startDate: new Date().toISOString().split('T')[0],
      currentDayIndex: 0,
      isActive: true
    };

    console.log('wisdomService: Created new wisdom batch with', batch.quotes.length, 'quotes');
    return batch;

  } catch (error) {
    console.error('wisdomService: Error creating new wisdom batch:', error);
    return null;
  }
}

/**
 * Gets the current day's quote from a batch
 */
function getCurrentDayQuote(batch: WisdomBatch, today: string): WisdomQuote | null {
  if (!batch.quotes || batch.quotes.length === 0) {
    return null;
  }

  const batchStartDate = new Date(batch.startDate);
  const todayDate = new Date(today);
  const dayIndex = Math.floor((todayDate.getTime() - batchStartDate.getTime()) / (1000 * 60 * 60 * 24));

  // Ensure day index is within bounds
  const validDayIndex = Math.max(0, Math.min(dayIndex, batch.quotes.length - 1));

  const todaysQuote = batch.quotes[validDayIndex];

  if (todaysQuote) {
    // Update the quote's shownAt timestamp
    todaysQuote.shownAt = new Date().toISOString();
  }

  return todaysQuote;
}

/**
 * Manually refreshes the wisdom batch (creates a new 5-day cycle)
 */
export const refreshWisdomBatch = async (): Promise<WisdomBatch | null> => {
  try {
    console.log('wisdomService: Manually refreshing wisdom batch...');

    const storage = await getWisdomStorage();

    // Move current batch to history if it exists
    if (storage.currentBatch) {
      const updatedHistory = [...(storage.batchHistory || []), storage.currentBatch].slice(-10); // Keep last 10 batches
      await saveWisdomStorage({ batchHistory: updatedHistory });
    }

    // Create new batch
    const newBatch = await createNewWisdomBatch();

    if (newBatch) {
      await saveWisdomStorage({ currentBatch: newBatch });
      console.log('wisdomService: Successfully refreshed wisdom batch');
    }

    return newBatch;
  } catch (error) {
    console.error('wisdomService: Error refreshing wisdom batch:', error);
    return null;
  }
};

/**
 * Forces creation of a new wisdom quote by creating a fresh batch
 * This is used when user clicks refresh and wants immediate variety
 */
export const forceNewWisdomQuote = async (): Promise<WisdomQuote | null> => {
  try {
    console.log('wisdomService: Forcing new wisdom quote...');

    // Create a completely new batch with fresh randomization
    const newBatch = await createNewWisdomBatch(true); // Pass true to force fresh extraction

    if (!newBatch || newBatch.quotes.length === 0) {
      console.log('wisdomService: Failed to create new batch for forced quote');
      return null;
    }

    // Save the new batch
    await saveWisdomStorage({ currentBatch: newBatch });

    // Return the first quote from the new batch
    const newQuote = newBatch.quotes[0];
    console.log('wisdomService: Returning forced new quote:', newQuote);

    return newQuote;
  } catch (error) {
    console.error('wisdomService: Error forcing new wisdom quote:', error);
    return null;
  }
};

/**
 * Gets the current wisdom batch information
 */
export const getCurrentWisdomBatch = async (): Promise<WisdomBatch | null> => {
  try {
    const storage = await getWisdomStorage();
    return storage.currentBatch || null;
  } catch (error) {
    console.error('wisdomService: Error getting current wisdom batch:', error);
    return null;
  }
};

/**
 * Clears all wisdom storage to force regeneration with new logic
 * This is useful when the wisdom extraction logic has been updated
 */
export const clearWisdomStorage = async (): Promise<void> => {
  try {
    // Check if we're in a Chrome extension context
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
      await chrome.storage.local.remove(WISDOM_STORAGE_KEY);
    } else {
      // Fallback to localStorage for web context
      localStorage.removeItem(WISDOM_STORAGE_KEY);
    }
    console.log('wisdomService: Cleared wisdom storage for regeneration');
  } catch (error) {
    console.error('wisdomService: Error clearing wisdom storage:', error);
    throw error;
  }
};

// Function to add a sample quote (for testing)
export const addSampleQuote = async (): Promise<WisdomQuote> => {
  const sampleQuote: Omit<WisdomQuote, 'id' | 'createdAt'> = {
    text: "This is a sample quote to test the Daily Wisdom feature.",
    author: "System",
    categories: ["Test", "Sample"],
    tags: ["test", "sample"],
    source: "System Generated"
  };
  return addWisdomQuote(sampleQuote);
};
