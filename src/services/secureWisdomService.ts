import { getToken } from './authService';

const API_BASE_URL = 'https://api.notely.social';

export interface SecureWisdomResponse {
  success: boolean;
  wisdom: string | null;
  error?: string;
}

export async function transformContentSecurely(content: string): Promise<SecureWisdomResponse> {
  try {
    const token = await getToken();
    if (!token) {
      return {
        success: false,
        wisdom: null,
        error: 'Authentication required for wisdom transformation',
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/wisdom/transform`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ content }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        wisdom: null,
        error: data.error || `Server error: ${response.status}`,
      };
    }

    return {
      success: true,
      wisdom: data.wisdom || null,
    };
  } catch (error) {
    console.error('Error transforming wisdom securely:', error);
    return {
      success: false,
      wisdom: null,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export interface GeneratedWisdom {
  quote: string;
  author: string;
  tags: string[];
  category: string;
}

export async function generateSecureWisdomQuotes(
  count: number,
  context: string,
  existing: string[] = []
): Promise<{ success: boolean; quotes: GeneratedWisdom[]; error?: string }> {
  try {
    const token = await getToken();
    if (!token) {
      return { success: false, quotes: [], error: 'Authentication required for wisdom generation' };
    }

    const response = await fetch(`${API_BASE_URL}/api/wisdom/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ count, context, existing }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        quotes: [],
        error: data.error || `Server error: ${response.status}`,
      };
    }

    return {
      success: true,
      quotes: data.quotes || [],
    };
  } catch (error) {
    console.error('Error generating secure wisdom quotes:', error);
    return {
      success: false,
      quotes: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export default {
  transformContentSecurely,
  generateSecureWisdomQuotes,
};

