/**
 * AI Prompt Service - Generates multilingual prompts for AI services
 * This service creates dynamic prompts based on the user's selected language
 */

export interface PromptTemplates {
  categorization: string;
  tagging: string;
  insight: string;
  fastTake: string;
  snapNote: string;
  contentIdeas: string;
  bookmarkExtraction: string;
  dailyPrompt: string;
  contentFormat: string;
}

// Multilingual prompt templates
const promptTemplates: Record<string, PromptTemplates> = {
  en: {
    categorization: `You are an expert social media content analyst. Your task is to categorize the following social media post into the most relevant category.

--- POST CONTENT START ---
{postContent}
--- POST CONTENT END ---

Available categories: {categories}

Rules:
- Select the single most relevant category that best captures the main essence of the post
- Category must be from the provided list
- Consider the main topic, industry, and content type
- Choose only the best-fitting category

Respond ONLY with a JSON object with a single key "categories" containing an array with exactly one category string.
Example: {"categories": ["technology"]}

JSON Output:`,

    tagging: `You are an expert social media content analyst. Your task is to generate relevant tags for a given social media post.

--- POST CONTENT START ---
{postContent}
--- POST CONTENT END ---

Generate {maxTags} relevant, specific, and searchable tags for this content.

Rules:
- Tags should be descriptive and specific
- Use lowercase with underscores for multi-word tags
- Focus on key topics, themes, and concepts
- Avoid generic tags like "social_media" or "post"
- Consider industry-specific terminology

Output your suggestions as a JSON array of strings.
Example: ["artificial_intelligence", "innovation", "future_tech", "conference_highlights"]

JSON Output:`,

    insight: `Analyze the following social media post content and provide insights:
--- POST CONTENT START ---
{postContent}
--- POST CONTENT END ---

Provide a comprehensive analysis including:
- Sentiment (positive/negative/neutral)
- A single, appropriate emoji reflecting the overall sentiment or main theme
- Key themes and topics
- Emotional tone
- Engagement potential
- Target audience insights

Respond ONLY with a JSON object containing: sentiment, emoji, themes, tone, engagement_score (1-10), and audience_insights.
Example: {"sentiment": "positive", "emoji": "💡", "themes": ["innovation", "technology"], "tone": "enthusiastic", "engagement_score": 8, "audience_insights": "Tech enthusiasts and professionals"}

JSON Output:`,

    fastTake: `Analyze the following social media post content and provide a "Fast Take":
--- POST CONTENT START ---
{postContent}
--- POST CONTENT END ---

A "Fast Take" is a concise, insightful summary that captures the essence and key takeaway of the content in 1-2 sentences. It should be actionable and memorable.

Respond ONLY with a JSON object with a single key "fastTake" containing the string.
Example: {"fastTake": "Revolutionary AI breakthrough could transform weather prediction accuracy by 99% using advanced satellite data analysis."}

JSON Output:`,

    snapNote: `Analyze the following social media post content and create a SnapNote:
--- POST CONTENT START ---
{postContent}
--- POST CONTENT END ---

A "SnapNote" is a brief, engaging, and contextually relevant micro-caption or summary (max 150 characters). It should capture the essence of the content in a way that's useful for quick reference and organization.

{imageContext}

Respond ONLY with a JSON object with a single key "snapNote" containing the string.
Example: {"snapNote": "AI breakthrough enables 99% accurate weather prediction using satellite data."}

JSON Output:`,

    contentIdeas: `Analyze the following social media post content:
--- POST CONTENT START ---
{postContent}
--- POST CONTENT END ---

Based on this content, generate 2-3 distinct and actionable content ideas for future posts. These ideas could be follow-up questions, related topics, different angles, or calls to action.
The ideas should be creative and engaging.
{existingIdeasContext}

Respond ONLY with a JSON object with a single key "contentIdeas" containing an array of strings.
Example: {"contentIdeas": ["Ask your audience about their experiences with X.", "Create a poll comparing Y and Z.", "Share a deep-dive into aspect A of the topic."]}

JSON Output:`,

    bookmarkExtraction: `You are an expert at identifying websites, tools, platforms, and services mentioned in social media posts. Your task is to extract real, useful bookmarks from the following post content.

--- POST CONTENT START ---
{postContent}
--- POST CONTENT END ---

Extract websites, tools, platforms, and services that are mentioned or referenced in this post. Look for:
- Direct website mentions (like "bolt.new", "github.com", "openai.com")
- Tool names (like "ChatGPT", "Figma", "Notion", "Slack")
- Platform names (like "YouTube", "TikTok", "LinkedIn")
- Service names (like "AWS", "Stripe", "Shopify")
- Company websites that can be inferred from context
- Educational resources or documentation sites

Rules:
- Only extract real, legitimate websites and tools
- Convert tool/service names to their actual website URLs
- Skip URL shorteners (bit.ly, t.co, lnkd.in, etc.)
- Skip social media post URLs
- Focus on useful resources the audience would want to bookmark
- Include the main website even if a specific page is mentioned

Respond ONLY with a JSON array of objects containing "name", "url", "description", and "category" fields.
Categories should be one of: "Tools", "Platforms", "News", "Education", "Business", "Entertainment", "Technology"

Example: [{"name": "Bolt.new", "url": "https://bolt.new", "description": "AI-powered web development platform", "category": "Tools"}]

JSON Output:`,

    dailyPrompt: `You are a creative content strategist and writing coach. Generate an inspiring daily writing or content creation prompt that will spark creativity and help content creators produce engaging material.

The prompt should be:
- Inspiring and thought-provoking
- Actionable and specific enough to start writing immediately
- Relevant to current trends and interests
- Suitable for various content formats (social media, blog, video)
- Fresh and unique (avoid generic prompts)

Consider these themes: personal growth, creativity, technology, relationships, productivity, mindfulness, innovation, storytelling, community, or current cultural moments.

Generate a single, compelling prompt that a content creator would be excited to explore today.

Respond with just the prompt text, no additional formatting or explanation.`,

    contentFormat: `Based on the following writing prompt, suggest the most effective content format and provide a brief creative direction.

PROMPT: {prompt}

Analyze this prompt and suggest ONE of these formats:
- Tweet/X Post: For quick, punchy insights or questions
- Blog Intro: For longer-form, detailed exploration
- Video Hook: For engaging, visual storytelling

Provide your response as a JSON object with:
- "format": the suggested format type
- "direction": a brief creative direction (1-2 sentences)
- "hook": a compelling opening line or angle

Example: {"format": "Tweet/X Post", "direction": "Frame as a thought-provoking question that encourages replies and discussion.", "hook": "What if the thing you're avoiding is exactly what you need to do today?"}

JSON Output:`
  },

  tr: {
    categorization: `Sosyal medya içerik analisti uzmanısınız. Göreviniz aşağıdaki sosyal medya gönderisini en uygun kategoriye ayırmaktır.

--- GÖNDERİ İÇERİĞİ BAŞLANGIÇ ---
{postContent}
--- GÖNDERİ İÇERİĞİ SON ---

Mevcut kategoriler: {categories}

Kurallar:
- Gönderinin ana özünü en iyi yakalayan tek en ilgili kategoriyi seçin
- Kategori sağlanan listeden olmalıdır
- Ana konu, sektör ve içerik türünü göz önünde bulundurun
- Sadece en uygun kategoriyi seçin

SADECE "categories" anahtarı ile tam olarak bir kategori içeren dizi ile JSON nesnesi ile yanıtlayın.
Örnek: {"categories": ["teknoloji"]}

JSON Çıktısı:`,

    tagging: `Sosyal medya içerik analisti uzmanısınız. Göreviniz verilen sosyal medya gönderisi için ilgili etiketler oluşturmaktır.

--- GÖNDERİ İÇERİĞİ BAŞLANGIÇ ---
{postContent}
--- GÖNDERİ İÇERİĞİ SON ---

Bu içerik için {maxTags} ilgili, spesifik ve aranabilir etiket oluşturun.

Kurallar:
- Etiketler açıklayıcı ve spesifik olmalıdır
- Çok kelimeli etiketler için alt çizgi ile küçük harf kullanın
- Ana konular, temalar ve kavramlara odaklanın
- "sosyal_medya" veya "gönderi" gibi genel etiketlerden kaçının
- Sektöre özgü terminolojiyi göz önünde bulundurun

Önerilerinizi JSON dizi olarak çıktılayın.
Örnek: ["artificial_intelligence", "innovation", "gelecek_teknolojisi", "konferans_önemli_noktalar"]

JSON Çıktısı:`,

    insight: `Aşağıdaki sosyal medya gönderisi içeriğini analiz edin ve içgörüler sağlayın:
--- GÖNDERİ İÇERİĞİ BAŞLANGIÇ ---
{postContent}
--- GÖNDERİ İÇERİĞİ SON ---

Şunları içeren kapsamlı bir analiz sağlayın:
- Duygu durumu (olumlu/olumsuz/nötr)
- Genel duygu durumunu veya ana temayı yansıtan tek, uygun bir emoji
- Ana temalar ve konular
- Duygusal ton
- Etkileşim potansiyeli
- Hedef kitle içgörileri

SADECE şunları içeren JSON nesnesi ile yanıtlayın: sentiment, emoji, themes, tone, engagement_score (1-10), ve audience_insights.
Örnek: {"sentiment": "olumlu", "emoji": "💡", "themes": ["inovasyon", "teknoloji"], "tone": "coşkulu", "engagement_score": 8, "audience_insights": "Teknoloji meraklıları ve profesyoneller"}

JSON Çıktısı:`,

    fastTake: `Aşağıdaki sosyal medya gönderisi içeriğini analiz edin ve "Hızlı Değerlendirme" sağlayın:
--- GÖNDERİ İÇERİĞİ BAŞLANGIÇ ---
{postContent}
--- GÖNDERİ İÇERİĞİ SON ---

"Hızlı Değerlendirme", içeriğin özünü ve ana çıkarımını 1-2 cümlede yakalayan kısa, anlayışlı bir özettir. Uygulanabilir ve akılda kalıcı olmalıdır.

Respond ONLY with a JSON object with a single key "fastTake" containing the string.
Örnek: {"fastTake": "Devrimci AI atılımı, gelişmiş uydu veri analizi kullanarak hava durumu tahmin doğruluğunu %99 oranında dönüştürebilir."}

JSON Çıktısı:`,

    snapNote: `Aşağıdaki sosyal medya gönderisi içeriğini analiz edin ve SnapNote oluşturun:
--- GÖNDERİ İÇERİĞİ BAŞLANGIÇ ---
{postContent}
--- GÖNDERİ İÇERİĞİ SON ---

"SnapNote", hızlı referans ve organizasyon için yararlı olan, içeriğin özünü yakalayan kısa, ilgi çekici ve bağlamsal olarak ilgili bir mikro başlık veya özettir (maksimum 150 karakter).

{imageContext}

Respond ONLY with a JSON object with a single key "snapNote" containing the string.
Örnek: {"snapNote": "AI atılımı uydu verisi kullanarak %99 doğru hava tahmini sağlıyor."}

JSON Çıktısı:`,

    contentIdeas: `Aşağıdaki sosyal medya gönderisi içeriğini analiz edin:
--- GÖNDERİ İÇERİĞİ BAŞLANGIÇ ---
{postContent}
--- GÖNDERİ İÇERİĞİ SON ---

Bu içeriğe dayanarak, gelecekteki gönderiler için 2-3 farklı ve uygulanabilir içerik fikri oluşturun. Bu fikirler takip soruları, ilgili konular, farklı açılar veya eylem çağrıları olabilir.
Fikirler yaratıcı ve ilgi çekici olmalıdır.
{existingIdeasContext}

Respond ONLY with a JSON object with a single key "contentIdeas" containing an array of strings.
Örnek: {"contentIdeas": ["Kitlenize X ile ilgili deneyimleri hakkında sorun.", "Y ve Z'yi karşılaştıran bir anket oluşturun.", "Konunun A yönü hakkında derinlemesine bilgi paylaşın."]}

JSON Çıktısı:`,

    bookmarkExtraction: `Sosyal medya gönderilerinde bahsedilen web siteleri, araçlar, platformlar ve hizmetleri belirleme konusunda uzmansınız. Göreviniz aşağıdaki gönderi içeriğinden gerçek, yararlı yer imleri çıkarmaktır.

--- GÖNDERİ İÇERİĞİ BAŞLANGIÇ ---
{postContent}
--- GÖNDERİ İÇERİĞİ SON ---

Bu gönderide bahsedilen veya referans verilen web siteleri, araçlar, platformlar ve hizmetleri çıkarın. Şunları arayın:
- Doğrudan web sitesi bahisleri ("bolt.new", "github.com", "openai.com" gibi)
- Araç isimleri ("ChatGPT", "Figma", "Notion", "Slack" gibi)
- Platform isimleri ("YouTube", "TikTok", "LinkedIn" gibi)
- Hizmet isimleri ("AWS", "Stripe", "Shopify" gibi)
- Bağlamdan çıkarılabilecek şirket web siteleri
- Eğitim kaynakları veya dokümantasyon siteleri

Rules:
- Sadece gerçek, meşru web siteleri ve araçları çıkarın
- Araç/hizmet isimlerini gerçek web sitesi URL'lerine dönüştürün
- URL kısaltıcıları atlayın (bit.ly, t.co, lnkd.in, vb.)
- Sosyal medya gönderi URL'lerini atlayın
- Kitlenin yer imi yapmak isteyeceği yararlı kaynaklara odaklanın
- Belirli bir sayfa bahsedilse bile ana web sitesini dahil edin

Respond ONLY with a JSON array of objects containing "name", "url", "description", and "category" fields.
Categories should be one of: "Tools", "Platforms", "News", "Education", "Business", "Entertainment", "Technology"

Example: [{"name": "Bolt.new", "url": "https://bolt.new", "description": "AI destekli web geliştirme platformu", "category": "Tools"}]

JSON Output:`,

    dailyPrompt: `Yaratıcı bir içerik stratejisti ve yazma koçusunuz. İçerik yaratıcılarının yaratıcılığını tetikleyecek ve ilgi çekici materyaller üretmelerine yardımcı olacak ilham verici bir günlük yazma veya içerik oluşturma istemi oluşturun.

İstem şu özelliklere sahip olmalıdır:
- İlham verici ve düşündürücü
- Hemen yazmaya başlamak için yeterince uygulanabilir ve spesifik
- Güncel trendler ve ilgi alanlarıyla ilgili
- Çeşitli içerik formatları için uygun (sosyal medya, blog, video)
- Taze ve benzersiz (genel istemlerden kaçının)

Şu temaları göz önünde bulundurun: kişisel gelişim, yaratıcılık, teknoloji, ilişkiler, verimlilik, farkındalık, inovasyon, hikaye anlatımı, topluluk veya güncel kültürel anlar.

Bir içerik yaratıcısının bugün keşfetmekten heyecan duyacağı tek, etkileyici bir istem oluşturun.

Sadece istem metniyle yanıtlayın, ek biçimlendirme veya açıklama olmadan.`,

    contentFormat: `Aşağıdaki yazma istemine dayanarak, en etkili içerik formatını önerin ve kısa bir yaratıcı yön sağlayın.

İSTEM: {prompt}

Analyze this prompt and suggest ONE of these formats:
- Tweet/X Post: For quick, punchy insights or questions
- Blog Intro: For longer-form, detailed exploration
- Video Hook: For engaging, visual storytelling

Provide your response as a JSON object with:
- "format": the suggested format type
- "direction": a brief creative direction (1-2 sentences)
- "hook": a compelling opening line or angle

Example: {"format": "Tweet/X Post", "direction": "Frame as a thought-provoking question that encourages replies and discussion.", "hook": "What if the thing you're avoiding is exactly what you need to do today?"}

JSON Output:`
  },

  fr: {
    categorization: `Vous êtes un expert analyste de contenu de médias sociaux. Votre tâche est de catégoriser la publication de médias sociaux suivante dans la catégorie la plus pertinente.

--- DÉBUT DU CONTENU DE LA PUBLICATION ---
{postContent}
--- FIN DU CONTENU DE LA PUBLICATION ---

Catégories disponibles : {categories}

Règles :
- Sélectionnez la seule catégorie la plus pertinente qui capture le mieux l'essence principale de la publication
- La catégorie doit provenir de la liste fournie
- Considérez le sujet principal, l'industrie et le type de contenu
- Choisissez uniquement la catégorie la mieux adaptée

Répondez UNIQUEMENT avec un objet JSON avec une seule clé "categories" contenant un tableau avec exactement une chaîne de catégorie.
Exemple : {"categories": ["technologie"]}

Sortie JSON :`,

    tagging: `Vous êtes un expert analyste de contenu de médias sociaux. Votre tâche est de générer des étiquettes pertinentes pour une publication de médias sociaux donnée.

--- DÉBUT DU CONTENU DE LA PUBLICATION ---
{postContent}
--- FIN DU CONTENU DE LA PUBLICATION ---

Générez {maxTags} étiquettes pertinentes, spécifiques et recherchables pour ce contenu.

Règles :
- Les étiquettes doivent être descriptives et spécifiques
- Utilisez des minuscules avec des traits de soulignement pour les étiquettes multi-mots
- Concentrez-vous sur les sujets, thèmes et concepts clés
- Évitez les étiquettes génériques comme "medias_sociaux" ou "publication"
- Considérez la terminologie spécifique à l'industrie

Sortez vos suggestions sous forme de tableau JSON de chaînes.
Exemple : ["intelligence_artificielle", "innovation", "technologie_future", "points_saillants_conference"]

Sortie JSON :`,

    insight: `Analysez le contenu de publication de médias sociaux suivant et fournissez des aperçus :
--- DÉBUT DU CONTENU DE LA PUBLICATION ---
{postContent}
--- FIN DU CONTENU DE LA PUBLICATION ---

Fournissez une analyse complète incluant :
- Sentiment (positif/négatif/neutre)
- Thèmes et sujets clés
- Ton émotionnel
- Potentiel d'engagement
- Aperçus de l'audience cible

Répondez UNIQUEMENT avec un objet JSON contenant : sentiment, emoji, themes, tone, engagement_score (1-10), et audience_insights.
Exemple : {"sentiment": "positif", "emoji": "💡", "themes": ["innovation", "technologie"], "tone": "enthousiaste", "engagement_score": 8, "audience_insights": "Passionnés de technologie et professionnels"}

Sortie JSON :`,

    fastTake: `Analysez le contenu de publication de médias sociaux suivant et fournissez un "Aperçu Rapide" :
--- DÉBUT DU CONTENU DE LA PUBLICATION ---
{postContent}
--- FIN DU CONTENU DE LA PUBLICATION ---

Un "Aperçu Rapide" est un résumé concis et perspicace qui capture l'essence et le point clé du contenu en 1-2 phrases. Il doit être actionnable et mémorable.

Répondez UNIQUEMENT avec un objet JSON avec une seule clé "fastTake" contenant la chaîne.
Exemple : {"fastTake": "Une percée révolutionnaire en IA pourrait transformer la précision de prédiction météorologique de 99% en utilisant l'analyse avancée de données satellitaires."}

Sortie JSON :`,

    snapNote: `Analysez le contenu de publication de médias sociaux suivant et créez une SnapNote :
--- DÉBUT DU CONTENU DE LA PUBLICATION ---
{postContent}
--- FIN DU CONTENU DE LA PUBLICATION ---

Une "SnapNote" est un micro-titre ou résumé bref, engageant et contextuellement pertinent (max 150 caractères). Elle doit capturer l'essence du contenu d'une manière utile pour la référence rapide et l'organisation.

{imageContext}

Répondez UNIQUEMENT avec un objet JSON avec une seule clé "snapNote" contenant la chaîne.
Exemple : {"snapNote": "Percée IA permet prédiction météo 99% précise avec données satellite."}

Sortie JSON :`,

    contentIdeas: `Analysez le contenu de publication de médias sociaux suivant :
--- DÉBUT DU CONTENU DE LA PUBLICATION ---
{postContent}
--- FIN DU CONTENU DE LA PUBLICATION ---

Basé sur ce contenu, générez 2-3 idées de contenu distinctes et actionnables pour de futures publications. Ces idées pourraient être des questions de suivi, des sujets connexes, des angles différents, ou des appels à l'action.
Les idées doivent être créatives et engageantes.
{existingIdeasContext}

Répondez UNIQUEMENT avec un objet JSON avec une seule clé "contentIdeas" contenant un tableau de chaînes.
Exemple : {"contentIdeas": ["Demandez à votre audience leurs expériences avec X.", "Créez un sondage comparant Y et Z.", "Partagez une analyse approfondie de l'aspect A du sujet."]}

Sortie JSON :`,

    bookmarkExtraction: `Vous êtes un expert pour identifier les sites web, outils, plateformes et services mentionnés dans les publications de médias sociaux. Votre tâche est d'extraire des signets réels et utiles du contenu de publication suivant.

--- DÉBUT DU CONTENU DE LA PUBLICATION ---
{postContent}
--- FIN DU CONTENU DE LA PUBLICATION ---

Extrayez les sites web, outils, plateformes et services mentionnés ou référencés dans cette publication. Recherchez :
- Mentions directes de sites web (comme "bolt.new", "github.com", "openai.com")
- Noms d'outils (comme "ChatGPT", "Figma", "Notion", "Slack")
- Noms de plateformes (comme "YouTube", "TikTok", "LinkedIn")
- Noms de services (comme "AWS", "Stripe", "Shopify")
- Sites web d'entreprises qui peuvent être déduits du contexte
- Ressources éducatives ou sites de documentation

Règles :
- Extrayez uniquement des sites web et outils réels et légitimes
- Convertissez les noms d'outils/services en leurs URLs de sites web réels
- Ignorez les raccourcisseurs d'URL (bit.ly, t.co, lnkd.in, etc.)
- Ignorez les URLs de publications de médias sociaux
- Concentrez-vous sur les ressources utiles que l'audience voudrait mettre en signet
- Incluez le site web principal même si une page spécifique est mentionnée

Répondez UNIQUEMENT avec un tableau JSON d'objets contenant les champs "name", "url", "description" et "category".
Les catégories doivent être l'une des suivantes : "Tools", "Platforms", "News", "Education", "Business", "Entertainment", "Technology"

Exemple : [{"name": "Bolt.new", "url": "https://bolt.new", "description": "Plateforme de développement web alimentée par IA", "category": "Tools"}]

Sortie JSON :`
  },

  de: {
    categorization: `Sie sind ein Experte für Social-Media-Inhaltsanalyse. Ihre Aufgabe ist es, den folgenden Social-Media-Beitrag in die relevanteste Kategorie einzuordnen.

--- BEITRAGSINHALT ANFANG ---
{postContent}
--- BEITRAGSINHALT ENDE ---

Verfügbare Kategorien: {categories}

Regeln:
- Wählen Sie die eine relevanteste Kategorie aus, die die Hauptessenz des Beitrags am besten erfasst
- Die Kategorie muss aus der bereitgestellten Liste stammen
- Berücksichtigen Sie das Hauptthema, die Branche und den Inhaltstyp
- Wählen Sie nur die am besten passende Kategorie

Antworten Sie NUR mit einem JSON-Objekt mit einem einzigen Schlüssel "categories", der ein Array mit genau einem Kategorie-String enthält.
Beispiel: {"categories": ["technologie"]}

JSON-Ausgabe:`,

    tagging: `Sie sind ein Experte für Social-Media-Inhaltsanalyse. Ihre Aufgabe ist es, relevante Tags für einen gegebenen Social-Media-Beitrag zu generieren.

--- BEITRAGSINHALT ANFANG ---
{postContent}
--- BEITRAGSINHALT ENDE ---

Generieren Sie {maxTags} relevante, spezifische und suchbare Tags für diesen Inhalt.

Regeln:
- Tags sollten beschreibend und spezifisch sein
- Verwenden Sie Kleinbuchstaben mit Unterstrichen für mehrteilige Tags
- Fokussieren Sie sich auf Schlüsselthemen, Themen und Konzepte
- Vermeiden Sie generische Tags wie "social_media" oder "beitrag"
- Berücksichtigen Sie branchenspezifische Terminologie

Geben Sie Ihre Vorschläge als JSON-Array von Strings aus.
Beispiel: ["kuenstliche_intelligenz", "innovation", "zukunftstechnologie", "konferenz_highlights"]

JSON-Ausgabe:`,

    insight: `Analysieren Sie den folgenden Social-Media-Beitragsinhalt und liefern Sie Einblicke:
--- BEITRAGSINHALT ANFANG ---
{postContent}
--- BEITRAGSINHALT ENDE ---

Liefern Sie eine umfassende Analyse einschließlich:
- Stimmung (positiv/negativ/neutral)
- Genel duygu durumunu veya ana temayı yansıtan tek, uygun bir emoji
- Hauptthemen und Themen
- Emotionaler Ton
- Engagement-Potenzial
- Zielgruppen-Einblicke

Antworten Sie NUR mit einem JSON-Objekt, das enthält: sentiment, emoji, themes, tone, engagement_score (1-10), und audience_insights.
Beispiel: {"sentiment": "positiv", "emoji": "💡", "themes": ["innovation", "technologie"], "tone": "enthusiastisch", "engagement_score": 8, "audience_insights": "Technologie-Enthusiasten und Fachleute"}

JSON-Ausgabe:`,

    fastTake: `Analysieren Sie den folgenden Social-Media-Beitragsinhalt und liefern Sie eine "Schnelle Einschätzung":
--- BEITRAGSINHALT ANFANG ---
{postContent}
--- BEITRAGSINHALT ENDE ---

Eine "Schnelle Einschätzung" ist eine prägnante, aufschlussreiche Zusammenfassung, die die Essenz und den Hauptpunkt des Inhalts in 1-2 Sätzen erfasst. Sie sollte umsetzbar und einprägsam sein.

Antworten Sie NUR mit einem JSON-Objekt mit einem einzigen Schlüssel "fastTake", der den String enthält.
Beispiel: {"fastTake": "Revolutionärer KI-Durchbruch könnte die Wettervorhersage-Genauigkeit um 99% durch fortgeschrittene Satellitendatenanalyse transformieren."}

JSON-Ausgabe:`,

    snapNote: `Analysieren Sie den folgenden Social-Media-Beitragsinhalt und erstellen Sie eine SnapNote:
--- BEITRAGSINHALT ANFANG ---
{postContent}
--- BEITRAGSINHALT ENDE ---

Eine "SnapNote" ist eine kurze, ansprechende und kontextuell relevante Mikro-Überschrift oder Zusammenfassung (max. 150 Zeichen). Sie sollte die Essenz des Inhalts auf eine Weise erfassen, die für schnelle Referenz und Organisation nützlich ist.

{imageContext}

Antworten Sie NUR mit einem JSON-Objekt mit einem einzigen Schlüssel "snapNote", der den String enthält.
Beispiel: {"snapNote": "KI-Durchbruch ermöglicht 99% genaue Wettervorhersage mit Satellitendaten."}

JSON-Ausgabe:`,

    contentIdeas: `Analysieren Sie den folgenden Social-Media-Beitragsinhalt:
--- BEITRAGSINHALT ANFANG ---
{postContent}
--- BEITRAGSINHALT ENDE ---

Basierend auf diesem Inhalt, generieren Sie 2-3 unterschiedliche und umsetzbare Inhaltsideen für zukünftige Beiträge. Diese Ideen könnten Nachfragen, verwandte Themen, verschiedene Blickwinkel oder Handlungsaufforderungen sein.
Die Ideen sollten kreativ und ansprechend sein.
{existingIdeasContext}

Antworten Sie NUR mit einem JSON-Objekt mit einem einzigen Schlüssel "contentIdeas", der ein Array von Strings enthält.
Beispiel: {"contentIdeas": ["Fragen Sie Ihr Publikum nach ihren Erfahrungen mit X.", "Erstellen Sie eine Umfrage, die Y und Z vergleicht.", "Teilen Sie eine Tiefenanalyse von Aspekt A des Themas."]}

JSON-Ausgabe:`,

    bookmarkExtraction: `Sie sind ein Experte darin, Websites, Tools, Plattformen und Dienste zu identifizieren, die in Social-Media-Beiträgen erwähnt werden. Ihre Aufgabe ist es, echte, nützliche Lesezeichen aus dem folgenden Beitragsinhalt zu extrahieren.

--- BEITRAGSINHALT ANFANG ---
{postContent}
--- BEITRAGSINHALT ENDE ---

Extrahieren Sie Websites, Tools, Plattformen und Dienste, die in diesem Beitrag erwähnt oder referenziert werden. Suchen Sie nach:
- Direkten Website-Erwähnungen (wie "bolt.new", "github.com", "openai.com")
- Tool-Namen (wie "ChatGPT", "Figma", "Notion", "Slack")
- Plattform-Namen (wie "YouTube", "TikTok", "LinkedIn")
- Service-Namen (wie "AWS", "Stripe", "Shopify")
- Unternehmens-Websites, die aus dem Kontext abgeleitet werden können
- Bildungsressourcen oder Dokumentations-Websites

Regeln:
- Extrahieren Sie nur echte, legitime Websites und Tools
- Konvertieren Sie Tool-/Service-Namen zu ihren tatsächlichen Website-URLs
- Überspringen Sie URL-Verkürzer (bit.ly, t.co, lnkd.in, etc.)
- Überspringen Sie Social-Media-Post-URLs
- Konzentrieren Sie sich auf nützliche Ressourcen, die das Publikum als Lesezeichen speichern möchte
- Schließen Sie die Haupt-Website ein, auch wenn eine spezifische Seite erwähnt wird

Antworten Sie NUR mit einem JSON-Array von Objekten, die "name", "url", "description" und "category" Felder enthalten.
Kategorien sollten eine der folgenden sein: "Tools", "Platforms", "News", "Education", "Business", "Entertainment", "Technology"

Beispiel: [{"name": "Bolt.new", "url": "https://bolt.new", "description": "KI-gestützte Webentwicklungsplattform", "category": "Tools"}]

JSON-Ausgabe:`
  },

  es: {
    categorization: `Eres un experto analista de contenido de redes sociales. Tu tarea es categorizar la siguiente publicación de redes sociales en la categoría más relevante.

--- INICIO DEL CONTENIDO DE LA PUBLICACIÓN ---
{postContent}
--- FIN DEL CONTENIDO DE LA PUBLICACIÓN ---

Categorías disponibles: {categories}

Reglas:
- Selecciona la única categoría más relevante que mejor capture la esencia principal de la publicación
- La categoría debe ser de la lista proporcionada
- Considera el tema principal, la industria y el tipo de contenido
- Elige solo la categoría que mejor se ajuste

Responde SOLO con un objeto JSON con una sola clave "categories" que contenga un array con exactamente un string de categoría.
Ejemplo: {"categories": ["tecnologia"]}

Salida JSON:`,

    tagging: `Eres un experto analista de contenido de redes sociales. Tu tarea es generar etiquetas relevantes para una publicación de redes sociales dada.

--- INICIO DEL CONTENIDO DE LA PUBLICACIÓN ---
{postContent}
--- FIN DEL CONTENIDO DE LA PUBLICACIÓN ---

Genera {maxTags} etiquetas relevantes, específicas y buscables para este contenido.

Reglas:
- Las etiquetas deben ser descriptivas y específicas
- Usa minúsculas con guiones bajos para etiquetas de múltiples palabras
- Enfócate en temas clave, temas y conceptos
- Evita etiquetas genéricas como "redes_sociales" o "publicacion"
- Considera terminología específica de la industria

Proporciona tus sugerencias como un array JSON de strings.
Ejemplo: ["inteligencia_artificial", "innovacion", "tecnologia_futura", "destacados_conferencia"]

Salida JSON:`,

    insight: `Analiza el siguiente contenido de publicación de redes sociales y proporciona perspectivas:
--- INICIO DEL CONTENIDO DE LA PUBLICACIÓN ---
{postContent}
--- FIN DEL CONTENIDO DE LA PUBLICACIÓN ---

Proporciona un análisis completo incluyendo:
- Sentimiento (positivo/negativo/neutral)
- Genel duygu durumunu veya ana temayı yansıtan tek, uygun bir emoji
- Temas y tópicos clave
- Tono emocional
- Potencial de engagement
- Perspectivas de audiencia objetivo

Responde SOLO con un objeto JSON que contenga: sentiment, emoji, themes, tone, engagement_score (1-10), y audience_insights.
Ejemplo: {"sentiment": "positivo", "emoji": "💡", "themes": ["innovacion", "tecnologia"], "tone": "entusiasta", "engagement_score": 8, "audience_insights": "Entusiastas de la tecnología y profesionales"}

Salida JSON:`,

    fastTake: `Analiza el siguiente contenido de publicación de redes sociales y proporciona una "Vista Rápida":
--- INICIO DEL CONTENIDO DE LA PUBLICACIÓN ---
{postContent}
--- FIN DEL CONTENIDO DE LA PUBLICACIÓN ---

Una "Vista Rápida" es un resumen conciso y perspicaz que captura la esencia y el punto clave del contenido en 1-2 oraciones. Debe ser accionable y memorable.

Responde SOLO con un objeto JSON con una sola clave "fastTake" que contenga el string.
Ejemplo: {"fastTake": "Avance revolucionario de IA podría transformar la precisión de predicción meteorológica en 99% usando análisis avanzado de datos satelitales."}

Salida JSON:`,

    snapNote: `Analiza el siguiente contenido de publicación de redes sociales y crea una SnapNote:
--- INICIO DEL CONTENIDO DE LA PUBLICACIÓN ---
{postContent}
--- FIN DEL CONTENIDO DE LA PUBLICACIÓN ---

Una "SnapNote" es un micro-título o resumen breve, atractivo y contextualmente relevante (máx. 150 caracteres). Debe capturar la esencia del contenido de una manera útil para referencia rápida y organización.

{imageContext}

Responde SOLO con un objeto JSON con una sola clave "snapNote" que contenga el string.
Ejemplo: {"snapNote": "Avance IA permite predicción meteorológica 99% precisa con datos satelitales."}

Salida JSON:`,

    contentIdeas: `Analiza el siguiente contenido de publicación de redes sociales:
--- INICIO DEL CONTENIDO DE LA PUBLICACIÓN ---
{postContent}
--- FIN DEL CONTENIDO DE LA PUBLICACIÓN ---

Basado en este contenido, genera 2-3 ideas de contenido distintas y accionables para futuras publicaciones. Estas ideas podrían ser preguntas de seguimiento, temas relacionados, diferentes ángulos, o llamadas a la acción.
Las ideas deben ser creativas y atractivas.
{existingIdeasContext}

Responde SOLO con un objeto JSON con una sola clave "contentIdeas" que contenga un array de strings.
Ejemplo: {"contentIdeas": ["Pregunta a tu audiencia sobre sus experiencias con X.", "Crea una encuesta comparando Y y Z.", "Comparte un análisis profundo del aspecto A del tema."]}

Salida JSON:`,

    bookmarkExtraction: `Eres un experto en identificar sitios web, herramientas, plataformas y servicios mencionados en publicaciones de redes sociales. Tu tarea es extraer marcadores reales y útiles del siguiente contenido de publicación.

--- INICIO DEL CONTENIDO DE LA PUBLICACIÓN ---
{postContent}
--- FIN DEL CONTENIDO DE LA PUBLICACIÓN ---

Extrae sitios web, herramientas, plataformas y servicios que se mencionan o referencian en esta publicación. Busca:
- Menciones directas de sitios web (como "bolt.new", "github.com", "openai.com")
- Nombres de herramientas (como "ChatGPT", "Figma", "Notion", "Slack")
- Nombres de plataformas (como "YouTube", "TikTok", "LinkedIn")
- Nombres de servicios (como "AWS", "Stripe", "Shopify")
- Sitios web de empresas que se pueden inferir del contexto
- Recursos educativos o sitios de documentación

Reglas:
- Solo extrae sitios web y herramientas reales y legítimas
- Convierte nombres de herramientas/servicios a sus URLs de sitios web reales
- Omite acortadores de URL (bit.ly, t.co, lnkd.in, etc.)
- Omite URLs de publicaciones de redes sociales
- Enfócate en recursos útiles que la audiencia querría marcar
- Incluye el sitio web principal incluso si se menciona una página específica

Responde SOLO con un array JSON de objetos que contengan los campos "name", "url", "description" y "category".
Las categorías deben ser una de: "Tools", "Platforms", "News", "Education", "Business", "Entertainment", "Technology"

Ejemplo: [{"name": "Bolt.new", "url": "https://bolt.new", "description": "Plataforma de desarrollo web impulsada por IA", "category": "Tools"}]

Salida JSON:`
  }
};

/**
 * Get localized prompt template for a specific type and language
 */
export function getPromptTemplate(type: keyof PromptTemplates, locale: string): string {
  const supportedLocales = ['en', 'tr', 'fr', 'de', 'es'];
  const fallbackLocale = 'en';

  // Use the requested locale if supported, otherwise fall back to English
  const selectedLocale = supportedLocales.includes(locale) ? locale : fallbackLocale;

  return promptTemplates[selectedLocale][type];
}

/**
 * Generate a localized prompt by replacing placeholders with actual values
 */
export function generateLocalizedPrompt(
  type: keyof PromptTemplates,
  locale: string,
  params: Record<string, string | number>
): string {
  let template = getPromptTemplate(type, locale);

  // Replace placeholders with actual values
  Object.entries(params).forEach(([key, value]) => {
    const placeholder = `{${key}}`;
    template = template.replace(new RegExp(placeholder, 'g'), String(value));
  });

  return template;
}

/**
 * Get current user locale from Chrome storage
 */
export async function getCurrentLocale(): Promise<string> {
  return new Promise((resolve) => {
    chrome.storage.sync.get({ locale: 'en' }, (result: { locale: string }) => {
      resolve(result.locale || 'en');
    });
  });
}

export default {
  getPromptTemplate,
  generateLocalizedPrompt,
  getCurrentLocale
};
