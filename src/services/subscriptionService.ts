/**
 * Subscription Service
 * 
 * This service handles subscription management for the extension.
 * It provides methods for checking subscription status, creating checkout sessions,
 * and managing billing.
 */

import * as authService from './authService';
import { getPriceId, PRICING_INFO } from '../config/stripe';

const API_URL = 'https://api.notely.social';

export interface SubscriptionDetails {
  id: string;
  status: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  cancelAt: number | null;
  trialEnd: number | null;
  plan: string;
}

/**
 * Get user subscription details
 */
export const getSubscriptionDetails = async (): Promise<SubscriptionDetails | null> => {
  try {
    const token = await authService.getToken();
    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(`${API_URL}/billing/subscription`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Subscription service received data:', data);

      // Extract the subscription object from the response
      if (data.subscription) {
        console.log('Returning subscription data:', data.subscription);
        return data.subscription;
      } else {
        console.log('No subscription data found in response');
        return null;
      }
    } else if (response.status === 401) {
      // Token expired
      await authService.logout();
      return null;
    } else {
      throw new Error(`Failed to fetch subscription details: ${response.status}`);
    }
  } catch (error) {
    console.error('Error fetching subscription details:', error);
    return null;
  }
};

/**
 * Create Stripe checkout session
 */
export const createCheckoutSession = async (priceId: string, couponCode?: string): Promise<string | null> => {
  try {
    const token = await authService.getToken();
    if (!token) {
      throw new Error('No authentication token available');
    }

    console.log('[SubscriptionService] Creating checkout session with:', { priceId, couponCode });

    const response = await fetch(`${API_URL}/billing/create-checkout-session`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        priceId,
        couponCode,
        successUrl: `chrome-extension://${chrome.runtime.id}/dashboard.html?success=true`,
        cancelUrl: `chrome-extension://${chrome.runtime.id}/dashboard.html?tab=settings`
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('[SubscriptionService] Checkout session created successfully:', data);
      return data.url;
    } else if (response.status === 401) {
      // Token expired
      console.log('[SubscriptionService] Token expired, logging out');
      await authService.logout();
      return null;
    } else {
      const errorText = await response.text();
      console.error('[SubscriptionService] Checkout session failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      throw new Error(`Failed to create checkout session: ${response.status}`);
    }
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return null;
  }
};

/**
 * Create billing portal session
 */
export const createPortalSession = async (): Promise<string | null> => {
  try {
    const token = await authService.getToken();
    if (!token) {
      console.error('No authentication token available for billing portal');
      throw new Error('No authentication token available');
    }

    console.log('Creating billing portal session...');
    const response = await fetch(`${API_URL}/billing/create-portal-session`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        return_url: 'https://notely.social/dashboard' // Use web dashboard for return
      })
    });

    console.log('Billing portal response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('Billing portal session created successfully');
      return data.url;
    } else if (response.status === 401) {
      console.error('Authentication failed for billing portal');
      // Token expired
      await authService.logout();
      return null;
    } else {
      // Try to get error details from response
      let errorMessage = `HTTP ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
        console.error('Billing portal error details:', errorData);
      } catch (parseError) {
        console.error('Could not parse error response');
      }

      throw new Error(`Failed to create portal session: ${errorMessage}`);
    }
  } catch (error) {
    console.error('Error creating portal session:', error);
    return null;
  }
};

/**
 * Cancel subscription
 */
export const cancelSubscription = async (): Promise<boolean> => {
  try {
    const token = await authService.getToken();
    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(`${API_URL}/billing/cancel-subscription`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      // Refresh user profile to get updated subscription status
      await authService.fetchUserProfile();
      return true;
    } else if (response.status === 401) {
      // Token expired
      await authService.logout();
      return false;
    } else {
      throw new Error(`Failed to cancel subscription: ${response.status}`);
    }
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return false;
  }
};

/**
 * Reactivate subscription
 */
export const reactivateSubscription = async (): Promise<boolean> => {
  try {
    const token = await authService.getToken();
    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(`${API_URL}/billing/reactivate-subscription`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      // Refresh user profile to get updated subscription status
      await authService.fetchUserProfile();
      return true;
    } else if (response.status === 401) {
      // Token expired
      await authService.logout();
      return false;
    } else {
      throw new Error(`Failed to reactivate subscription: ${response.status}`);
    }
  } catch (error) {
    console.error('Error reactivating subscription:', error);
    return false;
  }
};

/**
 * Check if user has active premium subscription
 */
export const hasActivePremiumSubscription = async (): Promise<boolean> => {
  try {
    const user = await authService.getUser();
    if (!user) {
      return false;
    }

    return user.plan === 'premium' && 
           (user.subscriptionStatus === 'active' || user.subscriptionStatus === 'trialing');
  } catch (error) {
    console.error('Error checking premium subscription:', error);
    return false;
  }
};

/**
 * Get subscription status for display
 */
export const getSubscriptionStatusDisplay = async (): Promise<string> => {
  try {
    const user = await authService.getUser();
    if (!user) {
      return 'Not logged in';
    }

    if (user.plan === 'free') {
      return 'Free Plan';
    }

    const status = user.subscriptionStatus || 'unknown';
    switch (status) {
      case 'active':
        return 'Premium Active';
      case 'trialing':
        return 'Premium Trial';
      case 'past_due':
        return 'Payment Past Due';
      case 'canceled':
        return 'Premium Canceled';
      default:
        return `Premium (${status})`;
    }
  } catch (error) {
    console.error('Error getting subscription status display:', error);
    return 'Unknown';
  }
};

/**
 * Get available pricing plans
 */
export const getAvailablePlans = async (): Promise<any[]> => {
  try {
    const response = await fetch(`${API_URL}/billing/plans`);

    if (response.ok) {
      const data = await response.json();
      return data.plans || [];
    } else {
      throw new Error(`Failed to fetch plans: ${response.status}`);
    }
  } catch (error) {
    console.error('Error fetching available plans:', error);

    // Return fallback plans if API fails
    return [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        interval: null,
        priceId: null,
        features: [
          'Basic AI analysis',
          '1GB cloud storage',
          'Standard support'
        ]
      },
      {
        id: 'monthly',
        name: 'Premium Monthly',
        price: 9.99,
        interval: 'month',
        priceId: 'price_monthly_fallback',
        features: [
          'Unlimited AI analysis',
          '10GB cloud storage',
          'Advanced content insights',
          'Priority support'
        ]
      },
      {
        id: 'yearly',
        name: 'Premium Yearly',
        price: 99.99,
        interval: 'year',
        priceId: 'price_yearly_fallback',
        features: [
          'Unlimited AI analysis',
          '10GB cloud storage',
          'Advanced content insights',
          'Priority support',
          '2 months free'
        ]
      }
    ];
  }
};

/**
 * Change subscription plan (upgrade/downgrade)
 */
export const changePlan = async (priceId: string): Promise<boolean> => {
  try {
    const token = await authService.getToken();
    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(`${API_URL}/billing/change-plan`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ priceId })
    });

    if (response.ok) {
      // Refresh user profile to get updated subscription status
      await authService.fetchUserProfile();
      return true;
    } else if (response.status === 401) {
      // Token expired
      await authService.logout();
      return false;
    } else {
      throw new Error(`Failed to change plan: ${response.status}`);
    }
  } catch (error) {
    console.error('Error changing plan:', error);
    return false;
  }
};

/**
 * Refresh user subscription data
 */
export const refreshSubscriptionData = async (): Promise<void> => {
  try {
    await authService.fetchUserProfile();
  } catch (error) {
    console.error('Error refreshing subscription data:', error);
  }
};
