import { Post } from '../types';
import { WisdomQuote } from '../types/wisdom';
import { getSavedPosts } from '../storage';
import { v4 as uuidv4 } from 'uuid';
import { transformContentSecurely } from './secureWisdomService';

// AI constants retained for future server-side implementation
// const AI_API_ENDPOINT = 'https://api.openai.com/v1/chat/completions';
// const AI_MODEL = 'gpt-4o';

/**
 * Service for extracting wisdom quotes from user's saved posts
 */

/**
 * Extracts potential wisdom quotes from saved posts
 * Looks for posts with meaningful content that can be turned into wisdom
 */
export async function extractWisdomFromPosts(maxQuotes: number = 5): Promise<WisdomQuote[]> {
  try {
    console.log('wisdomExtraction: Attempting to get saved posts...');
    const savedPosts = await getSavedPosts();
    console.log('wisdomExtraction: Retrieved', savedPosts.length, 'saved posts');

    if (savedPosts.length === 0) {
      console.log('wisdomExtraction: No saved posts available');
      return [];
    }

    // Filter posts that are suitable for wisdom extraction
    const suitablePosts = savedPosts.filter(post => {
      const content = post.content || post.textContent || post.text || '';
      const hasContent = content.length > 10; // More permissive length requirement
      const hasAuthor = post.author || post.authorName || 'Unknown Author';
      // Remove time restriction - allow all posts regardless of age
      
      console.log('wisdomExtraction: Checking post:', {
        id: post.id,
        platform: post.platform,
        contentLength: content.length,
        hasAuthor: !!hasAuthor,
        content: content.substring(0, 100) + '...'
      });
      
      return hasContent; // Much more permissive - just needs some content
    });

    if (suitablePosts.length === 0) {
      return [];
    }

    // Sort by engagement and recency, then add some randomization
    const sortedPosts = suitablePosts.sort((a, b) => {
      const aEngagement = (a.stats?.likes || 0) + (a.stats?.shares || 0) + (a.stats?.comments || 0);
      const bEngagement = (b.stats?.likes || 0) + (b.stats?.shares || 0) + (b.stats?.comments || 0);
      const aDate = new Date(a.savedAt || a.timestamp || 0).getTime();
      const bDate = new Date(b.savedAt || b.timestamp || 0).getTime();

      // Combine engagement and recency scores
      const aScore = aEngagement * 0.7 + (aDate / 1000000000) * 0.3;
      const bScore = bEngagement * 0.7 + (bDate / 1000000000) * 0.3;

      return bScore - aScore;
    });

    // Add randomization to make refresh more effective
    // Take top posts but shuffle them to get variety on refresh
    const topPosts = sortedPosts.slice(0, Math.min(maxQuotes * 2, sortedPosts.length));
    const shuffledPosts = [...topPosts].sort(() => Math.random() - 0.5);

    // Extract wisdom from shuffled posts
    const extractedWisdom: WisdomQuote[] = [];
    const targetCount = Math.min(maxQuotes, shuffledPosts.length);

    // Try to extract from more posts to increase success rate
    const maxAttempts = Math.min(shuffledPosts.length, targetCount * 3);

    for (let i = 0; i < maxAttempts && extractedWisdom.length < targetCount; i++) {
      const post = shuffledPosts[i];
      const wisdom = await extractWisdomFromPost(post);

      if (wisdom) {
        extractedWisdom.push(wisdom);
        console.log(`wisdomExtraction: Successfully extracted wisdom ${extractedWisdom.length}/${targetCount} from post ${post.id}`);
      } else {
        console.log(`wisdomExtraction: Failed to extract wisdom from post ${post.id} (${post.platform})`);
      }
    }

    console.log(`wisdomExtraction: Final result: ${extractedWisdom.length} wisdom quotes from ${maxAttempts} attempts`);
    return extractedWisdom;

  } catch (error) {
    console.error('wisdomExtraction: Error extracting wisdom from posts:', error);
    return [];
  }
}

/**
 * Extracts a wisdom quote from a single post
 */
async function extractWisdomFromPost(post: Post): Promise<WisdomQuote | null> {
  try {
    const content = post.content || post.textContent || post.text || '';

    console.log('wisdomExtraction: Processing post:', {
      id: post.id,
      originalPostId: post.originalPostId,
      platform: post.platform,
      author: post.author,
      permalink: post.permalink,
      source_link: post.source_link,
      contentLength: content.length
    });

    if (!content || content.length < 5) {
      console.log('wisdomExtraction: Skipping post - insufficient content');
      return null;
    }

    // Transform content into actual wisdom
    const wisdomText = await transformContentToWisdom(content);

    if (!wisdomText) {
      return null;
    }

    // Determine author
    let author = 'From Post';
    if (post.author && post.author !== 'Unknown') {
      author = post.author;
    } else if (post.authorName && post.authorName !== 'Unknown') {
      author = post.authorName;
    }

    // Generate tags based on post categories and content
    const tags = generateWisdomTags(post, wisdomText);

    // Get the best link to the original post
    const sourceLink = post.permalink || post.source_link || '';

    // Store all possible IDs for better matching later
    const relatedIds = [
      post.id,
      post.originalPostId,
      // Extract ID from permalink if possible
      ...(post.permalink ? [post.permalink.split('/').pop()] : []),
      // Extract ID from source_link if possible
      ...(post.source_link ? [post.source_link.split('/').pop()] : [])
    ].filter(Boolean); // Remove any undefined/null/empty values

    // Create wisdom quote
    const wisdomQuote: WisdomQuote = {
      id: uuidv4(),
      text: wisdomText,
      author: author,
      source: `${post.platform} Post`,
      source_link: sourceLink,
      categories: post.categories || ['Inspiration'],
      tags: tags,
      createdAt: new Date().toISOString(),
      extractedFrom: 'post',
      relatedPostIds: relatedIds
    };

    console.log('wisdomExtraction: Created wisdom quote with IDs:', {
      quoteId: wisdomQuote.id,
      relatedIds: wisdomQuote.relatedPostIds,
      sourceLink: wisdomQuote.source_link
    });

    return wisdomQuote;

  } catch (error) {
    console.error('wisdomExtraction: Error extracting wisdom from individual post:', error);
    return null;
  }
}

/**
 * Transforms content into wisdom by distilling the core insight
 */
  async function transformContentToWisdom(content: string): Promise<string | null> {
    // First clean the content
    const cleaned = cleanRawContent(content);

    if (!cleaned) {
      return null;
    }

    // Ensure the content actually contains wisdom and isn't promotional
    if (!isWisdomWorthy(cleaned)) {
      console.log('wisdomExtraction: Skipping non-wisdom content:', cleaned.substring(0, 80));
      return null;
    }

    // Try to extract wisdom using AI transformation
    try {
      const transformedWisdom = await transformWithAI(cleaned);
      if (transformedWisdom) {
      return transformedWisdom;
    }
  } catch (error) {
    console.error('wisdomExtraction: AI transformation failed:', error);
  }

  // Fallback to manual transformation
  return manualWisdomTransformation(cleaned);
}

/**
 * Cleans raw content by removing promotional elements
 */
function cleanRawContent(content: string): string | null {
  // Remove URLs
  let cleaned = content.replace(/https?:\/\/[^\s]+/g, '');

  // Remove mentions and hashtags
  cleaned = cleaned.replace(/@\w+/g, '');
  cleaned = cleaned.replace(/#\w+/g, '');

  // Remove emojis
  cleaned = cleaned.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '');

  // Remove promotional phrases
  const promotionalPhrases = [
    /here's the prompt/gi,
    /this tool will/gi,
    /click the link/gi,
    /follow for more/gi,
    /link in bio/gi,
    /swipe up/gi,
    /dm me/gi,
    /check out/gi,
    /sign up/gi,
    /get started/gi,
    /try this/gi,
    /use this/gi,
    /download/gi,
    /subscribe/gi,
    /like and share/gi,
    /comment below/gi,
    /what do you think/gi,
    /let me know/gi,
    /thread \d+\/\d+/gi,
    /\d+\/\d+$/gi
  ];

  promotionalPhrases.forEach(phrase => {
    cleaned = cleaned.replace(phrase, '');
  });

  // Remove extra whitespace and punctuation
  cleaned = cleaned.replace(/\s+/g, ' ').trim();
  cleaned = cleaned.replace(/[.]{2,}/g, '.');

  // Remove quotes if the entire content is quoted
  if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
    cleaned = cleaned.slice(1, -1).trim();
  }

  if (!cleaned || cleaned.length < 5) {
    return null;
  }

  return cleaned;
}

/**
 * Checks if content has wisdom potential
 */
  function isWisdomWorthy(content: string): boolean {
    const contentLower = content.toLowerCase();

    // Reject very short or generic content
    if (contentLower.split(/\s+/).length < 5) {
      return false;
    }

    // Skip if it's clearly promotional or instructional
    const skipPatterns = [
      /step \d+/, 
      /how to/, 
      /tutorial/, 
      /guide/, 
      /tips for/, 
      /ways to/, 
      /you can now/, 
      /here's how/, 
      /this will help/, 
      /use this to/, 
      /rip .* marketers/, 
      /\d+ reasons/, 
      /top \d+/, 
      /best \d+/
    ];

    if (skipPatterns.some(pattern => pattern.test(contentLower))) {
      return false;
    }

    // Additional promotional/low-value checks
    const promotionalPatterns = [
      /subscribe/,
      /sale/,
      /discount/,
      /buy now/,
      /sign up/,
      /limited time/,
      /offer/,
      /check out/,
      /link in bio/,
      /follow (me|for more)/,
      /giveaway/,
      /free trial/,
      /download/,
      /sponsored/,
      /promo code/,
      /deal/,
      /shop now/,
      /get yours/,
      /webinar/,
      /course/,
      /dm me/
    ];

    if (promotionalPatterns.some(pattern => pattern.test(contentLower))) {
      return false;
    }

    // Look for wisdom indicators
    const wisdomIndicators = [
      /\b(wisdom|truth|principle|insight|understanding|perspective)\b/i,
      /\b(learn|grow|journey|experience|reflection|realize)\b/i,
      /\b(life|success|failure|challenge|opportunity|change)\b/i,
      /\b(always|never|remember|important|essential|meaningful)\b/i,
      /\b(people|everyone|we|us|human|person)\b/i
    ];

    return wisdomIndicators.some(pattern => pattern.test(content));
  }

/**
 * Manual transformation of content into wisdom format
 * Now extracts multiple wisdom insights from different types of content
 */
function manualWisdomTransformation(content: string): string | null {
  console.log('wisdomExtraction: Starting manual transformation for:', content.substring(0, 100));

  // First, try to identify the content type and extract wisdom accordingly
  const contentType = identifyContentType(content);
  console.log('wisdomExtraction: Identified content type:', contentType);

  let extractedWisdom = null;

  switch (contentType) {
    case 'business_advice':
      extractedWisdom = extractBusinessWisdom(content);
      break;
    case 'design_resource':
      extractedWisdom = extractDesignWisdom(content);
      break;
    case 'personal_insight':
      extractedWisdom = extractPersonalWisdom(content);
      break;
    case 'cultural_content':
      extractedWisdom = extractCulturalWisdom(content);
      break;
    case 'general':
    default:
      extractedWisdom = extractGeneralWisdom(content);
      break;
  }

  if (!extractedWisdom) {
    console.log('wisdomExtraction: No wisdom extracted for content type:', contentType, '- deferring to other sources');
    return null;
  }

  // Clean up and format the wisdom
  let wisdom = extractedWisdom.trim();

  // Ensure proper capitalization
  wisdom = wisdom.charAt(0).toUpperCase() + wisdom.slice(1);

  // Ensure proper punctuation
  if (!wisdom.match(/[.!?]$/)) {
    wisdom += '.';
  }

  // Final validation
  if (wisdom.length < 15 || wisdom.length > 300) {
    console.log('wisdomExtraction: Final validation failed:', wisdom.length);
    return null;
  }

  console.log('wisdomExtraction: Manual transformation successful:', wisdom);
  return wisdom;
}

/**
 * Identifies the type of content to apply appropriate wisdom extraction
 */
function identifyContentType(content: string): string {
  const lowerContent = content.toLowerCase();

  if (/\b(business|bottleneck|leads|conversion|operations|strategy|entrepreneur)\b/.test(lowerContent)) {
    return 'business_advice';
  }

  if (/\b(design|guide|ai|workflow|tool|resource|collection)\b/.test(lowerContent)) {
    return 'design_resource';
  }

  if (/\b(realized|reflecting|never even|watching|video)\b/.test(lowerContent)) {
    return 'personal_insight';
  }

  if (/\b(traditional|discover|culture|food|chania|greece)\b/.test(lowerContent)) {
    return 'cultural_content';
  }

  return 'general';
}

/**
 * Extracts wisdom from business advice content
 */
function extractBusinessWisdom(content: string): string | null {
  // Look for business principles and actionable insights
  const businessPatterns = [
    /at any time[,\s]+we focus on (\d+[^.]*)/i,
    /the only (\d+[^:]*): ([^.]*)/i,
    /(focus|build|create|start)[^.]*\./i,
    /relentless obsession on ([^.]*)/i
  ];

  for (const pattern of businessPatterns) {
    const match = content.match(pattern);
    if (match) {
      const wisdom = match[0].trim();
      // Transform into wisdom format
      if (wisdom.includes('focus on 1 initiative')) {
        return 'Success comes from focusing on one initiative at a time rather than spreading efforts across multiple projects';
      }
      if (wisdom.includes('business bottlenecks')) {
        return 'Every business faces three core bottlenecks: generating leads, converting prospects, and optimizing operations';
      }
      return wisdom;
    }
  }

  return null;
}

/**
 * Extracts wisdom from design/resource content
 */
function extractDesignWisdom(content: string): string | null {
  // Transform resource descriptions into wisdom about learning and tools
  if (/ai.*design.*guide/i.test(content)) {
    return 'The best learning happens when knowledge is organized, accessible, and immediately applicable to real projects';
  }

  if (/collection.*free.*guides/i.test(content)) {
    return 'Great resources are not just about having information, but about making that information actionable and easy to use';
  }

  if (/workflow.*building/i.test(content)) {
    return 'Whether you\'re a beginner or expert, the right tools and guidance can accelerate your progress significantly';
  }

  return null;
}

/**
 * Extracts wisdom from personal insights and reflections
 */
function extractPersonalWisdom(content: string): string | null {
  if (/never even realized.*before.*reflecting/i.test(content)) {
    return 'Sometimes the most profound insights come from stepping back and reflecting on what we thought we already knew';
  }

  if (/watching.*video.*realized/i.test(content)) {
    return 'Learning often happens in unexpected moments when we\'re open to seeing familiar things from a new perspective';
  }

  return null;
}

/**
 * Extracts wisdom from cultural content
 */
function extractCulturalWisdom(content: string): string | null {
  if (/traditional.*discover/i.test(content)) {
    return 'Every culture holds treasures of wisdom and tradition that enrich our understanding of the world';
  }

  if (/chania.*greece.*traditional/i.test(content)) {
    return 'Exploring different cultures opens our minds to new ways of creating, thinking, and living';
  }

  return null;
}

/**
 * Extracts wisdom from general content using improved scoring
 */
function extractGeneralWisdom(content: string): string | null {
  const sentences = content
    .split(/[.!?]+/)
    .map(s => s.trim())
    .filter(s => s.length > 10);

  if (sentences.length === 0) {
    return null;
  }

  const MIN_WISDOM_SCORE = 2;
  const wisdomCandidates: string[] = [];

  for (let sentence of sentences) {
    // Clean up social media artifacts
    sentence = sentence.replace(/^(RT|via|@\w+:?)\s*/i, '');
    sentence = sentence.replace(/\s*#\w+/g, '');
    sentence = sentence.replace(/\s*@\w+/g, '');
    sentence = sentence.replace(/^(quoted tweet|quote tweet):\s*/i, '');
    sentence = sentence.replace(/^\d+\.\s*/, '');
    sentence = sentence.replace(/^-\s*/, '');

    if (sentence.length < 15 || sentence.length > 300) continue;

    const lowerSentence = sentence.toLowerCase();
    let score = 0;

    // Positive indicators
    if (/\b(learn|realize|understand|discover|insight|principle|lesson|truth|wisdom)\b/.test(lowerSentence)) score += 4;
    if (/\b(always|never|key|important|essential|crucial|fundamental|critical)\b/.test(lowerSentence)) score += 2;
    if (/\b(experience|reflect|think|believe|know|feel|find)\b/.test(lowerSentence)) score += 2;
    if (/\b(focus|build|create|start|stop|avoid|remember|consider)\b/.test(lowerSentence)) score += 2;

    // Negative indicators
    if (/\b(guide|collection|resource|tool|download|click|link|website|app|free)\b/.test(lowerSentence)) score -= 3;
    if (/\b(died|starvation|gaza|ministry|says|hours)\b/.test(lowerSentence)) score -= 5;
    if (/\d+\s*$/.test(sentence)) score -= 2;

    if (score >= MIN_WISDOM_SCORE) {
      wisdomCandidates.push(sentence);
    }
  }

  if (wisdomCandidates.length === 0) {
    console.log('wisdomExtraction: No sentences met the minimum wisdom score');
    return null;
  }

  const randomIndex = Math.floor(Math.random() * wisdomCandidates.length);
  return wisdomCandidates[randomIndex];
}

/**
 * Transforms content into wisdom using AI
 */
async function transformWithAI(content: string): Promise<string | null> {
  const response = await transformContentSecurely(content);
  if (response.success && response.wisdom) {
    return response.wisdom;
  }
  console.warn('wisdomExtraction: Secure AI transformation failed', response.error);
  return manualWisdomTransformation(content);
}

/**
 * Generates relevant tags for wisdom based on post content and metadata
 */
function generateWisdomTags(post: Post, content: string): string[] {
  const tags: string[] = [];

  // Add content-based tags (human-readable format)
  const contentLower = content.toLowerCase();

  const tagMappings = {
    'Growth': ['grow', 'development', 'progress', 'improve', 'better'],
    'Success': ['success', 'achieve', 'goal', 'win', 'accomplish'],
    'Mindset': ['mindset', 'think', 'believe', 'perspective', 'attitude'],
    'Leadership': ['lead', 'leader', 'team', 'manage', 'guide'],
    'Motivation': ['motivat', 'inspir', 'drive', 'passion', 'energy'],
    'Wisdom': ['wisdom', 'learn', 'lesson', 'insight', 'truth'],
    'Life': ['life', 'living', 'experience', 'journey', 'path'],
    'Business': ['business', 'work', 'career', 'professional', 'company'],
    'Creativity': ['creative', 'innovation', 'idea', 'design', 'art'],
    'Focus': ['productive', 'efficient', 'time', 'focus', 'organize']
  };

  for (const [tag, keywords] of Object.entries(tagMappings)) {
    if (keywords.some(keyword => contentLower.includes(keyword))) {
      tags.push(tag);
    }
  }

  // Add platform tag in readable format
  const platformTags = {
    'X/Twitter': 'Social Media',
    'LinkedIn': 'Professional',
    'Reddit': 'Community',
    'Instagram': 'Visual',
    'facebook': 'Social',
    'pinterest': 'Creative'
  };

  if (platformTags[post.platform as keyof typeof platformTags]) {
    tags.push(platformTags[post.platform as keyof typeof platformTags]);
  }

  // Remove duplicates and limit to 3 tags
  return [...new Set(tags)].slice(0, 3);
}

/**
 * Checks if a post is suitable for wisdom extraction
 */
export function isPostSuitableForWisdom(post: Post): boolean {
  const content = post.content || post.textContent || '';

  // Basic content requirements
  if (!content || content.length < 20 || content.length > 500) {
    return false;
  }

  // Must have some author information
  if (!post.author && !post.authorName) {
    return false;
  }

  // Clean the content first
  const cleaned = cleanRawContent(content);
  if (!cleaned) {
    return false;
  }

  // Check if it has wisdom potential
  return isWisdomWorthy(cleaned);
}
