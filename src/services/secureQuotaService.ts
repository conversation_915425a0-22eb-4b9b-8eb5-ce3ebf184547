import { getToken } from './authService';

const API_BASE_URL = 'https://api.notely.social';

export interface QuotaStatus {
  used: number;
  limit: number; // -1 for unlimited
  remaining: number; // -1 for unlimited
  resetTime?: Date;
  canAutoTag: boolean;
}

export interface QuotaResponse {
  success: boolean;
  quota: QuotaStatus;
  userPlan: 'free' | 'premium';
  error?: string;
}

/**
 * Get current auto-tagging quota status from server
 * This cannot be tampered with by users as it's server-side
 */
export async function getAutoTaggingQuotaStatus(): Promise<QuotaResponse> {
  try {
    const token = await getToken();
    if (!token) {
      return {
        success: false,
        quota: {
          used: 0,
          limit: 0,
          remaining: 0,
          canAutoTag: false,
        },
        userPlan: 'free',
        error: 'Authentication required',
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/posts/auto-tag/quota`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      return {
        success: false,
        quota: {
          used: 0,
          limit: 0,
          remaining: 0,
          canAutoTag: false,
        },
        userPlan: 'free',
        error: errorData.error || `Server error: ${response.status}`,
      };
    }

    const data = await response.json();
    return data;

  } catch (error) {
    console.error('Error fetching quota status:', error);
    return {
      success: false,
      quota: {
        used: 0,
        limit: 0,
        remaining: 0,
        canAutoTag: false,
      },
      userPlan: 'free',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Generate AI tags using secure server-side API
 */
export async function generateSecureAITags(
  content: string,
  platform: string,
  postId?: string
): Promise<{
  success: boolean;
  tags: string[];
  quota?: QuotaStatus;
  error?: string;
}> {
  try {
    const token = await getToken();
    if (!token) {
      return {
        success: false,
        tags: [],
        error: 'Authentication required for AI tagging',
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/posts/auto-tag`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        content,
        platform,
        postId,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        tags: [],
        quota: data.quota,
        error: data.error || `Server error: ${response.status}`,
      };
    }

    return {
      success: true,
      tags: data.tags || [],
      quota: data.quota,
    };

  } catch (error) {
    console.error('Error generating secure AI tags:', error);
    return {
      success: false,
      tags: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Generate AI FastTake using secure server-side API
 */
export async function generateSecureAIFastTake(
  content: string
): Promise<{
  success: boolean;
  fastTake: string;
  error?: string;
}> {
  try {
    const token = await getToken();
    if (!token) {
      return {
        success: false,
        fastTake: '',
        error: 'Authentication required for AI FastTake',
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/posts/fast-take`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ content }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        fastTake: '',
        error: data.error || `Server error: ${response.status}`,
      };
    }

    return {
      success: true,
      fastTake: data.fastTake || '',
    };

  } catch (error) {
    console.error('Error generating secure AI FastTake:', error);
    return {
      success: false,
      fastTake: '',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Generate AI Insight using secure server-side API
 */
export async function generateSecureAIInsight(
  content: string
): Promise<{
  success: boolean;
  insight: {
    sentiment: 'positive' | 'neutral' | 'negative';
    emoji: string;
    contextTags: string[];
  } | null;
  error?: string;
}> {
  try {
    const token = await getToken();
    if (!token) {
      return {
        success: false,
        insight: null,
        error: 'Authentication required for AI Insight',
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/posts/insight`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ content }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        insight: null,
        error: data.error || `Server error: ${response.status}`,
      };
    }

    return {
      success: true,
      insight: data.insight || null,
    };

  } catch (error) {
    console.error('Error generating secure AI Insight:', error);
    return {
      success: false,
      insight: null,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}


