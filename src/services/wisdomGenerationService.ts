import { WisdomQuote } from '../types/wisdom';
import { Post } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { generateSecureWisdomQuotes, GeneratedWisdom } from './secureWisdomService';

/**
 * Service for generating AI-powered wisdom quotes based on trending content
 */

// AI constants retained for future secure server implementation
// const AI_API_ENDPOINT = 'https://api.openai.com/v1/chat/completions';
// const AI_MODEL = 'gpt-4o';
const CURATED_QUOTES_ENDPOINT = 'https://api.quotable.io/quotes';


/**
 * Generates AI wisdom quotes to fill remaining slots in 5-day batch
 */
export async function generateAIWisdom(
  neededCount: number,
  userPosts: Post[] = [],
  existingWisdom: WisdomQuote[] = []
): Promise<WisdomQuote[]> {
  try {
    if (neededCount <= 0) {
      return [];
    }

    const userContext = analyzeUserContentPatterns(userPosts);
    let generatedWisdom: GeneratedWisdom[] = [];

    // Attempt AI generation through secure server-side endpoint
    try {
      const response = await generateSecureWisdomQuotes(
        neededCount,
        userContext,
        existingWisdom.map(w => w.text)
      );
      if (response.success) {
        generatedWisdom = response.quotes;
      } else {
        console.warn('wisdomGeneration: Secure generation failed', response.error);
      }
    } catch (error) {
      console.error('wisdomGeneration: AI generation failed', error);
    }

    // Fallback to curated online quotes
    if (!generatedWisdom || generatedWisdom.length === 0) {
      generatedWisdom = await fetchCuratedQuotes(neededCount);
    }

    if (!generatedWisdom || generatedWisdom.length === 0) {
      return [];
    }

    const wisdomQuotes: WisdomQuote[] = generatedWisdom.map((wisdom, index) => ({
      id: uuidv4(),
      text: wisdom.quote,
      author: wisdom.author || 'Unknown',
      source: 'AI Generated',
      categories: [wisdom.category || 'General'],
      tags: wisdom.tags || [],
      createdAt: new Date().toISOString(),
      extractedFrom: 'ai-generated',
      dayIndex: index
    }));

    return wisdomQuotes;

  } catch (error) {
    console.error('wisdomGeneration: Error generating wisdom:', error);
    return [];
  }
}

/**
 * Analyzes user's saved posts to understand their interests and content patterns
 */
function analyzeUserContentPatterns(posts: Post[]): string {
  if (!posts || posts.length === 0) {
    return 'general productivity, success, and personal growth topics';
  }

  // Extract common themes from user's posts
  const themes: string[] = [];
  const platforms: string[] = [];
  const categories: string[] = [];
  
  posts.slice(0, 20).forEach(post => { // Analyze recent 20 posts
    // Collect platforms
    if (post.platform) {
      platforms.push(post.platform);
    }
    
    // Collect categories
    if (post.categories) {
      categories.push(...post.categories);
    }
    
    // Extract themes from content
    const content = (post.content || post.textContent || '').toLowerCase();
    
    const themeKeywords = {
      'technology': ['tech', 'ai', 'software', 'digital', 'innovation', 'startup'],
      'business': ['business', 'entrepreneur', 'marketing', 'sales', 'strategy'],
      'productivity': ['productive', 'efficiency', 'time', 'focus', 'organize'],
      'leadership': ['leadership', 'team', 'manage', 'lead', 'culture'],
      'creativity': ['creative', 'design', 'art', 'innovation', 'idea'],
      'personal growth': ['growth', 'learn', 'develop', 'improve', 'mindset'],
      'career': ['career', 'job', 'work', 'professional', 'skill'],
      'finance': ['money', 'invest', 'finance', 'wealth', 'economic'],
      'health': ['health', 'fitness', 'wellness', 'mental', 'exercise'],
      'social media': ['social', 'content', 'audience', 'engagement', 'viral']
    };
    
    for (const [theme, keywords] of Object.entries(themeKeywords)) {
      if (keywords.some(keyword => content.includes(keyword))) {
        themes.push(theme);
      }
    }
  });

  // Get most common themes
  const themeCount = themes.reduce((acc, theme) => {
    acc[theme] = (acc[theme] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const topThemes = Object.entries(themeCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([theme]) => theme);

  const topPlatforms = [...new Set(platforms)].slice(0, 2);
  
  return `The user is interested in ${topThemes.join(', ')} and actively saves content from ${topPlatforms.join(' and ')}. Focus on wisdom related to these areas.`;
}

// AI-based wisdom generation is handled via secure server-side endpoint.

/**
 * Retrieves curated quotes from an external API when AI generation fails
 */
async function fetchCuratedQuotes(count: number): Promise<GeneratedWisdom[]> {
  try {
    const apiKey = import.meta.env?.VITE_QUOTES_API_KEY || process.env.VITE_QUOTES_API_KEY || process.env.QUOTES_API_KEY;
    const headers: Record<string, string> = {};
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }

    const response = await fetch(`${CURATED_QUOTES_ENDPOINT}?limit=${count}`, { headers });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      throw new Error(`Quotes API request failed: ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    const quotes = data.results || [];
    return quotes.slice(0, count).map((q: any) => ({
      quote: q.content,
      author: q.author,
      tags: q.tags || [],
      category: q.tags?.[0] || 'General'
    }));
  } catch (error) {
    console.error('wisdomGeneration: Failed to fetch curated quotes', error);
    return [];
  }
}

