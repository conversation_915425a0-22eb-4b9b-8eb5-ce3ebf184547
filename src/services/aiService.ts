import { CORE_CATEGORIES, MAX_CATEGORIES, MAX_TAGS } from '../config/constants';
import { CoreSubCategorySlug } from '../config/constants'; // Assuming this type is exported
import { generateLocalizedPrompt, getCurrentLocale } from './aiPromptService';

// TODO: Replace with your actual AI provider's endpoint and model details
const AI_API_ENDPOINT = 'https://api.openai.com/v1/chat/completions'; // Example for OpenAI
const AI_MODEL = 'gpt-4o'; // Example model

// SECURITY: API keys should NEVER be accessible client-side for production
export async function getOpenAIApiKey(): Promise<string | null> {
  console.error('[SECURITY] getOpenAIApiKey is deprecated for security reasons. All AI operations must use secure server-side endpoints.');
  console.error('[SECURITY] Use secureQuotaService functions instead.');
  return null;
}

/**
 * Generates AI-suggested categories for a given post content.
 *
 * @param postTextContent The text content of the post.
 * @returns A promise that resolves to an array of category slugs.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function generateAICategories(
  postTextContent: string
): Promise<CoreSubCategorySlug[]> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing from environment or Chrome storage.');
    throw new Error('AI API key is missing. Please ensure VITE_OPENAI_API_KEY is set.');
  }

  const coreCategoriesJsonString = JSON.stringify(CORE_CATEGORIES);

  const prompt = `You are an expert social media content analyst. Your task is to categorize a given social media post based on its content.

Please analyze the following social media post:
--- POST CONTENT START ---
${postTextContent}
--- POST CONTENT END ---

Here is a list of available categories, structured as main_category: [sub_category1, sub_category2, ...]:
--- AVAILABLE CATEGORIES START ---
${coreCategoriesJsonString}
--- AVAILABLE CATEGORIES END ---

Based on the post content, please identify the single most relevant sub-category from the provided list.
Choose only the best-fitting category that captures the main essence of the post.
Ensure your suggestion is an exact match to a sub-category slug in the list.

Note: While you must return the exact category slug for system compatibility, the UI will display this in a user-friendly format with Title Case and spaces instead of underscores.

Output your suggestion as a JSON array containing exactly one string, where the string is a sub-category slug.
Example: ["technology"]

JSON Output:`;

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.5, // Adjust for creativity vs. determinism
        max_tokens: 100, // Adjust based on expected output size
        response_format: { type: "json_object" } // For models that support JSON mode
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error:', errorData);
      throw new Error(`AI API request failed: ${errorData.message || response.statusText}`);
    }

    const data = await response.json();

    // Assuming the AI returns a JSON object with a key (e.g., 'categories' or 'choices[0].message.content')
    // that contains the stringified JSON array of categories. This needs to be adapted based on actual API response.
    // For OpenAI, it's often in data.choices[0].message.content which is a stringified JSON.
    let suggestionsString = '';
    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
        suggestionsString = data.choices[0].message.content;
    } else {
        // Fallback or error if the structure is not as expected
        console.error('Unexpected AI API response structure for categories:', data);
        throw new Error('Unexpected AI API response structure for categories.');
    }

    try {
      const parsed = JSON.parse(suggestionsString);
      let suggestedCategories: CoreSubCategorySlug[] = [];

      // Handle different response formats
      if (Array.isArray(parsed)) {
        // Direct array format: ["health", "technology"]
        suggestedCategories = parsed;
      } else if (parsed && typeof parsed === 'object') {
        // Object format: {"categories": ["health"]} or {"health": ["health"]}
        const possibleKeys = ['categories', 'category', 'suggestions'];
        for (const key of possibleKeys) {
          if (Array.isArray(parsed[key])) {
            suggestedCategories = parsed[key];
            break;
          }
        }
        // If no standard key found, try to extract arrays from any key
        if (suggestedCategories.length === 0) {
          for (const [, value] of Object.entries(parsed)) {
            if (Array.isArray(value)) {
              suggestedCategories = value;
              break;
            }
          }
        }
      }

      // Further validation if needed (e.g., ensure they are valid slugs from CORE_CATEGORIES)
      return suggestedCategories.slice(0, MAX_CATEGORIES);
    } catch (parseError) {
      console.error('Failed to parse AI category suggestions:', parseError, 'Raw string:', suggestionsString);
      throw new Error('Failed to parse AI category suggestions.');
    }

  } catch (error) {
    console.error('Error generating AI categories:', error);
    // Re-throw or handle as appropriate for your application
    throw error;
  }
}

/**
 * Generates AI-suggested tags for a given post content.
 *
 * @param postTextContent The text content of the post.
 * @returns A promise that resolves to an array of tags.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function generateAITags(
  postTextContent: string
): Promise<string[]> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing from environment or Chrome storage.');
    throw new Error('AI API key is missing. Please ensure VITE_OPENAI_API_KEY is set.');
  }

  // Get current user locale and generate localized prompt
  const locale = await getCurrentLocale();
  const prompt = generateLocalizedPrompt('tagging', locale, {
    postContent: postTextContent,
    maxTags: MAX_TAGS
  });

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.5,
        max_tokens: 100,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error:', errorData);
      throw new Error(`AI API request failed: ${errorData.message || response.statusText}`);
    }

    const data = await response.json();

    // Similar to categories, adapt based on actual API response structure.
    let suggestionsString = '';
    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
        suggestionsString = data.choices[0].message.content;
    } else {
        console.error('Unexpected AI API response structure for tags:', data);
        throw new Error('Unexpected AI API response structure for tags.');
    }

    try {
      const parsed = JSON.parse(suggestionsString);
      let suggestedTags: string[] = [];

      // Handle different response formats
      if (Array.isArray(parsed)) {
        // Direct array format: ["ai", "technology"]
        suggestedTags = parsed;
      } else if (parsed && typeof parsed === 'object') {
        // Object format: {"tags": ["ai", "technology"]} or {"suggestions": ["ai"]}
        const possibleKeys = ['tags', 'tag', 'suggestions', 'keywords'];
        for (const key of possibleKeys) {
          if (Array.isArray(parsed[key])) {
            suggestedTags = parsed[key];
            break;
          }
        }
        // If no standard key found, try to extract arrays from any key
        if (suggestedTags.length === 0) {
          for (const [, value] of Object.entries(parsed)) {
            if (Array.isArray(value)) {
              suggestedTags = value;
              break;
            }
          }
        }
      }

      // Apply Title Case formatting to each tag (capitalize each word)
      return suggestedTags
        .map(tag => {
          // Replace underscores with spaces
          const withSpaces = tag.replace(/_/g, ' ');
          // Apply Title Case (capitalize each word)
          return withSpaces
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
        })
        .slice(0, MAX_TAGS);
    } catch (parseError) {
      console.error('Failed to parse AI tag suggestions:', parseError, 'Raw string:', suggestionsString);
      throw new Error('Failed to parse AI tag suggestions.');
    }

  } catch (error) {
    console.error('Error generating AI tags:', error);
    throw error;
  }
}

// --- NEW AI FUNCTIONS ---

/**
 * Represents the AI-generated insight data for a post.
 */
export interface InSightData {
  sentiment: 'positive' | 'neutral' | 'negative';
  emoji: string;
  contextTags: string[]; // Keywords related to the insight/sentiment
}

/**
 * Generates AI-driven insight (sentiment, emoji, context tags) for a given post content.
 *
 * @param postTextContent The text content of the post.
 * @returns A promise that resolves to an InSightData object.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function generateAIInsight(
  postTextContent: string
): Promise<InSightData> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for generateAIInsight.');
    throw new Error('AI API key is missing.');
  }

  // Get current user locale and generate localized prompt
  const locale = await getCurrentLocale();
  const prompt = generateLocalizedPrompt('insight', locale, {
    postContent: postTextContent
  });

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.4,
        max_tokens: 150,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error (generateAIInsight):', errorData);
      throw new Error(`AI API request failed (generateAIInsight): ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    let insightString = '';
    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      insightString = data.choices[0].message.content;
    } else {
      console.error('Unexpected AI API response structure for insight:', data);
      throw new Error('Unexpected AI API response structure for insight.');
    }

    try {
      const parsedInsight: InSightData = JSON.parse(insightString);
      // Validate sentiment
      if (!['positive', 'neutral', 'negative'].includes(parsedInsight.sentiment)) {
          parsedInsight.sentiment = 'neutral';
      }
      // Ensure contextTags is an array of strings
      parsedInsight.contextTags = Array.isArray(parsedInsight.contextTags) ? parsedInsight.contextTags.filter(tag => typeof tag === 'string').map(tag => tag.toLowerCase()) : [];
      return parsedInsight;
    } catch (parseError) {
      console.error('Failed to parse AI insight suggestions:', parseError, 'Raw string:', insightString);
      throw new Error('Failed to parse AI insight suggestions.');
    }
  } catch (error) {
    console.error('Error generating AI insight:', error);
    throw error;
  }
}

/**
 * Generates an AI-driven "fast take" (a concise takeaway) for a given post content.
 *
 * @param postTextContent The text content of the post.
 * @returns A promise that resolves to a string containing the fast take.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function generateAIFastTake(
  postTextContent: string
): Promise<string> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for generateAIFastTake.');
    throw new Error('AI API key is missing.');
  }

  // Get current user locale and generate localized prompt
  const locale = await getCurrentLocale();
  const prompt = generateLocalizedPrompt('fastTake', locale, {
    postContent: postTextContent
  });

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.6,
        max_tokens: 100, // Fast take should be short
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error (generateAIFastTake):', errorData);
      throw new Error(`AI API request failed (generateAIFastTake): ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    let fastTakeString = '';
     if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      fastTakeString = data.choices[0].message.content;
    } else {
      console.error('Unexpected AI API response structure for fastTake:', data);
      throw new Error('Unexpected AI API response structure for fastTake.');
    }

    try {
      const parsedResponse: { fastTake: string } = JSON.parse(fastTakeString);
      return parsedResponse.fastTake || ''; // Return empty string if somehow null/undefined
    } catch (parseError) {
      console.error('Failed to parse AI fastTake suggestion:', parseError, 'Raw string:', fastTakeString);
      throw new Error('Failed to parse AI fastTake suggestion.');
    }
  } catch (error) {
    console.error('Error generating AI fast take:', error);
    throw error;
  }
}

/**
 * Extracts bookmarks (websites, tools, platforms) mentioned in a post using AI.
 *
 * @param postTextContent The text content of the post.
 * @returns A promise that resolves to an array of bookmark objects.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function extractAIBookmarks(
  postTextContent: string
): Promise<Array<{name: string; url: string; description: string; category: string}>> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for extractAIBookmarks.');
    throw new Error('AI API key is missing.');
  }

  // Get current user locale and generate localized prompt
  const locale = await getCurrentLocale();
  const prompt = generateLocalizedPrompt('bookmarkExtraction', locale, {
    postContent: postTextContent
  });

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content?.trim();

    if (!content) {
      return [];
    }

    try {
      const bookmarks = JSON.parse(content.replace(/```json\n|\n```/g, '').trim());

      // Validate the response format
      if (!Array.isArray(bookmarks)) {
        return [];
      }

      // Filter and validate each bookmark
      const validBookmarks = bookmarks.filter(bookmark => {
        return bookmark &&
               typeof bookmark.name === 'string' &&
               typeof bookmark.url === 'string' &&
               typeof bookmark.description === 'string' &&
               typeof bookmark.category === 'string' &&
               bookmark.url.startsWith('http') &&
               !bookmark.url.includes('bit.ly') &&
               !bookmark.url.includes('t.co') &&
               !bookmark.url.includes('lnkd.in') &&
               !bookmark.url.includes('twitter.com/') &&
               !bookmark.url.includes('linkedin.com/posts/') &&
               !bookmark.url.includes('instagram.com/p/');
      });

      console.log(`[AI Bookmark Extraction] Extracted ${validBookmarks.length} valid bookmarks from post`);
      return validBookmarks;

    } catch (parseError) {
      console.error('Error parsing bookmark extraction JSON response:', parseError);
      console.log('Raw response:', content);
      return [];
    }

  } catch (error) {
    console.error('Error extracting AI bookmarks:', error);
    throw error;
  }
}

/**
 * Analyzes all posts and groups them by content categories
 *
 * @param posts Array of all posts to analyze
 * @returns A promise that resolves to content-based categories
 */
export async function analyzePostsForContentCategories(
  posts: any[]
): Promise<{
  categories: Record<string, {
    posts: any[];
    summary: string;
    essence: string;
    keyInsights: string[];
    thematicTags: string[];
    patterns: string[];
  }>;
  uncategorized: any[];
}> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key missing for content categorization.');
    throw new Error('AI API key is missing.');
  }

  // Prepare post content for analysis (limit to 50 posts for performance)
  const postsToAnalyze = posts.slice(0, 50).map(post => ({
    id: post.id,
    content: (post.content || post.textContent || '').substring(0, 300),
    author: post.authorName || post.author || '',
    platform: post.platform,
    categories: post.categories || [],
    tags: post.tags || []
  }));

  const prompt = `Analyze these ${postsToAnalyze.length} social media posts and group them into meaningful content categories. Focus on WHAT the content is about, not WHERE it came from.

POSTS TO ANALYZE:
${postsToAnalyze.map((post, index) => `
${index + 1}. [${post.platform}] ${post.content}
   Author: ${post.author}
   Existing categories: ${post.categories.join(', ') || 'None'}
`).join('\n')}

Create 4-7 meaningful content categories that capture the essence of what users are saving. For each category, provide:

Respond with JSON in this exact format:
{
  "categories": {
    "Technology & Innovation": {
      "postIds": ["id1", "id2"],
      "summary": "Brief summary of this category",
      "essence": "Deep insight into why users save this content - what drives them, what they're seeking",
      "keyInsights": ["3-5 meaningful insights about patterns, motivations, trends"],
      "thematicTags": ["5-8 tags that capture the deeper themes"],
      "patterns": ["2-3 behavioral or content patterns you notice"]
    }
  },
  "uncategorizedPostIds": ["id3"]
}

Focus on:
- Content themes, not platforms
- User motivations and interests
- Deeper patterns and insights
- Why people save this type of content
- What it reveals about their interests/goals

Categories should be broad enough to be meaningful but specific enough to be useful.`;

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are an expert content analyst who understands user behavior and content consumption patterns. Always respond with valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from AI API');
    }

    try {
      const parsed = JSON.parse(content);

      // Map the results back to actual posts
      const result: any = {
        categories: {},
        uncategorized: []
      };

      // Create post lookup map
      const postMap = new Map(posts.map(post => [post.id, post]));

      // Process categories
      for (const [categoryName, categoryData] of Object.entries(parsed.categories || {})) {
        const categoryPosts = (categoryData as any).postIds
          ?.map((id: string) => postMap.get(id))
          .filter(Boolean) || [];

        if (categoryPosts.length > 0) {
          result.categories[categoryName] = {
            posts: categoryPosts,
            summary: (categoryData as any).summary || '',
            essence: (categoryData as any).essence || '',
            keyInsights: (categoryData as any).keyInsights || [],
            thematicTags: (categoryData as any).thematicTags || [],
            patterns: (categoryData as any).patterns || []
          };
        }
      }

      // Handle uncategorized posts
      const categorizedPostIds = new Set();
      Object.values(result.categories).forEach((cat: any) => {
        cat.posts.forEach((post: any) => categorizedPostIds.add(post.id));
      });

      result.uncategorized = posts.filter(post => !categorizedPostIds.has(post.id));

      return result;
    } catch (parseError) {
      console.error('Failed to parse AI categorization response:', content);
      // Fallback: create simple categories based on existing data
      return createFallbackCategories(posts);
    }
  } catch (error) {
    console.error('Error in content categorization:', error);
    return createFallbackCategories(posts);
  }
}

/**
 * Generates an AI-driven category overview summarizing posts from a specific platform/category.
 *
 * @param posts Array of posts from the category.
 * @param categoryName The name of the category (e.g., 'X/Twitter', 'LinkedIn', etc.).
 * @returns A promise that resolves to a category overview object.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function generateCategoryOverview(
  posts: any[],
  categoryName: string
): Promise<{
  summary: string;
  essence: string;
  keyInsights: string[];
  thematicTags: string[];
  patterns: string[];
  userMotivations: string[];
  postCount: number;
}> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for generateCategoryOverview.');
    throw new Error('AI API key is missing.');
  }

  // Prepare post content for analysis
  const postContents = posts.slice(0, 20).map(post => ({
    content: post.content || post.textContent || '',
    author: post.authorName || post.author || '',
    timestamp: post.savedAt || post.timestamp || '',
    categories: post.categories || [],
    tags: post.tags || []
  }));

  const prompt = `Analyze these ${posts.length} posts from ${categoryName} and provide deep insights into the essence of this content collection:

POSTS DATA:
${postContents.map((post, index) => `
Post ${index + 1}:
Content: ${post.content.substring(0, 200)}${post.content.length > 200 ? '...' : ''}
Author: ${post.author}
Categories: ${post.categories.join(', ') || 'None'}
Tags: ${post.tags.join(', ') || 'None'}
`).join('\n')}

Provide a JSON response with this structure:
{
  "summary": "2-3 sentences about what this collection represents",
  "essence": "Deep insight into WHY users save this content - what drives them, what they're seeking, what this reveals about their interests and goals",
  "keyInsights": ["4-6 meaningful insights about patterns, motivations, trends, and user behavior"],
  "thematicTags": ["6-10 tags that capture deeper themes and motivations"],
  "patterns": ["2-4 behavioral or content patterns you notice"],
  "userMotivations": ["3-5 reasons why someone would save this type of content"]
}

Focus on:
- The deeper WHY behind saving this content
- User psychology and motivations
- Patterns that reveal interests and goals
- What this content says about the user's aspirations
- Behavioral insights and trends
- The essence and meaning, not just surface topics

Go beyond surface-level categorization to understand the human element.`;

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are an expert content analyst specializing in social media content categorization and insight generation. Always respond with valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from AI API');
    }

    try {
      const parsed = JSON.parse(content);
      return {
        summary: parsed.summary || `Overview of ${posts.length} posts from ${categoryName}`,
        essence: parsed.essence || `This collection represents content that users find valuable and worth saving from ${categoryName}.`,
        keyInsights: parsed.keyInsights || [],
        thematicTags: parsed.thematicTags || [],
        patterns: parsed.patterns || [],
        userMotivations: parsed.userMotivations || [],
        postCount: posts.length
      };
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', content);
      // Fallback response
      return {
        summary: `Collection of ${posts.length} posts from ${categoryName} covering various topics and insights.`,
        essence: `Users are drawn to this content for its relevance to their interests and goals. This collection reflects a curated selection of valuable information and perspectives.`,
        keyInsights: ['Content analysis in progress', 'Multiple topics covered', 'Diverse perspectives shared'],
        thematicTags: ['general', 'social-media', 'content', 'insights'],
        patterns: ['Regular engagement with quality content', 'Selective curation behavior'],
        userMotivations: ['Stay informed', 'Learn new perspectives', 'Save for later reference'],
        postCount: posts.length
      };
    }
  } catch (error) {
    console.error('Error generating category overview:', error);
    // Return fallback data
    return {
      summary: `Collection of ${posts.length} posts from ${categoryName}. Analysis temporarily unavailable.`,
      essence: `This collection represents valuable content that users have chosen to save. Analysis will be available once the service is restored.`,
      keyInsights: ['Content analysis temporarily unavailable'],
      thematicTags: ['general'],
      patterns: ['Content curation behavior'],
      userMotivations: ['Information preservation'],
      postCount: posts.length
    };
  }
}

/**
 * Creates fallback categories when AI analysis fails
 */
function createFallbackCategories(posts: any[]) {
  // Group posts by existing categories or create simple groups
  const categories: Record<string, any> = {};
  const uncategorized: any[] = [];

  posts.forEach(post => {
    if (post.categories && post.categories.length > 0) {
      const category = post.categories[0]; // Use first category
      if (!categories[category]) {
        categories[category] = {
          posts: [],
          summary: `Posts categorized as ${category}`,
          essence: `Content that users find valuable in the ${category} domain`,
          keyInsights: [`Users actively save ${category} content`, 'Indicates interest in this topic area'],
          thematicTags: [category, 'curated-content'],
          patterns: ['Selective content curation']
        };
      }
      categories[category].posts.push(post);
    } else {
      uncategorized.push(post);
    }
  });

  return { categories, uncategorized };
}

/**
 * Generates an AI-driven SnapNote (a concise summary/caption) for a given post content.
 *
 * @param postTextContent The text content of the post.
 * @param imageUrl Optional image URL for visual context.
 * @returns A promise that resolves to a string containing the SnapNote.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function generateAISnapNote(
  postTextContent: string,
  imageUrl?: string
): Promise<string> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for generateAISnapNote.');
    throw new Error('AI API key is missing.');
  }
  console.log('[AI Service] API key found, length:', apiKey.length);

  // Get current user locale and generate localized prompt
  const locale = await getCurrentLocale();
  const imageContext = imageUrl ? 'Note: This post includes an image, so consider visual context in your SnapNote.' : '';
  const prompt = generateLocalizedPrompt('snapNote', locale, {
    postContent: postTextContent,
    imageContext: imageContext
  });

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.5,
        max_tokens: 100,
        response_format: { type: "json_object" }
      }),
    });
    console.log('[AI Service] SnapNote API response status:', response.status, response.statusText);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error (generateAISnapNote):', errorData);
      throw new Error(`AI API request failed (generateAISnapNote): ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    let snapNoteString = '';
    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      snapNoteString = data.choices[0].message.content;
    } else {
      console.error('Unexpected AI API response structure for snapNote:', data);
      throw new Error('Unexpected AI API response structure for snapNote.');
    }

    try {
      const parsedResponse: { snapNote: string } = JSON.parse(snapNoteString);
      return parsedResponse.snapNote || '';
    } catch (parseError) {
      console.error('Failed to parse AI snapNote suggestion:', parseError, 'Raw string:', snapNoteString);
      throw new Error('Failed to parse AI snapNote suggestion.');
    }
  } catch (error) {
    console.error('Error generating AI snapNote:', error);
    throw error;
  }
}

/**
 * Generates AI-driven content ideas based on a given post content.
 *
 * @param postTextContent The text content of the post.
 * @param existingIdeas Optional array of existing ideas to ensure novelty.
 * @returns A promise that resolves to an array of content idea strings.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function generateAIContentIdeas(
  postTextContent: string,
  existingIdeas: string[] = []
): Promise<string[]> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for generateAIContentIdeas.');
    throw new Error('AI API key is missing.');
  }

  // Get current user locale and generate localized prompt
  const locale = await getCurrentLocale();
  const existingIdeasContext = existingIdeas.length > 0 ? `Avoid generating ideas similar to these existing ones: ${JSON.stringify(existingIdeas)}` : '';
  const prompt = generateLocalizedPrompt('contentIdeas', locale, {
    postContent: postTextContent,
    existingIdeasContext: existingIdeasContext
  });

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7, // Higher temperature for more creative ideas
        max_tokens: 200,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error (generateAIContentIdeas):', errorData);
      throw new Error(`AI API request failed (generateAIContentIdeas): ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    let ideasString = '';
    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      ideasString = data.choices[0].message.content;
    } else {
      console.error('Unexpected AI API response structure for contentIdeas:', data);
      throw new Error('Unexpected AI API response structure for contentIdeas.');
    }

    try {
      const parsedResponse: { contentIdeas: string[] } = JSON.parse(ideasString);
      return Array.isArray(parsedResponse.contentIdeas) ? parsedResponse.contentIdeas.filter(idea => typeof idea === 'string') : [];
    } catch (parseError) {
      console.error('Failed to parse AI content ideas:', parseError, 'Raw string:', ideasString);
      throw new Error('Failed to parse AI content ideas.');
    }
  } catch (error) {
    console.error('Error generating AI content ideas:', error);
    throw error;
  }
}

/**
 * Generates content suggestions for repurposing saved posts
 *
 * @param postTextContent The text content of the post.
 * @param platform The original platform of the post.
 * @param actionType The type of content action to generate.
 * @returns A promise that resolves to generated content.
 * @throws If the API key is missing, or if the API call fails.
 */
export async function generateContentSuggestion(
  postTextContent: string,
  platform: string,
  actionType: 'thread' | 'caption' | 'newsletter' | 'quote' | 'poll' | 'story'
): Promise<string> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for generateContentSuggestion.');
    throw new Error('AI API key is missing.');
  }

  const prompt = getContentSuggestionPrompt(postTextContent, platform, actionType);

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are an expert social media content creator and strategist. Generate engaging, platform-appropriate content that maximizes engagement and value.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.8, // Higher creativity for content generation
        max_tokens: 500,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error (generateContentSuggestion):', errorData);
      throw new Error(`AI API request failed (generateContentSuggestion): ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      console.error('No content received from AI API (generateContentSuggestion)');
      throw new Error('No content received from AI API');
    }

    console.log('[AI Service] generateContentSuggestion successful for', actionType);
    return content.trim();

  } catch (error) {
    console.error('Error in generateContentSuggestion:', error);
    throw error;
  }
}

/**
 * Helper function to generate prompts for different content suggestion types
 */
function getContentSuggestionPrompt(content: string, platform: string, actionType: string): string {
  const baseContent = `Original content from ${platform}:\n"${content}"\n\n`;

  switch (actionType) {
    case 'thread':
      return `${baseContent}Transform this content into an engaging Twitter/X thread. Break it into 3-5 tweets, each under 280 characters. Start with a hook, provide value in each tweet, and end with a call-to-action. Use emojis and hashtags appropriately. Format as numbered tweets.`;

    case 'caption':
      return `${baseContent}Create a compelling social media caption based on this content. Keep it under 150 words, include relevant emojis, and end with an engaging question or call-to-action. Make it shareable and platform-appropriate.`;

    case 'newsletter':
      return `${baseContent}Write a newsletter introduction or featured section based on this content. Make it professional yet engaging, around 100-200 words. Include a compelling headline and explain why this insight matters to readers.`;

    case 'quote':
      return `${baseContent}Extract the most impactful and quotable parts from this content. Create 2-3 standalone quotes that are inspiring, insightful, or thought-provoking. Each quote should be under 100 characters and work well with visual quote graphics.`;

    case 'poll':
      return `${baseContent}Create an engaging poll based on this content. Write a compelling question with 2-4 answer options that will generate discussion and engagement. Include context that explains why this poll matters.`;

    case 'story':
      return `${baseContent}Transform this into engaging story content. Create 2-3 story slides with concise, visual-friendly text. Include emojis and make it personal and relatable. Each slide should be under 50 words.`;

    default:
      return `${baseContent}Repurpose this content for social media sharing. Make it engaging and platform-appropriate.`;
  }
}

/**
 * Generates a daily content creation prompt
 */
export async function generateDailyPrompt(): Promise<string> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for generateDailyPrompt.');
    throw new Error('AI API key is missing.');
  }

  const locale = await getCurrentLocale();
  const prompt = generateLocalizedPrompt('dailyPrompt', locale, {});

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are a creative content strategist and writing coach. Generate inspiring, actionable daily prompts for content creators.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.9, // High creativity for varied prompts
        max_tokens: 150,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error (generateDailyPrompt):', errorData);
      throw new Error(`AI API request failed (generateDailyPrompt): ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      console.error('No content received from AI API (generateDailyPrompt)');
      throw new Error('No content received from AI API');
    }

    return content.trim();

  } catch (error) {
    console.error('Error in generateDailyPrompt:', error);
    throw error;
  }
}

/**
 * Suggests the best content format for a given prompt
 */
export async function suggestContentFormat(prompt: string): Promise<{
  format: string;
  direction: string;
  hook: string;
}> {
  const apiKey = await getOpenAIApiKey();
  if (!apiKey) {
    console.error('OpenAI API key is missing for suggestContentFormat.');
    throw new Error('AI API key is missing.');
  }

  const locale = await getCurrentLocale();
  const promptTemplate = generateLocalizedPrompt('contentFormat', locale, { prompt });

  try {
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + apiKey,
      },
      body: JSON.stringify({
        model: AI_MODEL,
        messages: [
          {
            role: 'system',
            content: 'You are an expert content strategist. Analyze prompts and suggest the most effective content format with creative direction.'
          },
          {
            role: 'user',
            content: promptTemplate
          }
        ],
        temperature: 0.7,
        max_tokens: 200,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('AI API Error (suggestContentFormat):', errorData);
      throw new Error(`AI API request failed (suggestContentFormat): ${errorData.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      console.error('No content received from AI API (suggestContentFormat)');
      throw new Error('No content received from AI API');
    }

    const parsedResponse = JSON.parse(content);

    if (!parsedResponse.format || !parsedResponse.direction || !parsedResponse.hook) {
      console.error('Invalid response format from AI API (suggestContentFormat):', parsedResponse);
      throw new Error('Invalid response format from AI API');
    }

    console.log('[AI Service] suggestContentFormat successful');
    return {
      format: parsedResponse.format,
      direction: parsedResponse.direction,
      hook: parsedResponse.hook
    };

  } catch (error) {
    console.error('Error in suggestContentFormat:', error);
    throw error;
  }
}
