/**
 * Bookmark Storage Manager
 * Unified bookmark management
 */

import { unifiedStorage, STORAGE_KEYS } from './unifiedStorageService';
import { Post } from '../types';

export interface BookmarkItem {
  id: string;
  url: string;
  domain: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  favicon?: string;
  post: Post;
  createdAt?: Date;
  updatedAt?: Date;
  isManual?: boolean;
}

export class BookmarkStorageManager {
  private static instance: BookmarkStorageManager;
  
  private constructor() {}
  
  static getInstance(): BookmarkStorageManager {
    if (!BookmarkStorageManager.instance) {
      BookmarkStorageManager.instance = new BookmarkStorageManager();
    }
    return BookmarkStorageManager.instance;
  }

  /**
   * Get all bookmarks (manual + AI-generated)
   */
  async getAllBookmarks(): Promise<BookmarkItem[]> {
    try {
      const manualBookmarksResult = await unifiedStorage.get<BookmarkItem[]>(STORAGE_KEYS.MANUAL_BOOKMARKS);
      const manualBookmarks = manualBookmarksResult || [];

      // Add metadata to manual bookmarks
      const enrichedManualBookmarks = manualBookmarks.map(bookmark => ({
        ...bookmark,
        isManual: true,
        createdAt: bookmark.createdAt || new Date(),
        updatedAt: bookmark.updatedAt || new Date()
      }));

      return enrichedManualBookmarks.sort((a, b) => 
        new Date(b.updatedAt || 0).getTime() - new Date(a.updatedAt || 0).getTime()
      );
    } catch (error) {
      console.error('[BookmarkStorageManager] Error getting all bookmarks:', error);
      return [];
    }
  }

  /**
   * Get bookmarks by category
   */
  async getBookmarksByCategory(category: string): Promise<BookmarkItem[]> {
    try {
      const allBookmarks = await this.getAllBookmarks();
      return allBookmarks.filter(bookmark => bookmark.category === category);
    } catch (error) {
      console.error('[BookmarkStorageManager] Error getting bookmarks by category:', error);
      return [];
    }
  }

  /**
   * Get bookmarks by tags
   */
  async getBookmarksByTags(tags: string[]): Promise<BookmarkItem[]> {
    try {
      const allBookmarks = await this.getAllBookmarks();
      return allBookmarks.filter(bookmark => 
        tags.some(tag => bookmark.tags.includes(tag))
      );
    } catch (error) {
      console.error('[BookmarkStorageManager] Error getting bookmarks by tags:', error);
      return [];
    }
  }

  /**
   * Add a new bookmark
   */
  async addBookmark(bookmark: Omit<BookmarkItem, 'id' | 'createdAt' | 'updatedAt' | 'isManual'>): Promise<BookmarkItem> {
    try {
      const newBookmark: BookmarkItem = {
        ...bookmark,
        id: `bookmark-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date(),
        isManual: true
      };

      const allBookmarks = await this.getAllBookmarks();
      
      // Check for duplicates
      const duplicate = allBookmarks.find(b => b.url === newBookmark.url);
      if (duplicate) {
        throw new Error(`Bookmark with URL "${newBookmark.url}" already exists`);
      }

      const updatedBookmarks = [newBookmark, ...allBookmarks];
      await unifiedStorage.set(STORAGE_KEYS.MANUAL_BOOKMARKS, updatedBookmarks);

      return newBookmark;
    } catch (error) {
      console.error('[BookmarkStorageManager] Error adding bookmark:', error);
      throw error;
    }
  }

  /**
   * Update an existing bookmark
   */
  async updateBookmark(bookmarkId: string, updates: Partial<BookmarkItem>): Promise<void> {
    try {
      const allBookmarks = await this.getAllBookmarks();
      const bookmarkIndex = allBookmarks.findIndex(b => b.id === bookmarkId);
      
      if (bookmarkIndex === -1) {
        throw new Error(`Bookmark with ID "${bookmarkId}" not found`);
      }

      const updatedBookmark = {
        ...allBookmarks[bookmarkIndex],
        ...updates,
        updatedAt: new Date()
      };

      allBookmarks[bookmarkIndex] = updatedBookmark;
      await unifiedStorage.set(STORAGE_KEYS.MANUAL_BOOKMARKS, allBookmarks);
    } catch (error) {
      console.error('[BookmarkStorageManager] Error updating bookmark:', error);
      throw error;
    }
  }

  /**
   * Delete a bookmark
   */
  async deleteBookmark(bookmarkId: string): Promise<void> {
    try {
      const allBookmarks = await this.getAllBookmarks();
      const filteredBookmarks = allBookmarks.filter(b => b.id !== bookmarkId);
      await unifiedStorage.set(STORAGE_KEYS.MANUAL_BOOKMARKS, filteredBookmarks);
    } catch (error) {
      console.error('[BookmarkStorageManager] Error deleting bookmark:', error);
      throw error;
    }
  }

  /**
   * Get processed posts (for AI bookmark extraction)
   */
  async getProcessedPosts(): Promise<Set<string>> {
    try {
      const processedIdsResult = await unifiedStorage.get<string[]>(STORAGE_KEYS.PROCESSED_POSTS);
      const processedIds = processedIdsResult || [];
      return new Set(processedIds);
    } catch (error) {
      console.error('[BookmarkStorageManager] Error getting processed posts:', error);
      return new Set();
    }
  }

  /**
   * Save processed posts
   */
  async saveProcessedPosts(processedIds: Set<string>): Promise<void> {
    try {
      await unifiedStorage.set(STORAGE_KEYS.PROCESSED_POSTS, Array.from(processedIds));
    } catch (error) {
      console.error('[BookmarkStorageManager] Error saving processed posts:', error);
      throw error;
    }
  }

  /**
   * Add AI-generated bookmarks (batch operation)
   */
  async addAIBookmarks(bookmarks: Omit<BookmarkItem, 'id' | 'createdAt' | 'updatedAt' | 'isManual'>[]): Promise<BookmarkItem[]> {
    try {
      const existingBookmarks = await this.getAllBookmarks();
      const existingUrls = new Set(existingBookmarks.map(b => b.url));
      
      const newBookmarks: BookmarkItem[] = [];
      
      for (const bookmark of bookmarks) {
        // Skip duplicates
        if (existingUrls.has(bookmark.url)) {
          continue;
        }

        const newBookmark: BookmarkItem = {
          ...bookmark,
          id: `ai-bookmark-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date(),
          updatedAt: new Date(),
          isManual: false
        };

        newBookmarks.push(newBookmark);
        existingUrls.add(bookmark.url);
      }

      if (newBookmarks.length > 0) {
        const allBookmarks = [...newBookmarks, ...existingBookmarks];
        await unifiedStorage.set(STORAGE_KEYS.MANUAL_BOOKMARKS, allBookmarks);
      }

      return newBookmarks;
    } catch (error) {
      console.error('[BookmarkStorageManager] Error adding AI bookmarks:', error);
      throw error;
    }
  }

  /**
   * Get bookmark statistics
   */
  async getBookmarkStats(): Promise<{
    total: number;
    manual: number;
    ai: number;
    categories: { [category: string]: number };
    tags: { [tag: string]: number };
  }> {
    try {
      const allBookmarks = await this.getAllBookmarks();
      
      const stats = {
        total: allBookmarks.length,
        manual: allBookmarks.filter(b => b.isManual).length,
        ai: allBookmarks.filter(b => !b.isManual).length,
        categories: {} as { [category: string]: number },
        tags: {} as { [tag: string]: number }
      };

      // Count by categories
      allBookmarks.forEach(bookmark => {
        stats.categories[bookmark.category] = (stats.categories[bookmark.category] || 0) + 1;
      });

      // Count by tags
      allBookmarks.forEach(bookmark => {
        bookmark.tags.forEach(tag => {
          stats.tags[tag] = (stats.tags[tag] || 0) + 1;
        });
      });

      return stats;
    } catch (error) {
      console.error('[BookmarkStorageManager] Error getting bookmark stats:', error);
      return { total: 0, manual: 0, ai: 0, categories: {}, tags: {} };
    }
  }

  /**
   * Search bookmarks
   */
  async searchBookmarks(query: string): Promise<BookmarkItem[]> {
    try {
      const allBookmarks = await this.getAllBookmarks();
      const lowercaseQuery = query.toLowerCase();
      
      return allBookmarks.filter(bookmark => 
        bookmark.title.toLowerCase().includes(lowercaseQuery) ||
        bookmark.description.toLowerCase().includes(lowercaseQuery) ||
        bookmark.domain.toLowerCase().includes(lowercaseQuery) ||
        bookmark.category.toLowerCase().includes(lowercaseQuery) ||
        bookmark.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
      );
    } catch (error) {
      console.error('[BookmarkStorageManager] Error searching bookmarks:', error);
      return [];
    }
  }

  /**
   * Export bookmarks
   */
  async exportBookmarks(): Promise<string> {
    try {
      const allBookmarks = await this.getAllBookmarks();
      return JSON.stringify(allBookmarks, null, 2);
    } catch (error) {
      console.error('[BookmarkStorageManager] Error exporting bookmarks:', error);
      throw error;
    }
  }

  /**
   * Import bookmarks
   */
  async importBookmarks(bookmarksJson: string): Promise<number> {
    try {
      const importedBookmarks = JSON.parse(bookmarksJson) as BookmarkItem[];
      const existingBookmarks = await this.getAllBookmarks();
      const existingUrls = new Set(existingBookmarks.map(b => b.url));
      
      const newBookmarks = importedBookmarks.filter(bookmark => 
        !existingUrls.has(bookmark.url)
      ).map(bookmark => ({
        ...bookmark,
        id: `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      if (newBookmarks.length > 0) {
        const allBookmarks = [...newBookmarks, ...existingBookmarks];
        await unifiedStorage.set(STORAGE_KEYS.MANUAL_BOOKMARKS, allBookmarks);
      }

      return newBookmarks.length;
    } catch (error) {
      console.error('[BookmarkStorageManager] Error importing bookmarks:', error);
      throw error;
    }
  }

  /**
   * Subscribe to bookmark changes
   */
  subscribeToChanges(callback: (bookmarks: BookmarkItem[]) => void): () => void {
    return unifiedStorage.subscribe(STORAGE_KEYS.MANUAL_BOOKMARKS, async () => {
      const bookmarks = await this.getAllBookmarks();
      callback(bookmarks);
    });
  }

  /**
   * Clear all bookmarks
   */
  async clearAllBookmarks(): Promise<void> {
    try {
      await unifiedStorage.set(STORAGE_KEYS.MANUAL_BOOKMARKS, []);
      await unifiedStorage.set(STORAGE_KEYS.PROCESSED_POSTS, []);
    } catch (error) {
      console.error('[BookmarkStorageManager] Error clearing bookmarks:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const bookmarkStorage = BookmarkStorageManager.getInstance();
