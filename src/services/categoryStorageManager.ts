/**
 * Category Storage Manager
 * Unified category management across all components
 */

import { unifiedStorage, STORAGE_KEYS } from './unifiedStorageService';

export interface Category {
  id: string;
  name: string;
  emoji?: string;
  tags?: string[];
  sortOrder?: number;
  postCount?: number;
  isUserCreated?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface BookmarkCategory {
  name: string;
  emoji: string;
  tags: string[];
}

export class CategoryStorageManager {
  private static instance: CategoryStorageManager;
  
  private constructor() {}
  
  static getInstance(): CategoryStorageManager {
    if (!CategoryStorageManager.instance) {
      CategoryStorageManager.instance = new CategoryStorageManager();
    }
    return CategoryStorageManager.instance;
  }

  /**
   * Get all user categories (unified from all sources)
   */
  async getAllCategories(): Promise<Category[]> {
    try {
      // Get categories from different sources
      const [userCategoriesResult, bookmarkCategoriesResult] = await Promise.all([
        unifiedStorage.get<string[]>(STORAGE_KEYS.USER_CATEGORIES),
        unifiedStorage.get<BookmarkCategory[]>(STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES)
      ]);

      const userCategories = userCategoriesResult || [];
      const bookmarkCategories = bookmarkCategoriesResult || [];

      const categories: Category[] = [];

      // Add user categories (from main storage)
      userCategories.forEach((name, index) => {
        categories.push({
          id: `user-${name.toLowerCase().replace(/\s+/g, '-')}`,
          name,
          sortOrder: index,
          isUserCreated: true,
          postCount: 0
        });
      });

      // Add bookmark categories (convert to unified format)
      bookmarkCategories.forEach((bookmarkCat, index) => {
        const id = `bookmark-${bookmarkCat.name.toLowerCase().replace(/\s+/g, '-')}`;
        
        // Check if already exists from user categories
        const existingIndex = categories.findIndex(cat => 
          cat.name.toLowerCase() === bookmarkCat.name.toLowerCase()
        );

        if (existingIndex >= 0) {
          // Merge with existing category
          categories[existingIndex] = {
            ...categories[existingIndex],
            emoji: bookmarkCat.emoji,
            tags: [...(categories[existingIndex].tags || []), ...bookmarkCat.tags]
          };
        } else {
          // Add as new category
          categories.push({
            id,
            name: bookmarkCat.name,
            emoji: bookmarkCat.emoji,
            tags: bookmarkCat.tags,
            sortOrder: userCategories.length + index,
            isUserCreated: true,
            postCount: 0
          });
        }
      });

      return categories.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    } catch (error) {
      console.error('[CategoryStorageManager] Error getting all categories:', error);
      return [];
    }
  }

  /**
   * Add a new category (unified across all systems)
   */
  async addCategory(category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category> {
    try {
      const existingCategories = await this.getAllCategories();
      
      // Check for duplicates
      const duplicate = existingCategories.find(cat => 
        cat.name.toLowerCase() === category.name.toLowerCase()
      );
      
      if (duplicate) {
        throw new Error(`Category "${category.name}" already exists`);
      }

      const newCategory: Category = {
        ...category,
        id: `user-${Date.now()}-${category.name.toLowerCase().replace(/\s+/g, '-')}`,
        createdAt: new Date(),
        updatedAt: new Date(),
        isUserCreated: true,
        sortOrder: existingCategories.length
      };

      // Save to both storage systems for compatibility
      await Promise.all([
        this.saveToUserCategories(newCategory),
        this.saveToBookmarkCategories(newCategory)
      ]);

      return newCategory;
    } catch (error) {
      console.error('[CategoryStorageManager] Error adding category:', error);
      throw error;
    }
  }

  /**
   * Update an existing category
   */
  async updateCategory(categoryId: string, updates: Partial<Category>): Promise<void> {
    try {
      const categories = await this.getAllCategories();
      const categoryIndex = categories.findIndex(cat => cat.id === categoryId);
      
      if (categoryIndex === -1) {
        throw new Error(`Category with ID "${categoryId}" not found`);
      }

      const updatedCategory = {
        ...categories[categoryIndex],
        ...updates,
        updatedAt: new Date()
      };

      categories[categoryIndex] = updatedCategory;

      // Save to both storage systems
      await Promise.all([
        this.saveAllToUserCategories(categories),
        this.saveAllToBookmarkCategories(categories)
      ]);
    } catch (error) {
      console.error('[CategoryStorageManager] Error updating category:', error);
      throw error;
    }
  }

  /**
   * Delete a category
   */
  async deleteCategory(categoryId: string): Promise<void> {
    try {
      const categories = await this.getAllCategories();
      const filteredCategories = categories.filter(cat => cat.id !== categoryId);

      // Save to both storage systems
      await Promise.all([
        this.saveAllToUserCategories(filteredCategories),
        this.saveAllToBookmarkCategories(filteredCategories)
      ]);
    } catch (error) {
      console.error('[CategoryStorageManager] Error deleting category:', error);
      throw error;
    }
  }

  /**
   * Get bookmark-specific categories (for backward compatibility)
   */
  async getBookmarkCategories(): Promise<BookmarkCategory[]> {
    const categories = await this.getAllCategories();
    return categories
      .filter(cat => cat.emoji && cat.tags)
      .map(cat => ({
        name: cat.name,
        emoji: cat.emoji!,
        tags: cat.tags || []
      }));
  }

  /**
   * Subscribe to category changes
   */
  subscribeToChanges(callback: (categories: Category[]) => void): () => void {
    const unsubscribers = [
      unifiedStorage.subscribe(STORAGE_KEYS.USER_CATEGORIES, async () => {
        const categories = await this.getAllCategories();
        callback(categories);
      }),
      unifiedStorage.subscribe(STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES, async () => {
        const categories = await this.getAllCategories();
        callback(categories);
      })
    ];

    return () => {
      unsubscribers.forEach(unsub => unsub());
    };
  }

  /**
   * Migrate existing category data to unified format
   */
  async migrateExistingData(): Promise<void> {
    try {
      // Migrate from localStorage (category chat data)
      const localStorageKeys = Object.keys(localStorage).filter(key =>
        key.startsWith('category-chat-')
      );

      for (const key of localStorageKeys) {
        const newKey = `${STORAGE_KEYS.CATEGORY_CHAT_PREFIX}${key.replace('category-chat-', '')}`;
        await unifiedStorage.migrateFromLocalStorage(key, newKey);
      }

      // Migrate from chrome.storage.sync if needed
      await unifiedStorage.migrateFromSync('userCategories', STORAGE_KEYS.USER_CATEGORIES);
    } catch (error) {
      console.error('[CategoryStorageManager] Error during migration:', error);
    }
  }

  // Private helper methods
  private async saveToUserCategories(category: Category): Promise<void> {
    const userCategoriesResult = await unifiedStorage.get<string[]>(STORAGE_KEYS.USER_CATEGORIES);
    const userCategories = userCategoriesResult || [];
    if (!userCategories.includes(category.name)) {
      userCategories.push(category.name);
      await unifiedStorage.set(STORAGE_KEYS.USER_CATEGORIES, userCategories);
    }
  }

  private async saveToBookmarkCategories(category: Category): Promise<void> {
    if (category.emoji && category.tags) {
      const bookmarkCategoriesResult = await unifiedStorage.get<BookmarkCategory[]>(STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES);
      const bookmarkCategories = bookmarkCategoriesResult || [];
      const existingIndex = bookmarkCategories.findIndex(cat => cat.name === category.name);
      
      const bookmarkCategory: BookmarkCategory = {
        name: category.name,
        emoji: category.emoji,
        tags: category.tags
      };

      if (existingIndex >= 0) {
        bookmarkCategories[existingIndex] = bookmarkCategory;
      } else {
        bookmarkCategories.push(bookmarkCategory);
      }

      await unifiedStorage.set(STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES, bookmarkCategories);
    }
  }

  private async saveAllToUserCategories(categories: Category[]): Promise<void> {
    const userCategoryNames = categories.map(cat => cat.name);
    await unifiedStorage.set(STORAGE_KEYS.USER_CATEGORIES, userCategoryNames);
  }

  private async saveAllToBookmarkCategories(categories: Category[]): Promise<void> {
    const bookmarkCategories = categories
      .filter(cat => cat.emoji && cat.tags)
      .map(cat => ({
        name: cat.name,
        emoji: cat.emoji!,
        tags: cat.tags || []
      }));
    
    await unifiedStorage.set(STORAGE_KEYS.CUSTOM_BOOKMARK_CATEGORIES, bookmarkCategories);
  }
}

// Export singleton instance
export const categoryStorage = CategoryStorageManager.getInstance();
