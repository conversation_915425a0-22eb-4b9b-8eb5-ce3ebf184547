/**
 * Analytics Service for tracking upgrade flow events
 * 
 * This service provides analytics tracking for the premium upgrade flow.
 * It can be extended to integrate with various analytics providers.
 */

export interface AnalyticsEvent {
  event: string;
  properties: Record<string, any>;
  timestamp: number;
}

/**
 * Analytics provider interface
 */
export interface AnalyticsProvider {
  track(event: string, properties: Record<string, any>): void;
}

/**
 * Console analytics provider for development/debugging
 */
class ConsoleAnalyticsProvider implements AnalyticsProvider {
  track(event: string, properties: Record<string, any>): void {
    console.log(`[Analytics] ${event}:`, properties);
  }
}

/**
 * Local storage analytics provider for basic tracking
 */
class LocalStorageAnalyticsProvider implements AnalyticsProvider {
  private readonly storageKey = 'notely_analytics_events';
  private readonly maxEvents = 100;

  track(event: string, properties: Record<string, any>): void {
    try {
      const analyticsEvent: AnalyticsEvent = {
        event,
        properties,
        timestamp: Date.now(),
      };

      // Get existing events
      const existingEvents = this.getStoredEvents();
      
      // Add new event
      existingEvents.push(analyticsEvent);
      
      // Keep only the most recent events
      const recentEvents = existingEvents.slice(-this.maxEvents);
      
      // Store back
      localStorage.setItem(this.storageKey, JSON.stringify(recentEvents));
      
      // Also log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Analytics] ${event}:`, properties);
      }
    } catch (error) {
      console.warn('Failed to store analytics event:', error);
    }
  }

  private getStoredEvents(): AnalyticsEvent[] {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Failed to retrieve stored analytics events:', error);
      return [];
    }
  }

  /**
   * Get all stored analytics events
   */
  getEvents(): AnalyticsEvent[] {
    return this.getStoredEvents();
  }

  /**
   * Clear all stored analytics events
   */
  clearEvents(): void {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('Failed to clear analytics events:', error);
    }
  }
}

/**
 * Chrome extension analytics provider
 */
class ChromeExtensionAnalyticsProvider implements AnalyticsProvider {
  track(event: string, properties: Record<string, any>): void {
    try {
      // Store in Chrome storage
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const analyticsEvent: AnalyticsEvent = {
          event,
          properties,
          timestamp: Date.now(),
        };

        chrome.storage.local.get(['analytics_events'], (result) => {
          const events: AnalyticsEvent[] = result.analytics_events || [];
          events.push(analyticsEvent);
          
          // Keep only the most recent 100 events
          const recentEvents = events.slice(-100);
          
          chrome.storage.local.set({ analytics_events: recentEvents });
        });
      }
      
      // Also log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Analytics] ${event}:`, properties);
      }
    } catch (error) {
      console.warn('Failed to track analytics event in Chrome extension:', error);
    }
  }
}

/**
 * Analytics service class
 */
class AnalyticsService {
  private providers: AnalyticsProvider[] = [];

  constructor() {
    // Initialize with appropriate providers based on environment
    this.initializeProviders();
  }

  private initializeProviders(): void {
    // Always add console provider in development
    if (process.env.NODE_ENV === 'development') {
      this.providers.push(new ConsoleAnalyticsProvider());
    }

    // Add Chrome extension provider if available
    if (typeof chrome !== 'undefined' && chrome.storage) {
      this.providers.push(new ChromeExtensionAnalyticsProvider());
    } else {
      // Fallback to localStorage provider for web environments
      this.providers.push(new LocalStorageAnalyticsProvider());
    }
  }

  /**
   * Track an analytics event
   */
  track(event: string, properties: Record<string, any> = {}): void {
    // Add common properties
    const enrichedProperties = {
      ...properties,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // Send to all providers
    this.providers.forEach(provider => {
      try {
        provider.track(event, enrichedProperties);
      } catch (error) {
        console.warn('Analytics provider failed:', error);
      }
    });
  }

  /**
   * Track upgrade flow opened event
   */
  trackUpgradeFlowOpened(source: string): void {
    this.track('upgrade_flow_opened', { source });
  }

  /**
   * Track upgrade plan selected event
   */
  trackUpgradePlanSelected(source: string, plan: string): void {
    this.track('upgrade_plan_selected', { source, plan });
  }

  /**
   * Track checkout started event
   */
  trackCheckoutStarted(source: string, plan: string): void {
    this.track('checkout_started', { source, plan });
  }

  /**
   * Track billing portal opened event
   */
  trackBillingPortalOpened(source: string): void {
    this.track('billing_portal_opened', { source });
  }

  /**
   * Add a custom analytics provider
   */
  addProvider(provider: AnalyticsProvider): void {
    this.providers.push(provider);
  }

  /**
   * Remove all providers (useful for testing)
   */
  clearProviders(): void {
    this.providers = [];
  }
}

// Create singleton instance
const analyticsService = new AnalyticsService();

export default analyticsService;

// Export types and classes for advanced usage
export {
  AnalyticsService,
  ConsoleAnalyticsProvider,
  LocalStorageAnalyticsProvider,
  ChromeExtensionAnalyticsProvider,
};
