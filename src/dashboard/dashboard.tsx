import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import '../styles/platformIcons.css';
import ReactDOM from 'react-dom/client';
import '../index.css'; // Import Tailwind CSS
import '../styles/notely-theme.css'; // Import Notely theme
import '../styles/improved-typography.css'; // Import improved typography
import '../styles/dramatic-typography.css'; // Import dramatic typography changes
import {
  getSavedPosts,
  getAllCategories,
  updatePostDetails // <-- Import (replaces updatePostCategory)
} from '../storage';
import { Post, Platform, IUserFrontend } from '../types';
import LoginModal from '../components/LoginModal'; // Import the modal
import DailyWisdom from '../components/DailyWisdom';
import StorageUsage from '../components/StorageUsage';
import MindstreamSidebar from '../components/MindstreamSidebar';
import MindstreamWidgets from '../components/MindstreamWidgets';
import ThreadCard from '../components/ThreadCard';
import { PremiumBadge } from '../components/PremiumBadge';

import SimpleCategorySelector from '../components/SimpleCategorySelector';

import CategorySummary from '../components/CategorySummary';
import CategoryChatWithPosts from '../components/CategoryChatWithPosts';
import ThemeToggle from '../components/ThemeToggle';
import { useTranslation } from '../hooks/useTranslation';
import { downloadPostCardAsImage, copyPostCardAsImage } from '../utils/postCardCapture';
import { toast } from '../utils/toast';
import { initializeTheme } from '../utils/themeUtils';
import { formatForDisplay, convertWisdomQuoteToPost } from '../utils/formatUtils';
import { migrationService } from '../services/migrationService';
import { syncUnifiedDataFromCloud } from '../services/cloudSyncService';
import { performSemanticSearch, debounce } from '../services/semanticSearchService';
// import domtoimage from 'dom-to-image-more'; // Remove import
// import html2canvas from 'html2canvas'; // Remove import

// --- NEW IMPORTS ---
import PostViewerFullScreen, { PostWithAIData } from '../components/PostViewerFullScreen';
import ProxyImage from '../components/ProxyImage'; // Import ProxyImage component for handling CORS issues
import { ImageSwiper } from '../components/ui/image-swiper'; // Import ImageSwiper component for multi-image posts
import { getImage } from '../utils/imageUtils'; // Import the unified image retrieval function
import TagsCategoriesWidget from '../components/TagsCategoriesWidget';
import { LocaleProvider } from '../contexts/LocaleProvider';
import { useAuth } from '../web/context/WebAuthContext';
// --- END NEW IMPORTS ---

// Theme initialization is handled by components to avoid conflicts

// Define API_URL if not already present or ensure it's correctly defined
const API_URL = 'https://api.notely.social';

// --- Placeholder Components ---

// --- NEW: Confirmation Modal Component ---
interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText,
  cancelText,
}) => {
  const { t } = useTranslation();
  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
      onClick={handleOverlayClick}
    >
      <div className="notely-card bg-notely-card border border-[#2F2F2F] rounded-notely-xl shadow-notely-md hover:shadow-notely-lg p-6 w-full max-w-md mx-auto" onClick={(e) => e.stopPropagation()}>
        <h3 className="text-lg font-semibold mb-2 text-notely-text-primary">{title}</h3>
        <p className="text-sm text-notely-text-muted mb-6">{message}</p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="px-4 py-2 rounded-md text-sm font-medium text-notely-text-muted bg-notely-surface hover:bg-notely-border focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-notely-border transition-all duration-200 ease-in-out hover:scale-105 active:scale-95"
          >
            {cancelText || 'Cancel'}
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onConfirm();
            }}
            className="px-4 py-2 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 ease-in-out hover:scale-105 active:scale-95"
          >
            {confirmText || t('post.delete')}
          </button>
        </div>
      </div>
    </div>
  );
};
// --- END: Confirmation Modal Component ---

interface ThreadCardProps {
  threadPosts: Post[];
  firstPost: Post;
  onDelete: (postId: string) => void;
  onOpenDetails: () => void;
  t: (key: string) => string;
}

interface PostCardProps {
  post: Post;
  onDelete: (postId: string) => void;
  onOpenDetails: (postId: string) => void; // Prop to handle opening the detail view
  isForCapture?: boolean; // Add prop to indicate capture mode
  t: (key: string) => string; // Translation function
}

// Helper to format timestamp (can be expanded)
const formatTimestamp = (isoString: string | undefined | null): string => {
  // 1. Check if input string is provided
  if (!isoString) {
    return "Unknown date";
  }

  // 2. Try parsing the date
  const date = new Date(isoString);

  // 3. Check if the parsed date is valid
  if (isNaN(date.getTime())) {
    console.warn("Invalid date string received by formatTimestamp:", isoString);
    return "Invalid date"; // Return a fallback string for invalid dates
  }

  // 4. Calculate the difference in days (ensure finite result)
  const diffInMs = date.getTime() - Date.now();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  // 5. Format only if diffInDays is a finite number
  if (!isFinite(diffInDays)) {
      console.warn("Could not calculate finite date difference for:", isoString);
      return "Some time ago"; // Fallback for non-finite diff
  }

  // 6. Proceed with formatting
  try {
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(diffInDays, 'day');
  } catch (error) {
      console.error("Error formatting relative time:", error, "Input:", isoString, "Calculated days:", diffInDays);
      return "Error formatting date"; // Fallback on formatting error
  }
};

// Format number for display (e.g., 1500 -> 1.5K)
const formatNumber = (num: number | undefined): string => {
  if (num === undefined) return '0';
  if (num === 0) return '0';
  if (num < 1000) return num.toString();
  return (num / 1000).toFixed(1) + 'K';
};

// Helper function to get engagement metrics from either stats or interactions
const getEngagementMetrics = (post: Post) => {
  const stats = post.stats;
  const interactions = (post as any).interactions;

  return {
    comments: stats?.comments ?? interactions?.replies ?? 0,
    shares: stats?.shares ?? interactions?.reposts ?? 0,
    likes: stats?.likes ?? interactions?.likes ?? 0,
    views: stats?.views ?? 0
  };
};

// Image Modal Component for full-screen image viewing
interface ImageModalProps {
  imageUrl?: string;
  images?: string[];
  initialIndex?: number;
  alt?: string;
  onClose: () => void;
}

const ImageModal: React.FC<ImageModalProps> = ({ imageUrl, images, initialIndex = 0, alt, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Determine if we're showing multiple images or single image
  const isMultiImage = images && images.length > 1;
  const displayImages = images && images.length > 0 ? images : (imageUrl ? [imageUrl] : []);
  const [currentImageIndex, setCurrentImageIndex] = useState(initialIndex);

  // Check if URL is a base64 data URL
  const isBase64Image = (url: string | null | undefined): boolean => {
    return !!url && url.startsWith('data:image/');
  };

  // Close modal when clicking outside the image
  const handleClickOutside = (e: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (isMultiImage) {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        setCurrentImageIndex(prev => prev > 0 ? prev - 1 : displayImages.length - 1);
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        setCurrentImageIndex(prev => prev < displayImages.length - 1 ? prev + 1 : 0);
      }
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    // Prevent scrolling when modal is open
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto';
    };
  }, []);

  if (displayImages.length === 0) {
    return null;
  }

  const currentImageUrl = displayImages[currentImageIndex];

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="relative max-w-full max-h-full" ref={modalRef}>
        {isMultiImage ? (
          // Use ImageSwiper for multiple images
          <div className="max-w-full max-h-[90vh]">
            <ImageSwiper
              images={displayImages}
              className="max-w-full max-h-full"
              aspectRatio="auto"
              showControls={true}
              showDots={true}
              imageClassName="max-w-full max-h-[90vh] object-contain"
            />
          </div>
        ) : (
          // Single image display
          <>
            {isBase64Image(currentImageUrl) ? (
              // Directly render base64 image in modal
              <img
                src={currentImageUrl}
                alt={alt || 'Full size image'}
                className="max-w-full max-h-[90vh] object-contain rounded-lg"
                onError={(e) => {
                  console.error(`[ImageModal] Base64 image failed to load`);
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : isInstagramUrl(currentImageUrl) ? (
              // Use ProxyImage for Instagram URLs that aren't base64
              <ProxyImage
                src={currentImageUrl}
                alt={alt || 'Full size image'}
                className="max-w-full max-h-[90vh] object-contain rounded-lg"
                fallbackSrc={currentImageUrl}
                postId={currentPost?.id || 'unknown'}

              />
            ) : (
              // Regular image for other platforms
              <img
                src={currentImageUrl}
                alt={alt || 'Full size image'}
                className="max-w-full max-h-[90vh] object-contain rounded-lg"
              />
            )}
          </>
        )}

        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95 z-10"
          aria-label="Close image modal"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};



const PostCard: React.FC<PostCardProps> = ({ post, onDelete, onOpenDetails, isForCapture, t }) => {
  const currentPlatform: Platform = post.platform; // Explicitly type the platform
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
  const [isExpanded, setIsExpanded] = useState(false);
  const [imageDataUrls, setImageDataUrls] = useState<Record<string, string>>({});


  // Removed confirmation modal state as it's handled at the Dashboard level



  // Visual truncation is now handled purely by CSS line-clamp
  // We only need expansion state for very long posts (more than ~500 chars)
  const EXPANSION_THRESHOLD = 500; // Much higher threshold for expansion button
  const postContentLength = post.content?.length || 0;
  const needsExpansionButton = postContentLength > EXPANSION_THRESHOLD;
  // End of truncation definitions

  // --- Helper functions for image handling ---
  // Check if URL needs special handling for Instagram/Facebook images
  const needsFetching = (url: string | null | undefined): boolean => {
    // Skip fetching if it's a base64 image or if we don't have a URL
    if (!url || url.startsWith('data:image/')) {
      return false;
    }

    // For Instagram avatar URLs, we want to use them directly without fetching
    // Only fetch Instagram/Facebook post image URLs
    if (post.platform === 'Instagram' && url === post.authorAvatar) {
      return false;
    }

    // Only fetch Instagram/Facebook URLs for other cases
    return url.includes('fbcdn.net') || url.includes('instagram');
  };

  // Check if URL is specifically from Instagram
  const isInstagramUrl = (url: string | null | undefined): boolean => {
    return !!url && (url.includes('instagram') || url.includes('fbcdn.net'));
  };

  // Check if URL is a base64 data URL
  const isBase64Image = (url: string | null | undefined): boolean => {
    return !!url && url.startsWith('data:image/');
  };
  // --- End helper functions ---

  // Handle Toggle Expansion
  const toggleExpansion = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    setIsExpanded(!isExpanded);
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await onDelete(post.id);
    } catch (error) {
      console.error('Failed to delete post:', error);
    }
    return false; // Explicitly return false to be absolutely sure
  };

  // useEffect to load images using our unified approach
  useEffect(() => {
    // Skip all fetching if we already have base64 images
    const hasBase64Avatar = post.authorAvatar && post.authorAvatar.startsWith('data:image/');
    const hasBase64MainImage = post.savedImage && post.savedImage.startsWith('data:image/');
    const hasBase64MediaImage = post.media && post.media.length > 0 &&
                               post.media[0].url && post.media[0].url.startsWith('data:image/');

    // If we already have base64 images for both avatar and main image, skip fetching
    if (hasBase64Avatar && (hasBase64MainImage || hasBase64MediaImage)) {
      return;
    }

    const loadImages = async () => {
      try {
        // For Instagram posts, we need special handling
        if (post.platform === 'Instagram') {

          const loadInstagramImages = async () => {
            try {
              const db = await new Promise<IDBDatabase>((resolve, reject) => {
                const request = indexedDB.open('social-saver-images', 2);
                request.onerror = (event) => reject('Error opening IndexedDB');
                request.onsuccess = (event) => resolve((event.target as IDBOpenDBRequest).result);
              });
  
              if (!db.objectStoreNames.contains('images')) {
                console.warn('[PostCard] No images object store found.');
                db.close();
                return;
              }
  
              const transaction = db.transaction(['images'], 'readonly');
              const store = transaction.objectStore('images');
              const index = store.index('postId'); // Assuming 'postId' index exists
              const getAllRequest = index.getAll(post.id);
  
              getAllRequest.onsuccess = async () => {
                const images = getAllRequest.result;
                if (images && images.length > 0) {
                  const processedImages = await Promise.all(images.map(async (img: any) => {
                    if (img.blob) {
                      return new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onloadend = () => resolve({ ...img, dataUrl: reader.result as string });
                        reader.onerror = () => resolve({ ...img, dataUrl: null });
                        reader.readAsDataURL(img.blob);
                      });
                    }
                    return { ...img, dataUrl: null };
                  }));
  
                  const validImages = processedImages.filter(img => img.dataUrl);
                  
                  const mainImage = validImages.find(img => !img.url.includes('profile') && !img.id.includes('profile'));
                  if (mainImage) {
                    setImageDataUrls(prev => ({ ...prev, [post.media?.[0]?.url || 'main']: mainImage.dataUrl }));
                  }
  
                  const avatarImage = validImages.find(img => img.url.includes('profile') || img.id.includes('profile'));
                  if (avatarImage && post.authorAvatar) {
                    setImageDataUrls(prev => ({ ...prev, [post.authorAvatar || 'avatar']: avatarImage.dataUrl }));
                  }
                }
                db.close();
              };
  
              getAllRequest.onerror = (event) => {
                console.error('[PostCard] Error fetching images from IndexedDB:', event);
                db.close();
              };
            } catch (error) {
              console.error('[PostCard] Error loading Instagram images from IndexedDB:', error);
            }
          };

          loadInstagramImages();
          return; // Return after starting async operation
        }

        // Regular loading process for non-Instagram posts or if Instagram special handling failed

        // Load avatar image if needed - only if it's not already a direct URL
        if (!hasBase64Avatar && needsFetching(post.authorAvatar)) {
          const avatarUrl = post.authorAvatar;
          if (avatarUrl && !imageDataUrls[avatarUrl]) {
            const avatarData = await getImage(post.id, avatarUrl);
            if (avatarData) {
              setImageDataUrls(prev => ({ ...prev, [avatarUrl]: avatarData }));
            }
          }
        }

        // Load main image if needed
        const mainImageUrl = post.savedImage || (post.media && post.media.length > 0 ? post.media[0].url : null);
        if (!hasBase64MainImage && !hasBase64MediaImage && needsFetching(mainImageUrl)) {
          if (mainImageUrl && !imageDataUrls[mainImageUrl]) {
            const mainImageData = await getImage(post.id, mainImageUrl);
            if (mainImageData) {
              setImageDataUrls(prev => ({ ...prev, [mainImageUrl]: mainImageData }));
            }
          }
        }
      } catch (error) {
        console.error('[PostCard] Error loading images:', error);
      }
    };

    loadImages();
  }, [post.id, post.authorAvatar, post.savedImage, post.media, post.platform, imageDataUrls]);

  // --- Determine image sources conditionally ---
  // Select the correct avatar URL based on what's available
  const sourceAvatarUrl = post.authorAvatar || post.authorImage; // Prefer authorAvatar, fallback to authorImage

  const needsFetchingAvatar = needsFetching(sourceAvatarUrl);
  const avatarSrc = needsFetchingAvatar
    ? imageDataUrls[sourceAvatarUrl!] // Use data URL if fetched
    : sourceAvatarUrl; // Use original URL directly otherwise

  // --- Prepare media data for single or multi-media display ---
  const hasMultipleMedia = post.media && post.media.length > 1;
  const hasVideo = post.media && post.media.some(item => item.type === 'video');

  // For multi-media posts, prepare all media URLs and types
  const allMediaItems = hasMultipleMedia
    ? post.media!.map((mediaItem, index) => {
        const needsFetchingMedia = mediaItem.type === 'image' ? needsFetching(mediaItem.url) : false;
        const finalUrl = needsFetchingMedia
          ? imageDataUrls[mediaItem.url] || mediaItem.url // Use data URL if available, fallback to original
          : mediaItem.url; // Use original URL directly



        return {
          ...mediaItem,
          url: finalUrl
        };
      }).filter(item => item.url && item.url.trim() !== '') // Remove any null/undefined/empty URLs
    : [];

  // For legacy compatibility, extract image URLs for existing ImageSwiper
  const allImageUrls = allMediaItems
    .filter(item => item.type === 'image')
    .map(item => item.url);

  // For single image posts, use existing logic
  const mainImageUrl = post.savedImage || (post.media && post.media.length > 0 ? post.media[0].url : null);
  const needsFetchingMainImage = needsFetching(mainImageUrl);
  const mainImageSrc = needsFetchingMainImage
    ? imageDataUrls[mainImageUrl!] // Use data URL if fetched
    : mainImageUrl; // Use original URL directly otherwise

  const mainImageAlt = (post.media && post.media.length > 0 ? post.media[0].alt : '') || post.altText || '';

  // Determine if we should show the swiper or single media
  const shouldUseSwiper = hasMultipleMedia && allMediaItems.length > 1;
  const finalImageUrls = shouldUseSwiper ? allImageUrls : (mainImageSrc ? [mainImageSrc] : []);
  
  // Get the first video if available
  const firstVideo = post.media && post.media.find(item => item.type === 'video');
  const shouldShowVideo = hasVideo && !shouldUseSwiper; // Show video only if single video post

  // Determine if placeholders should be shown
  // const showAvatarPlaceholder = !avatarSrc; // Unused, removing
  // const showMainImagePlaceholder = !!mainImageUrl && !mainImageSrc; // Unused, removing

  // --- Handle Card Click ---
  const handleCardClick = (e: React.MouseEvent) => {
    if (!isForCapture) { // Only allow opening details if not in capture mode
      // Add brief scale animation to the clicked card
      const target = e.currentTarget as HTMLElement;
      target.style.transform = 'scale(1.02)';
      target.style.transition = 'transform 0.15s ease-out';

      setTimeout(() => {
        target.style.transform = 'scale(1)';
        setTimeout(() => {
          target.style.transform = '';
          target.style.transition = '';
        }, 150);
      }, 100);

      // Open post details (handleOpenPostDetails already handles thread logic)
      onOpenDetails(post.id);
    }
  };

  return (
    <article
      className={`post-card notely-card relative bg-notely-card border border-[#2F2F2F] rounded-notely-xl shadow-notely-md hover:shadow-notely-lg hover:-translate-y-2 group break-inside-avoid mb-4 overflow-hidden cursor-pointer transition-all duration-300 ease-out hover:scale-[1.02] active:scale-[0.98] h-auto
      ${currentPlatform === 'X/Twitter' ? 'border-t-[3px] border-t-black' : ''}
      ${currentPlatform === 'LinkedIn' ? 'border-t-[3px] border-t-blue-600' : ''}
      ${currentPlatform === 'Reddit' ? 'border-t-[3px] border-t-orange-500' : ''}
      ${currentPlatform === 'Instagram' ? 'border-t-[3px] border-t-purple-500' : ''}
      ${currentPlatform === 'pinterest' ? 'border-t-[3px] border-t-red-600' : ''}
      ${currentPlatform === 'Web' ? 'border-t-[3px] border-t-green-500' : ''}`}
      onClick={handleCardClick}
      data-post-id={post.id}
    >
      {/* Platform logo in top-right corner */}
      <div className="absolute top-3 right-3 z-10">
        <PlatformLogo platform={currentPlatform} className="w-5 h-5 text-white" />
      </div>

      {/* Thread indicator - show if post is part of a thread */}
      {!isForCapture && post.isThread && (
        <div className="absolute top-2 right-10 z-10">
          <div className="bg-red-500 text-white text-sm px-3 py-2 rounded-full font-bold flex items-center gap-1 border-2 border-red-600/50 shadow-lg">
            🧵 THREAD
          </div>
        </div>
      )}

      <div className={`${currentPlatform === 'pinterest' ? 'notely-breathing-compact-sm' : 'notely-breathing-compact-md'} ${currentPlatform !== 'pinterest' ? 'px-2 sm:px-4' : ''}`}>
        {/* Header: Avatar + Name + Handle + Time - Enhanced hierarchy */}
        {currentPlatform !== 'pinterest' && (
          <div className="flex items-center mb-2 sm:mb-3">
            {/* Avatar section with better spacing */}
            <div className="flex-shrink-0">
              {avatarSrc ? (
                isBase64Image(avatarSrc) ? (
                  // Directly render base64 avatar image
                  <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden shadow-sm border border-gray-100">
                    <img
                      src={avatarSrc}
                      alt={`${post.authorName || 'Author'} avatar`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        console.error(`[PostCard] Base64 avatar failed to load`);
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                ) : isInstagramUrl(sourceAvatarUrl) ? (
                  // For Instagram, use the avatar URL directly without proxying
                  <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden shadow-sm border border-gray-100">
                    <img
                      src={sourceAvatarUrl}
                      alt={`${post.authorName || 'Author'} avatar`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        console.error(`[PostCard] Instagram avatar failed to load directly: ${sourceAvatarUrl?.substring(0, 50)}...`);
                        // Instead of hiding, show a fallback avatar with initials
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          parent.style.display = 'flex';
                          parent.style.alignItems = 'center';
                          parent.style.justifyContent = 'center';
                          parent.style.backgroundColor = '#e0e0e0';
                          parent.style.color = '#666';
                          parent.style.fontWeight = 'bold';
                          parent.style.fontSize = '16px';

                          // Get initials from author name
                          const name = post.authorName || post.authorHandle || 'U';
                          const initials = name.split(' ')
                            .map(part => part.charAt(0))
                            .join('')
                            .substring(0, 2)
                            .toUpperCase();

                          parent.textContent = initials;
                        }
                      }}

                    />
                  </div>
                ) : (
                  // Render the actual avatar image for non-Instagram sources
                  <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gray-200 bg-cover bg-center shadow-sm border border-gray-100`}
                       style={{ backgroundImage: `url(${avatarSrc})` }}>
                  </div>
                )
              ) : (
                // Render default grey circle placeholder for other platforms (Pinterest avatar section is hidden entirely)
                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gray-200">
                </div>
              )}
            </div>

            {/* Author info with improved hierarchy */}
            <div className={`flex-grow min-w-0 ml-2 sm:ml-2.5 flex items-start`}>
              <div className="flex flex-col space-y-0 leading-none">
                {/* Author name - larger and more prominent */}
                <span className={`author-name font-bold text-lg text-notely-text-primary notely-heading ${!isForCapture ? 'truncate' : ''}`} style={{fontSize: '1.125rem', letterSpacing: '-0.01em', lineHeight: '1.1', marginBottom: '1px'}}>
                  {post.platform === 'Web' && post.permalink ? new URL(post.permalink).hostname.replace('www.', '') : (post.authorName || post.authorHandle || 'Unknown Author')}
                </span>

                {/* Handle and timestamp - smaller and secondary */}
                <div className="flex items-center space-x-2 text-xs sm:text-sm">
                    {post.platform !== 'Web' && post.authorHandle && (
                      <span className={`author-handle text-notely-text-muted notely-body ${!isForCapture ? 'truncate' : ''}`} style={{lineHeight: '1.1', marginTop: '0'}}>
                        @{post.authorHandle}
                      </span>
                    )}
                    {(post.timestamp || post.savedAt) && (
                      <>
                        {post.platform !== 'Web' && post.authorHandle && <span className="text-notely-text-tertiary">·</span>}
                        <span className="timestamp text-notely-text-muted hover:text-notely-text-secondary notely-body transition-colors">
                          {formatTimestamp(post.timestamp || post.savedAt)}
                        </span>
                      </>
                    )}

                    {/* Domain/source for web content - even smaller */}
                    {post.platform === 'Web' && post.permalink && false && (
                      <>
                        <span className="text-notely-text-tertiary">·</span>
                        <span className="text-xs text-notely-text-muted">
                          {new URL(post.permalink).hostname.replace('www.', '')}
                        </span>
                      </>
                    )}
                  </div>
              </div>
            </div>
          </div>
        )}

        {/* Media - Show image(s) and video(s) with improved spacing */}
        {currentPlatform !== 'pinterest' && (finalImageUrls.length > 0 || shouldShowVideo) && (
          <div className={`mt-2 mb-2 sm:mt-3 sm:mb-3`}>
            {shouldShowVideo && firstVideo ? (
              // Single video display
              <div
                className="relative rounded-xl overflow-hidden bg-gray-100 shadow-sm hover:shadow-md transition-shadow duration-200 flex items-center justify-center"
                style={{
                  aspectRatio: currentPlatform === 'Instagram' ? '1 / 1' :
                               currentPlatform === 'LinkedIn' ? '16 / 9' :
                               '16 / 9'
                }}
              >
                <video
                  src={firstVideo.url}
                  className="w-full h-full object-cover"
                  controls
                  preload={firstVideo.url?.startsWith('blob:') ? 'auto' : 'metadata'}
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click when interacting with video controls
                  }}
                  onError={(e) => {
                    console.error(`[PostCard] 🎥 Video failed to load: ${firstVideo.url?.substring(0, 50)}...`);
                    const target = e.currentTarget;
                    
                    // For blob URLs, try to reload the video element once
                    if (firstVideo.url?.startsWith('blob:')) {
                      console.log(`[PostCard] 🔄 Attempting to reload blob video...`);
                      setTimeout(() => {
                        target.load(); // Try to reload the video
                      }, 1000);
                      return; // Don't show error immediately for blob URLs
                    }
                    
                    target.style.display = 'none';
                    
                    // Show retry option
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'flex flex-col items-center justify-center h-full bg-gray-100 text-gray-600 p-4 text-center cursor-pointer';
                    errorDiv.innerHTML = `
                      <svg class="w-12 h-12 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                      </svg>
                      <p class="text-sm font-medium">Video temporarily unavailable</p>
                      <p class="text-xs text-gray-500 mt-1">Click to view in full screen</p>
                    `;
                    
                    errorDiv.onclick = (event) => {
                      event.stopPropagation();
                      onOpenDetails(post.id);
                    };
                    
                    target.parentNode?.appendChild(errorDiv);
                  }}
                >
                  <div className="flex items-center justify-center h-full bg-gray-200 text-gray-500">
                    Video not supported
                  </div>
                </video>
                {/* Video overlay indicator */}
                <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M0 12V4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm6.79-6.907a.5.5 0 0 0-.79.407v5a.5.5 0 0 0 .79.407l3.5-2.5a.5.5 0 0 0 0-.814z"/>
                  </svg>
                  <span>Video</span>
                </div>
                {/* Streaming indicator for Twitter videos */}
                {currentPlatform === 'X/Twitter' && (
                  <div className="absolute top-2 right-2 bg-blue-500/80 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                    <span>Live</span>
                  </div>
                )}
              </div>
            ) : shouldUseSwiper ? (
              // Multi-image carousel using ImageSwiper
              <ImageSwiper
                images={finalImageUrls}
                className="rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200"
                imageClassName={currentPlatform === 'Reddit' ? 'max-h-[400px]' : undefined}
                aspectRatio={currentPlatform === 'Instagram' ? 'square' : 'video'}
                onImageClick={(index) => {
                  // Open PostViewerFullScreen instead of ImageModal
                  onOpenDetails(post.id);
                }}
              />
            ) : isInstagramUrl(mainImageUrl) ? (
              // Use ProxyImage for Instagram URLs that aren't base64 - centered with flexbox
              <ProxyImage
                src={mainImageSrc || ''}
                alt={mainImageAlt}
                className={`w-full h-full ${isForCapture ? 'object-contain' : 'object-cover'}`}
                style={{ objectPosition: 'center center', objectFit: isForCapture ? 'contain' : 'cover' }}
                fallbackSrc={mainImageUrl || ''}
                postId={post.id}
                onClick={() => {
                  onOpenDetails(post.id);
                }}

              />
            ) : (
              // Regular image for other platforms - centered with flexbox
              <div
                className="relative rounded-xl overflow-hidden bg-gray-100 shadow-sm hover:shadow-md transition-shadow duration-200 flex items-center justify-center"
                style={{
                  aspectRatio: currentPlatform === 'Instagram' ? '1 / 1' :
                               currentPlatform === 'LinkedIn' ? '16 / 9' :
                               '16 / 9'
                }}
              >
                <img
                  src={mainImageSrc}
                  alt={mainImageAlt}
                  className={`w-full h-full ${isForCapture ? 'object-contain' : 'object-cover'}`}
                  style={{ objectPosition: 'center center', objectFit: isForCapture ? 'contain' : 'cover' }}
                  loading="lazy"
                  onClick={() => {
                    onOpenDetails(post.id);
                  }}
                  onError={(e) => (e.currentTarget.style.display = 'none')}
                />
              </div>
            )
          }
          </div>
        )}

        {/* Content for non-Pinterest (shown after images now) */}
        {currentPlatform !== 'pinterest' && post.content && (
          <div className="post-content px-4 py-3 font-normal text-[15px] text-notely-text-primary leading-relaxed">
            {/* Proper JavaScript truncation instead of broken CSS line-clamp */}
            {isExpanded ? post.content : 
              post.content.length > 300 ? 
                post.content.substring(0, 300).trim() + '...' : 
                post.content
            }
            {post.content.length > 300 && (
              <button onClick={(e) => {
                e.stopPropagation();
                onOpenDetails(post.id);
              }} className="show-more text-notely-accent font-medium hover:underline ml-1 notely-filter-transition">
                {t('dashboard.showMore')}
              </button>
            )}
          </div>
        )}

        {/* Pinterest-specific render approach */}
        {currentPlatform === 'pinterest' && finalImageUrls.length > 0 && (
          <div className="w-full rounded-xl overflow-hidden">
            {shouldUseSwiper ? (
              // Multi-image carousel for Pinterest using ImageSwiper
              <div className="relative w-full">
                <ImageSwiper
                  images={finalImageUrls}
                  className="rounded-xl"
                  aspectRatio="auto"
                  onImageClick={(index) => {
                    // Open PostViewerFullScreen instead of ImageModal
                    onOpenDetails(post.id);
                  }}
                />
                {post.content && post.content.trim() && (
                  <div className="absolute bottom-2 left-2 right-2 bg-white/80 dark:bg-black/80 backdrop-blur-md p-2 rounded-lg text-xs line-clamp-2 text-black dark:text-white">
                    {post.content}
                  </div>
                )}
              </div>
            ) : (
              // Single image for Pinterest (existing logic)
              <div className="relative w-full">
                {isBase64Image(mainImageSrc) ? (
                  // Directly render base64 image data for Pinterest - centered
                  <img
                    src={mainImageSrc}
                    alt={mainImageAlt}
                    className="w-full object-cover object-center rounded-xl"
                    loading="lazy"
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}
                    onError={(e) => {
                      console.error(`[PostCard] Pinterest base64 image failed to load`);
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                ) : isInstagramUrl(mainImageUrl) ? (
                  // Use ProxyImage for Instagram URLs that aren't base64 - centered
                  <ProxyImage
                    src={mainImageSrc || ''}
                    alt={mainImageAlt}
                    className="w-full object-cover object-center rounded-xl"
                    fallbackSrc={mainImageUrl || ''}
                    postId={post.id}
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}

                  />
                ) : (
                  // Regular image for other platforms - centered
                  <img
                    src={mainImageSrc}
                    alt={mainImageAlt}
                    className="w-full object-cover object-center rounded-xl"
                    loading="lazy"
                    onClick={() => {
                      onOpenDetails(post.id);
                    }}
                    onError={(e) => (e.currentTarget.style.display = 'none')}
                  />
                )}
                {post.content && post.content.trim() && (
                  <div className="absolute bottom-2 left-2 right-2 bg-white/80 dark:bg-black/80 backdrop-blur-md p-2 rounded-lg text-xs line-clamp-2 text-black dark:text-white">
                    {post.content}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Engagement Metrics - Enhanced with consistent alignment */}
        {(() => {
          const metrics = getEngagementMetrics(post);

          // For Pinterest, show engagement metrics only if they have meaningful data
          if (currentPlatform === 'pinterest') {
            const hasMetrics = metrics.comments > 0 || metrics.shares > 0 || metrics.likes > 0;

            return (
              <div className="mt-3 pt-3 pb-2 border-t border-[#2F2F2F]">
                <div className="flex items-center justify-between min-h-[24px]">
                  {/* Left side - Pinterest engagement metrics (only show if available) */}
                  <div className="flex items-center space-x-4">
                    {hasMetrics ? (
                      <>
                        {/* Comments */}
                        {metrics.comments > 0 && (
                          <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-sky transition-all duration-200 cursor-pointer hover:scale-105">
                            <div className="w-4 h-4 flex items-center justify-center">
                              <CommentIcon className="w-4 h-4 group-hover:scale-110 transition-transform duration-200 ease-out" />
                            </div>
                            <span className="text-sm font-medium">{formatNumber(metrics.comments)}</span>
                          </div>
                        )}

                        {/* Saves (Pinterest's equivalent of shares) */}
                        {metrics.shares > 0 && (
                          <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-mint transition-all duration-200 cursor-pointer hover:scale-105">
                            <div className="w-4 h-4 flex items-center justify-center">
                              <ShareIcon className="w-4 h-4 group-hover:scale-110 transition-transform duration-200 ease-out" />
                            </div>
                            <span className="text-sm font-medium">{formatNumber(metrics.shares)}</span>
                          </div>
                        )}

                        {/* Likes */}
                        {metrics.likes > 0 && (
                          <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-coral transition-all duration-200 cursor-pointer hover:scale-105">
                            <div className="w-4 h-4 flex items-center justify-center">
                              <HeartIcon className="w-4 h-4 group-hover:scale-110 transition-transform duration-200 ease-out" />
                            </div>
                            <span className="text-sm font-medium">{formatNumber(metrics.likes)}</span>
                          </div>
                        )}
                      </>
                    ) : (
                      /* Show only icons for Pinterest when no engagement data */
                      <>
                        <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                          <div className="w-4 h-4 flex items-center justify-center">
                            <CommentIcon className="w-4 h-4" />
                          </div>
                        </div>
                        <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                          <div className="w-4 h-4 flex items-center justify-center">
                            <ShareIcon className="w-4 h-4" />
                          </div>
                        </div>
                        <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                          <div className="w-4 h-4 flex items-center justify-center">
                            <HeartIcon className="w-4 h-4" />
                          </div>
                        </div>
                      </>
                    )}
                  </div>

                  {/* Right side - Action buttons (always show for Pinterest) */}
                  {!isForCapture && (
                    <div className="flex items-center space-x-2">
                      {/* Delete Button */}
                      <button
                        onClick={handleDelete}
                        className="text-notely-text-muted hover:text-red-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                        title="Delete Post"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>

                      {/* Original Link */}
                      {post.permalink && (
                        <a
                          href={post.permalink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-notely-text-muted hover:text-blue-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                          onClick={(e) => e.stopPropagation()}
                          title="View original post"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                        </a>
                      )}

                      {/* Download as Image Button */}
                      <button
                        onClick={async (e) => {
                          e.stopPropagation();
                          try {
                            const postCardElement = e.currentTarget.closest('.post-card') as HTMLElement;
                            if (postCardElement) {
                              toast.info('Generating image...', { duration: 1000 });
                              await downloadPostCardAsImage(postCardElement, post.id, post.platform, post);
                              toast.success('Post card downloaded successfully!');
                            }
                          } catch (error) {
                            console.error('Failed to download post card as image:', error);
                            toast.error('Failed to download post card. Please try again.');
                          }
                        }}
                        className="text-notely-text-muted hover:text-green-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                        title="Download post card as image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                      </button>

                      {/* Copy as Image Button */}
                      <button
                        onClick={async (e) => {
                          e.stopPropagation();
                          try {
                            const postCardElement = e.currentTarget.closest('.post-card') as HTMLElement;
                            if (postCardElement) {
                              toast.info('Generating image...', { duration: 1000 });
                              await copyPostCardAsImage(postCardElement, post.platform, post);
                              toast.success('Post card copied to clipboard!');
                            }
                          } catch (error) {
                            console.error('Failed to copy post card as image:', error);
                            toast.error('Failed to copy post card. Please try again.');
                          }
                        }}
                        className="text-notely-text-muted hover:text-purple-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                        title="Copy post card as image to clipboard"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            );
          }

          // For other platforms, show engagement metrics as before
          const hasMetrics = metrics.comments > 0 || metrics.shares > 0 || metrics.likes > 0 || (post.views && post.views > 0);

          return hasMetrics ? (
            <div className="mt-3 pt-3 pb-2 border-t border-[#2F2F2F]">
              <div className="flex items-center justify-between min-h-[24px]">
                {/* Left side - Statistics with consistent spacing */}
                <div className="flex items-center space-x-4">
                  {/* Comments */}
                  {metrics.comments > 0 ? (
                    <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-sky transition-all duration-200 cursor-pointer hover:scale-105">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <CommentIcon className="w-4 h-4 group-hover:scale-110 transition-transform duration-200 ease-out" />
                      </div>
                      <span className="text-sm font-medium">{formatNumber(metrics.comments)}</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <CommentIcon className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">0</span>
                    </div>
                  )}

                  {/* Shares */}
                  {metrics.shares > 0 ? (
                    <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-mint transition-all duration-200 cursor-pointer hover:scale-105">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <ShareIcon className="w-4 h-4 group-hover:scale-110 transition-transform duration-200 ease-out" />
                      </div>
                      <span className="text-sm font-medium">{formatNumber(metrics.shares)}</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <ShareIcon className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">0</span>
                    </div>
                  )}

                  {/* Likes */}
                  {metrics.likes > 0 ? (
                    <div className="group flex items-center space-x-1.5 text-notely-text-muted hover:text-notely-coral transition-all duration-200 cursor-pointer hover:scale-105">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <HeartIcon className="w-4 h-4 group-hover:scale-110 transition-transform duration-200 ease-out" />
                      </div>
                      <span className="text-sm font-medium">{formatNumber(metrics.likes)}</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1.5 text-notely-text-muted opacity-40">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <HeartIcon className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">0</span>
                    </div>
                  )}

                  {/* Views - only show if available */}
                  {post.views && post.views > 0 && (
                    <div className="flex items-center space-x-1.5 text-notely-text-muted">
                      <div className="w-4 h-4 flex items-center justify-center">
                        <EyeIcon className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium">{formatNumber(post.views)}</span>
                    </div>
                  )}
                </div>

                {/* Right side - Action buttons */}
                {!isForCapture && (
                  <div className="flex items-center space-x-2">
                    {/* Delete Button */}
                    <button
                      onClick={handleDelete}
                      className="text-notely-text-muted hover:text-red-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                      title="Delete Post"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>

                    {/* Original Link */}
                    {post.permalink && (
                      <a
                        href={post.permalink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-notely-text-muted hover:text-blue-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                        onClick={(e) => e.stopPropagation()}
                        title="View original post"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    )}

                    {/* Download as Image Button */}
                    <button
                      onClick={async (e) => {
                        e.stopPropagation();
                        try {
                          const postCardElement = e.currentTarget.closest('.post-card') as HTMLElement;
                          if (postCardElement) {
                            toast.info('Generating image...', { duration: 1000 });
                            await downloadPostCardAsImage(postCardElement, post.id, post.platform, post);
                            toast.success('Post card downloaded successfully!');
                          }
                        } catch (error) {
                          console.error('Failed to download post card as image:', error);
                          toast.error('Failed to download post card. Please try again.');
                        }
                      }}
                      className="text-notely-text-muted hover:text-green-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                      title="Download post card as image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                    </button>

                    {/* Copy as Image Button */}
                    <button
                      onClick={async (e) => {
                        e.stopPropagation();
                        try {
                          const postCardElement = e.currentTarget.closest('.post-card') as HTMLElement;
                          if (postCardElement) {
                            toast.info('Generating image...', { duration: 1000 });
                            await copyPostCardAsImage(postCardElement, post.platform, post);
                            toast.success('Post card copied to clipboard!');
                          }
                        } catch (error) {
                          console.error('Failed to copy post card as image:', error);
                          toast.error('Failed to copy post card. Please try again.');
                        }
                      }}
                      className="text-notely-text-muted hover:text-purple-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                      title="Copy post card as image to clipboard"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                      </svg>
                    </button>
                  </div>
                )}

              </div>
            </div>
          ) : (
            /* Always show placeholder row for consistent spacing */
            <div className="mt-3 pt-3 pb-2 border-t border-[#2F2F2F]">
              <div className="flex items-center justify-between min-h-[24px]">
                <div className="flex items-center space-x-4 opacity-40">
                  <div className="flex items-center space-x-1.5 text-notely-text-muted">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <CommentIcon className="w-4 h-4" />
                    </div>
                    <span className="text-sm font-medium">0</span>
                  </div>
                  <div className="flex items-center space-x-1.5 text-notely-text-muted">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <ShareIcon className="w-4 h-4" />
                    </div>
                    <span className="text-sm font-medium">0</span>
                  </div>
                  <div className="flex items-center space-x-1.5 text-notely-text-muted">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <HeartIcon className="w-4 h-4" />
                    </div>
                    <span className="text-sm font-medium">0</span>
                  </div>
                </div>

                {/* Right side - Action buttons */}
                {!isForCapture && (
                  <div className="flex items-center space-x-2">
                    {/* Delete Button */}
                    <button
                      onClick={handleDelete}
                      className="text-notely-text-muted hover:text-red-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                      title="Delete Post"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>

                    {/* Original Link */}
                    {post.permalink && (
                      <a
                        href={post.permalink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-notely-text-muted hover:text-blue-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                        onClick={(e) => e.stopPropagation()}
                        title="View original post"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    )}

                    {/* Download as Image Button */}
                    <button
                      onClick={async (e) => {
                        e.stopPropagation();
                        try {
                          const postCardElement = e.currentTarget.closest('.post-card') as HTMLElement;
                          if (postCardElement) {
                            toast.info('Generating image...', { duration: 1000 });
                            await downloadPostCardAsImage(postCardElement, post.id, post.platform, post);
                            toast.success('Post card downloaded successfully!');
                          }
                        } catch (error) {
                          console.error('Failed to download post card as image:', error);
                          toast.error('Failed to download post card. Please try again.');
                        }
                      }}
                      className="text-notely-text-muted hover:text-green-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                      title="Download post card as image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                    </button>

                    {/* Copy as Image Button */}
                    <button
                      onClick={async (e) => {
                        e.stopPropagation();
                        try {
                          const postCardElement = e.currentTarget.closest('.post-card') as HTMLElement;
                          if (postCardElement) {
                            toast.info('Generating image...', { duration: 1000 });
                            await copyPostCardAsImage(postCardElement, post.platform, post);
                            toast.success('Post card copied to clipboard!');
                          }
                        } catch (error) {
                          console.error('Failed to copy post card as image:', error);
                          toast.error('Failed to copy post card. Please try again.');
                        }
                      }}
                      className="text-notely-text-muted hover:text-purple-500 transition-all duration-200 ease-in-out hover:scale-110 active:scale-95"
                      title="Copy post card as image to clipboard"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                      </svg>
                    </button>
                  </div>
                )}

              </div>
            </div>
          );
        })()}



      </div>

      {/* Image Modal */}
      {(selectedImage || selectedImages.length > 0) && (
        <ImageModal
          imageUrl={selectedImage}
          images={selectedImages}
          initialIndex={selectedImageIndex}
          alt={mainImageAlt}
          onClose={() => {
            setSelectedImage(null);
            setSelectedImages([]);
            setSelectedImageIndex(0);
          }}
        />
      )}

      {/* No confirmation modal at the individual card level */}
    </article>
  );
};





// --- NEW: Platform Logo Component ---
const PlatformLogo: React.FC<{ platform: Platform, className?: string }> = ({ platform, className = "w-4 h-4" }) => { // Default to w-4 h-4
  // PlatformLogo ONLY passes down the className for size/layout.
  // Color (fill/stroke) is handled by individual logo components below.
  switch (platform) {
    case 'X/Twitter': return <div title="X/Twitter"><XLogo className={className} /></div>;
    case 'LinkedIn': return <div title="LinkedIn"><LinkedInLogo className={className} /></div>;
    case 'Reddit': return <div title="Reddit"><RedditLogo className={className} /></div>;
    case 'Instagram': return <div title="Instagram"><InstagramLogo className={className} /></div>;
    case 'pinterest': return <div title="Pinterest"><PinterestLogo className={className} /></div>;
    case 'Web': return <div title="Web"><WebLogo className={className} /></div>;
    // Add cases for other platforms if needed
    default: return null;
  }
};

// --- Individual Logo SVG Components - Accept className Prop AND set their own color ---

// X (Twitter) Logo SVG
const XLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg viewBox="0 0 24 24" className={`fill-current ${className}`} xmlns="http://www.w3.org/2000/svg">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231 5.45-6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

// LinkedIn Logo SVG - Using Bootstrap Icons definition
const LinkedInLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-current ${className}`} viewBox="0 0 16 16">
    <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248S2.4 3.226 2.4 3.934c0 .694.521 1.248 1.327 1.248zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016l.016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225z"/>
  </svg>
);

// Reddit Logo SVG - Using correct Bootstrap Icons definition
const RedditLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-current ${className}`} viewBox="0 0 16 16">
    <path d="M6.167 8a.83.83 0 0 0-.83.83c0 .459.372.84.83.831a.831.831 0 0 0 0-1.661m1.843 3.647c.315 0 1.403-.038 1.976-.611a.23.23 0 0 0 0-.306.213.213 0 0 0-.306 0c-.353.363-1.126.487-1.67.487-.545 0-1.308-.124-1.671-.487a.213.213 0 0 0-.306 0 .213.213 0 0 0 0 .306c.564.563 1.652.61 1.977.61zm.992-2.807c0 .458.373.83.831.83s.83-.381.83-.83a.831.831 0 0 0-1.66 0z"/>
    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0m-3.828-1.165c-.315 0-.602.124-.812.325-.801-.573-1.9-.945-3.121-.993l.534-2.501 1.738.372a.83.83 0 1 0 .83-.869.83.83 0 0 0-.744.468l-1.938-.41a.2.2 0 0 0-.153.028.2.2 0 0 0-.086.134l-.592 2.788c-1.24.038-2.358.41-3.17.992-.21-.2-.496-.324-.81-.324a1.163 1.163 0 0 0-.478 2.224q-.03.17-.029.353c0 1.795 2.091 3.256 4.669 3.256s4.668-1.451 4.668-3.256c0-.114-.01-.238-.029-.353.401-.181.688-.592.688-1.069 0-.65-.525-1.165-1.165-1.165"/>
  </svg>
);

const InstagramLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} /* Removed stroke/fill attributes here */ viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
  </svg>
);

// Pinterest Logo SVG - Using the new SVG provided by user
const PinterestLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={`fill-current ${className}`} viewBox="0 0 579.148 579.148" xmlns="http://www.w3.org/2000/svg" xmlSpace="preserve">
      <path d="M434.924,38.847C390.561,12.954,342.107,0.01,289.574,0.01c-52.54,0-100.992,12.944-145.356,38.837
        C99.854,64.741,64.725,99.87,38.837,144.228C12.944,188.597,0,237.049,0,289.584c0,58.568,15.955,111.732,47.883,159.486
        c31.922,47.768,73.771,83.08,125.558,105.949c-1.01-26.896,0.625-49.137,4.902-66.732l37.326-157.607
        c-6.285-12.314-9.425-27.645-9.425-45.999c0-21.365,5.404-39.217,16.212-53.538c10.802-14.333,24.003-21.5,39.59-21.5
        c12.564,0,22.246,4.143,29.034,12.448c6.787,8.292,10.184,18.727,10.184,31.292c0,7.797-1.451,17.289-4.334,28.47
        c-2.895,11.187-6.665,24.13-11.31,38.837c-4.651,14.701-7.98,26.451-9.994,35.252c-3.525,15.33-0.63,28.463,8.672,39.4
        c9.295,10.936,21.616,16.4,36.952,16.4c26.898,0,48.955-14.951,66.176-44.865c17.217-29.914,25.826-66.236,25.826-108.973
        c0-32.925-10.617-59.701-31.859-80.312c-21.242-20.606-50.846-30.918-88.795-30.918c-42.486,0-76.862,13.642-103.123,40.906
        c-26.267,27.277-39.401,59.896-39.401,97.84c0,22.625,6.414,41.609,19.229,56.941c4.272,5.029,5.655,10.428,4.149,16.205
        c-0.508,1.512-1.511,5.281-3.017,11.309c-1.505,6.029-2.515,9.934-3.017,11.689c-2.014,8.049-6.787,10.564-14.333,7.541
        c-19.357-8.043-34.064-21.99-44.113-41.85c-10.055-19.854-15.08-42.852-15.08-68.996c0-16.842,2.699-33.685,8.103-50.527
        c5.404-16.842,13.819-33.115,25.264-48.832c11.432-15.698,25.135-29.596,41.102-41.659c15.961-12.069,35.38-21.738,58.256-29.04
        c22.871-7.283,47.51-10.93,73.904-10.93c35.693,0,67.744,7.919,96.146,23.751c28.402,15.839,50.086,36.329,65.043,61.463
        c14.951,25.135,22.436,52.026,22.436,80.692c0,37.705-6.541,71.641-19.607,101.807c-13.072,30.166-31.549,53.855-55.43,71.072
        c-23.887,17.215-51.035,25.826-81.445,25.826c-15.336,0-29.664-3.58-42.986-10.748c-13.33-7.166-22.503-15.648-27.528-25.453
        c-11.31,44.486-18.097,71.018-20.361,79.555c-4.78,17.852-14.584,38.457-29.413,61.836c26.897,8.043,54.296,12.062,82.198,12.062
        c52.534,0,100.987-12.943,145.35-38.83c44.363-25.895,79.492-61.023,105.387-105.393c25.887-44.365,38.838-92.811,38.838-145.352
        c0-52.54-12.951-100.985-38.838-145.355C514.422,99.87,479.287,64.741,434.924,38.847z"/>
  </svg>
);

// Web Logo SVG - Globe icon for web content
const WebLogo: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="2" y1="12" x2="22" y2="12"></line>
    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
  </svg>
);

// Engagement Icons - SVG icons for post metrics
const CommentIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
  </svg>
);

const ShareIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M17 1l4 4-4 4"></path>
    <path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
    <path d="M7 23l-4-4 4-4"></path>
    <path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
  </svg>
);

const HeartIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
  </svg>
);

const EyeIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={`fill-none stroke-current ${className}`} viewBox="0 0 24 24" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
    <circle cx="12" cy="12" r="3"></circle>
  </svg>
);

type ViewMode = Platform | 'All' | 'Mindstream';

const PlatformSelector: React.FC<{
  onSelect: (platform: ViewMode) => void,
  selectedPlatform: ViewMode,
  onCategoryViewToggle?: (platform: ViewMode) => void,
  enabledPlatforms?: Record<string, boolean>
}> = ({ onSelect, selectedPlatform, onCategoryViewToggle, enabledPlatforms = {} }) => {
  // Ensure these values exactly match the Platform type in types.ts and background.ts
  const allPlatforms: ViewMode[] = ['Mindstream', 'All', 'X/Twitter', 'LinkedIn', 'Reddit', 'Instagram', 'pinterest', 'Web'];
  
  // Filter platforms based on enabled integrations
  const platforms = allPlatforms.filter(platform => {
    // Always show Mindstream and All tabs
    if (platform === 'Mindstream' || platform === 'All') return true;
    
    // For other platforms, check if they're enabled in settings
    // If enabledPlatforms is empty (not loaded yet), show all platforms
    return Object.keys(enabledPlatforms).length === 0 || enabledPlatforms[platform] === true;
  });

  // Function to get platform-specific styles with enhanced visual hierarchy
  const getPlatformStyles = (platform: string, isSelected: boolean) => {
    const baseStyles = 'rounded-lg flex items-center justify-center px-4 py-2.5 text-sm font-medium transition-all duration-200 ease-out focus:outline-none focus:ring-2 focus:ring-offset-2 relative transform hover:shadow-lg active:scale-95';

    if (!isSelected) {
      return `${baseStyles} bg-notely-surface text-notely-text-secondary border border-gray-500/20 hover:bg-notely-dark-hover hover:text-notely-text-primary hover:shadow-notely-md hover:scale-110 focus:ring-notely-border/30 dark-mode-platform-btn`;
    }

    // Selected state styles with enhanced visual feedback and rounded design
    switch (platform) {
      case 'X/Twitter':
        return `${baseStyles} bg-black text-white border-none shadow-notely-md font-semibold hover:bg-gray-800 hover:shadow-notely-lg hover:scale-110 focus:ring-gray-500/30`;
      case 'LinkedIn':
        return `${baseStyles} bg-[#0077b5] text-white border-none shadow-notely-md font-semibold hover:bg-[#005885] hover:shadow-notely-lg hover:scale-110 focus:ring-blue-400/30`;
      case 'Reddit':
        return `${baseStyles} bg-[#FF4500] text-white border-none shadow-notely-md font-semibold hover:bg-[#e03d00] hover:shadow-notely-lg hover:scale-110 focus:ring-orange-400/30`;
      case 'Instagram':
        return `${baseStyles} text-white border-none shadow-notely-md font-semibold bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 hover:shadow-notely-lg hover:scale-110 focus:ring-purple-400/30`;
      case 'pinterest':
        return `${baseStyles} bg-[#E60023] text-white border-none shadow-notely-md font-semibold hover:bg-[#c7001e] hover:shadow-notely-lg hover:scale-110 focus:ring-red-400/30`;
      case 'Web':
        return `${baseStyles} text-white border-none shadow-notely-md font-semibold bg-green-500 hover:bg-green-600 hover:shadow-notely-lg hover:scale-110 focus:ring-green-400/30`;
      case 'Mindstream':
        return `${baseStyles} text-white border border-purple-400/30 dark:border-purple-500/50 shadow-notely-md font-semibold bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 hover:shadow-notely-lg hover:scale-110 focus:ring-purple-400/30`;
      case 'All':
      default:
        return `${baseStyles} text-white border-none shadow-notely-md font-semibold bg-notely-sky hover:bg-blue-600 hover:shadow-notely-lg hover:scale-110 focus:ring-blue-400/30`;
    }
  };

  return (
    <div className="flex flex-wrap gap-2 py-1">
      {platforms.map((platform) => {
        const isSelected = selectedPlatform === platform;

        // Handle logo element for different platform types
        let logoElement = null;
        if (platform === 'Mindstream') {
          logoElement = <span className="text-lg">🧠</span>;
        } else if (platform !== 'All') {
          logoElement = <PlatformLogo platform={platform as Platform} className={`w-4 h-4 ${isSelected ? 'text-white' : 'text-current'}`} />;
        }

        return (
          <button
            key={platform}
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              e.nativeEvent?.preventDefault();
              e.nativeEvent?.stopPropagation();
              e.nativeEvent?.stopImmediatePropagation();

              onSelect(platform);
              // Trigger category view for specific platforms
              if (platform !== 'All' && platform !== 'Mindstream' && onCategoryViewToggle) {
                onCategoryViewToggle(platform);
              }
              return false;
            }}
            className={getPlatformStyles(platform, isSelected)}
          >
            {logoElement && <span className={`mr-1.5 ${platform === 'Mindstream' ? 'text-lg' : ''}`}>{logoElement}</span>}
            {platform === 'pinterest' ? 'Pinterest' : platform === 'All' ? 'All' : platform === 'Mindstream' ? 'Mindstream' : platform}
          </button>
        );
      })}
    </div>
  );
};

// Add cog icon component
const SettingsIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="3"></circle>
    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
  </svg>
);

// --- Main Dashboard Component ---

// Constants for storage keys
const PLATFORM_INTEGRATIONS_KEY = 'platformIntegrations';

function DashboardContent() {
  const { t } = useTranslation();
  const [posts, setPosts] = useState<Post[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<Post[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<ViewMode>('Mindstream');
  const [availableCategoriesForFilter, setAvailableCategoriesForFilter] = useState<string[]>([]);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<Post[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [enabledPlatforms, setEnabledPlatforms] = useState<Record<string, boolean>>({});
  const [platformsLoaded, setPlatformsLoaded] = useState<boolean>(false);

    const loadPlatformIntegrations = async () => {
      try {
        const result = await chrome.storage.local.get([PLATFORM_INTEGRATIONS_KEY]);
        const savedIntegrations = result[PLATFORM_INTEGRATIONS_KEY] || {};
        setEnabledPlatforms(savedIntegrations);
        setPlatformsLoaded(true);
      } catch (error) {
        console.error('Error loading platform integrations:', error);
        // Set default values if there's an error
        setEnabledPlatforms({
          'X/Twitter': true,
          'LinkedIn': true,
          'Reddit': true,
          'Instagram': true,
          'pinterest': true,
          'Web': true
        });
        setPlatformsLoaded(true);
      }
    };

  const loadAndMergePosts = async (token: string | null, userId: string | null) => {
    // The actual implementation of merging local and cloud posts would go here.
    const localPosts = await getSavedPosts();
    setPosts(localPosts);
  };

  const loadWisdomBatch = async () => {
    // This function will contain the logic to load the daily wisdom
  };

  // Load platform integrations settings from storage
  useEffect(() => {
    loadPlatformIntegrations();

    // Listen for storage changes to update platform integrations in real-time
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes[PLATFORM_INTEGRATIONS_KEY]) {
        const newIntegrations = changes[PLATFORM_INTEGRATIONS_KEY].newValue || {};
        setEnabledPlatforms(newIntegrations);
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);

    // Cleanup listener on unmount
    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        setSearchError(null);
        setIsSearching(false);
        return;
      }

      if (!isLoggedIn) {
        setSearchError('Please log in to search your posts');
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      // Auto-switch to "All" tab only when searching from Mindstream tab
      if (selectedPlatform === 'Mindstream') {
        // Save current scroll position so we can restore after the DOM updates
        prevScrollYRef.current = window.scrollY;
        setSelectedPlatform('All');
      }

      setIsSearching(true);
      setSearchError(null);

      try {
        const results = await performSemanticSearch(query);

        if (results && results.results) {
          // Store search type for UI indicators
          (window as any).lastSearchType = results.searchType;
          // Merge search results with local posts to get complete data
          const searchPosts = results.results.map(result => {
            const searchPost = result.post as any; // Type assertion for search result

            // Try multiple matching strategies since local and cloud IDs are different
            let localPost = posts.find(p => p.id === searchPost.id); // Direct ID match

            if (!localPost) {
              // Try matching by permalink (most reliable)
              localPost = posts.find(p => p.permalink === searchPost.permalink);
              // Found by permalink
            }

            if (!localPost) {
              // Try matching by content + platform + author (fallback)
              localPost = posts.find(p =>
                p.platform === searchPost.platform &&
                p.authorName === searchPost.authorName &&
                p.content === searchPost.content
              );
              // Found by content match
            }

            if (localPost) {

              // Merge local post data with search result, prioritizing search result fields
              const mergedPost = {
                ...localPost,
                ...searchPost,
                // Ensure we keep important local fields that might be missing from search
                authorAvatar: searchPost.authorAvatar || localPost.authorAvatar,
                authorImage: searchPost.authorImage || localPost.authorImage,
                savedImage: searchPost.savedImage || localPost.savedImage,
                timestamp: searchPost.timestamp || localPost.timestamp,
                authorHandle: searchPost.authorHandle || localPost.authorHandle,
              } as Post;

              return mergedPost;
            } else {
              // If no local post found, use search result as-is (might be missing some fields)
              return searchPost as Post;
            }
          });

          setSearchResults(searchPosts);
          setSearchError(null);
        } else {
          setSearchResults([]);
          setSearchError('No results found');
        }
      } catch (error) {
        setSearchResults([]);
        setSearchError(error instanceof Error ? error.message : 'Search failed');
      } finally {
        setIsSearching(false);
      }
    }, 500),
    [isLoggedIn, selectedPlatform, posts]
  );

  // Handle search query changes
  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);



  // Simple category and tag filtering
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [categoryPostCounts, setCategoryPostCounts] = useState<Record<string, number>>({});
  // Preserve previous window scroll position between category switches
  const prevScrollYRef = useRef<number>(0);



  // Extract categories from posts filtered by selected platform and count posts per category
  useEffect(() => {
    const categories = new Set<string>();
    const categoryCounts: Record<string, number> = {};

    // Filter posts by selected platform first
    let postsToProcess = posts;
    if (selectedPlatform !== 'All' && selectedPlatform !== 'Mindstream') {
      postsToProcess = posts.filter(post => {
        const postPlatformLower = post.platform?.toLowerCase();
        const selectedPlatformLower = selectedPlatform.toLowerCase();
        return postPlatformLower === selectedPlatformLower;
      });
    }

    // Process posts to extract categories and count them
    postsToProcess.forEach(post => {
      if (post.categories && Array.isArray(post.categories)) {
        // Track which categories we've counted for this post
        const countedCategories = new Set<string>();
        
        post.categories.forEach(category => {
          if (category && category.trim() !== '') {
            // Add to unique categories set
            categories.add(category);
            
            // Only count each category once per post to avoid inflated counts
            if (!countedCategories.has(category)) {
              categoryCounts[category] = (categoryCounts[category] || 0) + 1;
              countedCategories.add(category);
            }
          }
        });
      }
    });

    // Sort categories by post count (descending), then alphabetically as a tiebreaker
    const sortedCategories = Array.from(categories).sort((a, b) => {
      const countA = categoryCounts[a] || 0;
      const countB = categoryCounts[b] || 0;
      
      // First sort by count (descending)
      if (countA !== countB) {
        return countB - countA;
      }
      
      // If counts are equal, sort alphabetically
      return a.localeCompare(b);
    });
    
    setAvailableCategories(sortedCategories);
    setCategoryPostCounts(categoryCounts);


    // Clear selected category if it's no longer available for the current platform
    if (selectedCategory && !categories.has(selectedCategory)) {
      setSelectedCategory(null);
    }
  }, [posts, selectedPlatform]);



  const [loggedInUser, setLoggedInUser] = useState<IUserFrontend | null>(null);
  const [isUserDataLoading, setIsUserDataLoading] = useState<boolean>(true); // Start true

  // --- NEW STATE FOR POST VIEWER ---
  const [selectedPostForViewer, setSelectedPostForViewer] = useState<PostWithAIData | null>(null);

  // Thread navigation state
  const [currentThreadPosts, setCurrentThreadPosts] = useState<PostWithAIData[] | null>(null);
  const [currentThreadIndex, setCurrentThreadIndex] = useState(0);
  // --- END NEW STATE ---

  // --- DELETE CONFIRMATION DIALOG STATE ---
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState<boolean>(false);
  const [postToDelete, setPostToDelete] = useState<string | null>(null);
  // --- END DELETE CONFIRMATION DIALOG STATE ---

  // const contentRef = useRef<HTMLDivElement>(null); // Removed unused ref

  // Function to merge and deduplicate posts from cloud and local storage
  const mergeAndDeduplicatePosts = (cloudPosts: Post[], localPosts: Post[]): Post[] => {
    // Create a map to track posts by their unique identifiers
    const postMap = new Map<string, Post>();

    // Add cloud posts first (they take priority)
    cloudPosts.forEach(post => {
      if (post) {
        // Use originalPostId as id if id is missing (for cloud posts)
        const postId = post.id || post.originalPostId || post._id;
        if (postId) {
          const normalizedPost = { ...post, id: postId };
          const key = getPostKey(normalizedPost);
          postMap.set(key, { ...normalizedPost, source: 'cloud' });
        }
      }
    });

    // Add local posts, but only if they don't already exist from cloud
    localPosts.forEach(post => {
      if (post && post.id) {
        const key = getPostKey(post);

        if (!postMap.has(key)) {
          // Check if this is a local post that might have been uploaded to cloud
          const cloudMatch = cloudPosts.find(cp =>
            cp.originalPostId === post.id ||
            cp.permalink === post.permalink ||
            (cp.authorName === post.authorName && cp.timestamp === post.timestamp)
          );

          if (!cloudMatch) {
            postMap.set(key, { ...post, source: 'local' });
          } else {
            // Mark the cloud post as synced since we found a local match
            const cloudKey = getPostKey(cloudMatch);
            if (postMap.has(cloudKey)) {
              const existingCloudPost = postMap.get(cloudKey)!;

              // IMPORTANT: Preserve local fields that might be newer than cloud
              const mergedPost = { ...existingCloudPost, source: 'synced' };

              // Preserve thread metadata from local post if cloud post doesn't have it
              if (post.isThread && !existingCloudPost.isThread) {
                mergedPost.isThread = post.isThread;
                mergedPost.threadId = post.threadId;
                mergedPost.threadPosition = post.threadPosition;
                mergedPost.threadLength = post.threadLength;
              }

              // Preserve notes from local post if it's newer or cloud doesn't have it
              if (post.notes && (!existingCloudPost.notes || post.notes !== existingCloudPost.notes)) {
                mergedPost.notes = post.notes;
              }

              postMap.set(cloudKey, mergedPost);
            } else {
              postMap.set(key, { ...post, source: 'local' });
            }
          }
        } else {
          // Check if we need to preserve local data (thread metadata or notes)
          const existingPost = postMap.get(key)!;
          let needsUpdate = false;
          const updatedPost = { ...existingPost };

          if (post.isThread) {
            if (!existingPost.isThread) {
              updatedPost.isThread = post.isThread;
              updatedPost.threadId = post.threadId;
              updatedPost.threadPosition = post.threadPosition;
              updatedPost.threadLength = post.threadLength;
              updatedPost.source = 'synced' as const;
              needsUpdate = true;
            }
          }

          // Check if we need to preserve notes from local post
          if (post.notes && (!existingPost.notes || post.notes !== existingPost.notes)) {
            updatedPost.notes = post.notes;
            updatedPost.source = 'synced' as const;
            needsUpdate = true;
          }

          if (needsUpdate) {
            postMap.set(key, updatedPost);
          }
        }
      }
    });

    const mergedPosts = Array.from(postMap.values());
    return mergedPosts;
  };

  // Helper function to generate a unique key for a post
  const getPostKey = (post: Post): string => {
    // Use multiple identifiers to create a unique key
    const id = post.id || post._id?.toString() || '';
    const permalink = post.permalink || '';
    const platform = post.platform || '';

    // Create a composite key
    return `${platform}:${id}:${permalink}`;
  };

  // Define initiateLocalToCloudSync early to avoid reference errors
  const initiateLocalToCloudSync = async () => {
    const localPostsToSync = await getSavedPosts(); // From '../storage'

    if (localPostsToSync && localPostsToSync.length > 0) {
      // For now, let's try to sync them one by one without complex queueing first
      // We'll rely on the backend to handle duplicates (409 conflict)
      // A more robust solution would involve checking if a post *needs* syncing (e.g., no cloud ID yet)

      const tokenResult = await new Promise<{ authToken?: string }>(resolve =>
        chrome.storage.local.get(['authToken'], result => resolve(result as { authToken?: string }))
      );
      const currentToken = tokenResult?.authToken;

      if (!currentToken) {
        console.warn('[Dashboard] initiateLocalToCloudSync: No auth token found. Cannot sync local posts.');
        return;
      }

      for (const post of localPostsToSync) {
        // We need to ensure the post object sent to SAVE_POST_CLOUD is what the background script expects.
        // The raw post from getSavedPosts() might be slightly different from what content.ts sends.
        // For now, assume it's compatible or background script's generateAnalyzedPost handles it.
        chrome.runtime.sendMessage(
          {
            action: 'SAVE_POST_CLOUD',
            data: post, // Send the local post data
            token: currentToken
          },
          (response) => {
            if (chrome.runtime.lastError) {
              console.error(`[Dashboard] initiateLocalToCloudSync: Error syncing post ${post.id}:`, chrome.runtime.lastError.message);
              // Handle error, maybe retry later or notify user
            } else if (response?.status !== 'success' && response?.status !== 'duplicate') {
              console.warn(`[Dashboard] initiateLocalToCloudSync: Failed to sync post ${post.id}. Response:`, response);
              // Handle failure
            }
          }
        );
        // Add a small delay to avoid overwhelming the background script/backend
        await new Promise(resolve => setTimeout(resolve, 500)); // 0.5 second delay between sync attempts
      }
      refreshPosts(); // Refresh dashboard to show newly synced posts from cloud
    }
  };

  // --- MODIFIED: loadInitialData ---
  const [isLoadingData, setIsLoadingData] = useState(false);
  const loadInitialData = async () => {
    // Prevent multiple concurrent calls
    if (isLoadingData) {
      return;
    }
    
    setIsLoadingData(true);
    setIsUserDataLoading(true);
    
    // Add a timeout to prevent infinite hanging
    const timeoutId = setTimeout(() => {
      setIsUserDataLoading(false);
      setIsLoggedIn(false);
      setLoggedInUser(null);
      setIsLoadingData(false);
    }, 10000); // 10 second timeout
    
    try {
      const tokenData = await new Promise<{ token?: string; authToken?: string }>(
        (resolve) =>
          chrome.storage.local.get(['token', 'authToken'], (result) =>
            resolve(result as { token?: string; authToken?: string })
          )
      );
      const currentToken = tokenData.authToken || tokenData.token;

      let fetchedPosts: Post[] = [];
      let fetchedCategories: string[] = [];

      if (currentToken) {
        setIsLoggedIn(true);
        // Fetch user data
        try {
          // Add timeout to prevent hanging
          const controller = new AbortController();
          const fetchTimeoutId = setTimeout(() => {
            controller.abort();
          }, 5000); // 5 second timeout
          
          const userResponse = await fetch(`${API_URL}/auth/me`, {
            headers: { 'Authorization': `Bearer ${currentToken}` },
            signal: controller.signal
          });
          clearTimeout(fetchTimeoutId);
          if (userResponse.ok) {
            const userData: IUserFrontend = await userResponse.json();
            setLoggedInUser(userData);
            // Save user profile to chrome storage for authService
            await chrome.storage.local.set({ user_profile: userData });
          } else {
            console.error('[Dashboard] Failed to fetch user data:', userResponse.status);
            // Clear authentication state directly instead of calling handleLogout to avoid infinite loop
            setIsLoggedIn(false);
            setLoggedInUser(null);
            await chrome.storage.local.remove(['token', 'authToken', 'user_profile']);
          }
        } catch (error) {
          console.error('[Dashboard] Error fetching user data:', error);
          // Clear authentication state directly instead of calling handleLogout to avoid infinite loop
          setIsLoggedIn(false);
          setLoggedInUser(null);
          await chrome.storage.local.remove(['token', 'authToken', 'user_profile']);
        }

        // Fetch posts from both cloud and local storage for logged-in users
        try {
            // Fetch cloud posts
            let cloudPosts: Post[] = [];
            try {
                const postsResponse = await fetch(`${API_URL}/api/posts`, {
                    headers: { 'Authorization': `Bearer ${currentToken}` },
                });
                if (postsResponse.ok) {
                    cloudPosts = await postsResponse.json();
                } else {
                    console.error('[Dashboard] Failed to fetch posts from cloud:', postsResponse.status);
                }
            } catch (cloudError) {
                console.error('[Dashboard] Error fetching posts from cloud:', cloudError);
            }

            // Fetch local posts
            let localPosts: Post[] = [];
            try {
                localPosts = await getSavedPosts();
            } catch (localError) {
                console.error('[Dashboard] Error fetching posts from local storage:', localError);
            }

            // Merge and deduplicate posts
            const mergedPosts = mergeAndDeduplicatePosts(cloudPosts, localPosts);

            // Validate merged posts
            const validPosts = mergedPosts.filter(p => {
                if (!p) {
                    return false;
                }

                const hasPlatform = p.platform && typeof p.platform === 'string';
                const hasPermalink = p.permalink && typeof p.permalink === 'string';

                let idToUse = p.id;
                if (p._id && (typeof p._id === 'string' || typeof p._id === 'object')) {
                    if (!(typeof idToUse === 'string' && idToUse.trim() !== '')) {
                        idToUse = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
                    }
                }
                const hasValidStringId = typeof idToUse === 'string' && idToUse.trim() !== '';

                if (!hasValidStringId) {
                    return false;
                }
                if (!hasPlatform) {
                    return false;
                }
                if (!hasPermalink) {
                    return false;
                }
                return true; // All checks passed
            });

            fetchedPosts = validPosts.map(p => {
                let finalId = p.id;
                if (!(typeof finalId === 'string' && finalId.trim() !== '') && p._id) {
                    finalId = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
                }
                return {
                    ...p,
                    id: finalId,
                    _id: p._id,
                    source: p.source || (p._id ? 'cloud' : 'local') // Add source metadata
                };
            });

            if (fetchedPosts.length < mergedPosts.length && mergedPosts.length > 0) {
                console.warn('[Dashboard] Filtered out', mergedPosts.length - fetchedPosts.length, 'of', mergedPosts.length, 'posts due to missing/invalid ID, platform, or permalink.');
            }
        } catch (error) {
            console.error('[Dashboard] Error fetching and merging posts:', error);
        }
        // TODO: Fetch categories & tags from cloud if they are user-specific
        // For now, using local as they might be global or locally managed for now.
        fetchedCategories = await getAllCategories();

      } else {
        setIsLoggedIn(false);
        setLoggedInUser(null);
        fetchedPosts = await getSavedPosts();
        fetchedCategories = await getAllCategories();
      }

      setPosts(fetchedPosts);
      // setFilteredPosts(fetchedPosts); // This is handled by the useEffect for filtering
      setAvailableCategoriesForFilter(fetchedCategories);

    } catch (error) {
      console.error("[Dashboard] Error loading initial data:", error);
      // Ensure some default state if everything fails
      setPosts([]);
      setAvailableCategoriesForFilter([]);
      // Clear auth state if there's a critical error
      setIsLoggedIn(false);
      setLoggedInUser(null);
    } finally {
      clearTimeout(timeoutId); // Clear the timeout since we completed normally
      setIsUserDataLoading(false);
      setIsLoadingData(false);
    }
  };

  useEffect(() => {
    // Initialize unified storage migration and cloud sync
    migrationService.runMigrations().then(() => {
      loadInitialData();

      // Initialize unified data sync after migration
      setTimeout(() => {
        syncUnifiedDataFromCloud().catch(error => {
          // This is normal if offline, no need to log
        });
      }, 2000);
    }).catch(error => {
      console.error('[Dashboard] Storage migration failed, loading initial data anyway:', error);
      loadInitialData();
    });

    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }, areaName: string) => {
      if (areaName === 'local' || areaName === 'sync') { // Listen to both local and sync
        let needsRefresh = false;
        let authChanged = false;

        if (changes.token) { // Token change means auth state might have changed
          authChanged = true;
        }
        // Assuming 'savedPosts' might be a key used by older local saves,
        // 'localSavedPosts' for explicit local saves, and cloud sync would also trigger a refresh.
        // The key 'posts' itself in storage is not standard here, rather 'savedPosts'.
        if (changes.savedPosts || changes.localSavedPosts || changes.allPosts) { // Monitor 'allPosts' if backend syncs to this key
          needsRefresh = true;
        }
        if (changes.categories) {
          // Refresh categories if they change
          getAllCategories().then(setAvailableCategoriesForFilter);
        }

        if (authChanged) {
          setLoggedInUser(null); // Reset user to trigger fetch if new token
          loadInitialData(); // Re-run full load to check auth and fetch user/posts
        } else if (needsRefresh) {
          refreshPosts(); // Just refresh post data if auth didn't change
        }
      }
    };
    chrome.storage.onChanged.addListener(handleStorageChange);

    const handleAuthMessage = (event: MessageEvent) => {
      if (event.data?.type === 'AUTH_SUCCESS' && event.data?.token) {
        chrome.storage.local.set({ token: event.data.token, authToken: event.data.token, userInfo: {} }, () => {
            // The storage listener (authChanged) will now trigger loadInitialData
            // The storage listener (authChanged) will trigger loadInitialData by itself.
            // We can call initiateLocalToCloudSync here directly after auth success.
            initiateLocalToCloudSync();
        });
        setIsLoginModalOpen(false);
      } else if (event.data?.type === 'AUTH_FAILURE') {
         console.error('[Dashboard] Received auth failure via postMessage:', event.data.error);
      }
    };
    window.addEventListener('message', handleAuthMessage);

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
      window.removeEventListener('message', handleAuthMessage);
      window.removeEventListener('popstate', handlePopState);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, []); // Main useEffect runs once on mount

  // --- MODIFIED: refreshPosts with useCallback ---
  const refreshPosts = useCallback(async () => {
    try {
      const tokenResult = await new Promise<{ token?: string }>(resolve =>
        chrome.storage.local.get(['token'], result => resolve(result as { token?: string }))
      );
      const currentToken = tokenResult?.token;
      let refreshedPosts: Post[] = [];

      if (currentToken) {
        // Fetch cloud posts
        let cloudPosts: Post[] = [];
        try {
          const fetchUrl = `${API_URL}/api/posts`;
          const response = await fetch(fetchUrl, {
            headers: { 'Authorization': `Bearer ${currentToken}` },
          });
          if (response.ok) {
            cloudPosts = await response.json();
          } else {
            const errorText = await response.text().catch(() => "Failed to get error text from response");
            console.error(`[Dashboard] refreshPosts: Failed to refresh posts from cloud. Status: ${response.status}. Response text:`, errorText);
          }
        } catch (cloudError) {
          console.error(`[Dashboard] refreshPosts: Error fetching cloud posts:`, cloudError);
        }

        // Fetch local posts
        let localPosts: Post[] = [];
        try {
          localPosts = await getSavedPosts();
        } catch (localError) {
          console.error(`[Dashboard] refreshPosts: Error fetching local posts:`, localError);
        }

        // Merge and deduplicate posts
        const mergedPosts = mergeAndDeduplicatePosts(cloudPosts, localPosts);

        // Validate and normalize merged posts
        const validIdPosts = mergedPosts.filter(p => {
          if (!p) {
            console.warn('[Dashboard] refreshPosts: Filtering out null/undefined post object.');
            return false;
          }

          const hasPlatform = p.platform && typeof p.platform === 'string';
          const hasPermalink = p.permalink && typeof p.permalink === 'string';

          let idToUse = p.id;
          if (p._id && (typeof p._id === 'string' || typeof p._id === 'object')) {
              // If p.id is not a valid string, try to use p._id (or its string representation)
              if (!(typeof idToUse === 'string' && idToUse.trim() !== '')) {
                  idToUse = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
              }
          }
          const hasValidStringId = typeof idToUse === 'string' && idToUse.trim() !== '';

          if (!hasValidStringId) {
            console.warn(`[Dashboard] refreshPosts: Filtering out post due to missing or invalid ID. Post data (raw):`, JSON.parse(JSON.stringify(p)));
            return false;
          }
          if (!hasPlatform) {
            console.warn(`[Dashboard] refreshPosts: Filtering out post (ID: ${idToUse}) due to missing or invalid platform. Post data:`, JSON.parse(JSON.stringify(p)));
            return false;
          }
          if (!hasPermalink) {
            console.warn(`[Dashboard] refreshPosts: Filtering out post (ID: ${idToUse}) due to missing or invalid permalink. Post data:`, JSON.parse(JSON.stringify(p)));
            return false;
          }
          return true; // All checks passed
        });

        // Map MongoDB format to frontend Post format if needed
        const normalizedPosts = validIdPosts.map(p => {
          let finalId = p.id;
          // If p.id is not a valid string, but p._id is, derive ID from p._id
          if (!(typeof finalId === 'string' && finalId.trim() !== '') && p._id) {
              finalId = typeof p._id === 'string' ? p._id : (p.originalPostId || p._id.toString());
          }
          // Ensure the final object has a valid 'id' field for the frontend.
          return {
            ...p,
            id: finalId,
            _id: p._id, // keep original _id for reference if needed
            source: p.source || (p._id ? 'cloud' : 'local') // Add source metadata
          };
        });

        if (normalizedPosts.length < mergedPosts.length && mergedPosts.length > 0) { // Only log if there were posts to begin with
              console.warn(`[Dashboard] refreshPosts: Filtered out ${mergedPosts.length - normalizedPosts.length} of ${mergedPosts.length} posts due to missing/invalid ID, platform, or permalink.`);
        }
        refreshedPosts = normalizedPosts;
      } else {
        refreshedPosts = await getSavedPosts();
      }
      setPosts(refreshedPosts);
    } catch (error) {
      console.error("[Dashboard] refreshPosts: General error during refresh:", error);
      setPosts([]); // Clear posts on error or show a message
    }
  }, [setPosts]); // Dependencies: setPosts. BACKEND_URL and getSavedPosts are stable.

  // Effect for listening to messages from background script
  useEffect(() => {
    const messageListener = (message: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => {
      if (message.action === 'REFRESH_DASHBOARD_FROM_CLOUD') {
        refreshPosts(); // Calling the memoized version of refreshPosts
        sendResponse({ status: 'ok', message: 'Dashboard refresh triggered by REFRESH_DASHBOARD_FROM_CLOUD.' });
        return true; // Indicate async response potential

      }
    };
    chrome.runtime.onMessage.addListener(messageListener);
    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, [refreshPosts]); // Dependency array now only contains the memoized refreshPosts

  const handleUpdatePostDetails = async (postId: string, details: { categories?: string[], tags?: string[], notes?: string }) => {
    try {
      await updatePostDetails(postId, details);
      // Optimistically update local state
      setPosts(prevPosts =>
        prevPosts.map(p =>
          p.id === postId ? {
              ...p,
              ...(details.categories !== undefined && { categories: details.categories }),
              ...(details.tags !== undefined && { tags: details.tags }),
              ...(details.notes !== undefined && { notes: details.notes }),
           } : p
        )
      );
      // If the post viewer is open and this is the selected post, update its data too
      if (selectedPostForViewer && selectedPostForViewer.id === postId) {
        setSelectedPostForViewer(prevSelectedPost => {
          if (!prevSelectedPost) return null;
          // Update with flattened structure
          return {
            ...prevSelectedPost,
            ...(details.categories !== undefined && { categories: details.categories }),
            ...(details.tags !== undefined && { tags: details.tags }),
            ...(details.notes !== undefined && { notes: details.notes }),
          };
        });
      }
    } catch (error) {
      console.error(`[Dashboard] Failed to update details for post ${postId}:`, error);
      // Consider reverting optimistic update or showing error message
    }
  };

  // Helper function to convert Post to PostWithAIData format
  const convertPostToViewerFormat = (postToView: Post): PostWithAIData => {

    let determinedMediaType: 'image' | 'video' | 'text';

    // Check if postToView itself has a valid mediaType property first
    if (postToView.hasOwnProperty('mediaType')) {
      const existingMediaType = (postToView as any).mediaType as string;
      if (existingMediaType === 'image' || existingMediaType === 'video' || existingMediaType === 'text') {
        determinedMediaType = existingMediaType;
      } else if (postToView.savedImage || (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('image'))) {
        determinedMediaType = 'image';
      } else if (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('video')) {
        determinedMediaType = 'video';
      } else {
        determinedMediaType = 'text';
      }
    } else {
      if (postToView.savedImage || (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('image'))) {
        determinedMediaType = 'image';
      } else if (postToView.media && postToView.media.length > 0 && postToView.media[0].type?.startsWith('video')) {
        determinedMediaType = 'video';
      } else {
        determinedMediaType = 'text';
      }
    }



    // Safely access potential existing AI data
    const snapNote = (postToView as any).snapNote;
    const inSight = (postToView as any).inSight;
    const fastTake = (postToView as any).fastTake;
    const postTags = (postToView as any).tags;
    const postCategories = (postToView as any).categories;

    // Better timestamp handling - check multiple possible fields
    const getTimestamp = (): string => {
      const possibleTimestamps = [
        postToView.timestamp,
        postToView.createdAt,
        postToView.savedAt
      ].filter(Boolean);

      for (const ts of possibleTimestamps) {
        if (ts) {
          // Validate the timestamp
          try {
            const date = new Date(ts);
            if (!isNaN(date.getTime())) {
              return ts;
            }
          } catch (e) {
            console.warn('Invalid timestamp:', ts);
          }
        }
      }

      // Fallback to current time if no valid timestamp found
      console.warn('No valid timestamp found for post:', postToView.id);
      return new Date().toISOString();
    };

    const finalTimestamp = getTimestamp();
    
    const result = {
      id: postToView.id,
      platform: postToView.platform,
      mediaType: determinedMediaType,
      mediaUrl: postToView.savedImage || (postToView.media && postToView.media.length > 0 ? postToView.media[0].url : undefined),
      text: postToView.content,
      author: postToView.authorName || postToView.authorHandle || 'Unknown Author',
      timestamp: finalTimestamp,
      stats: (postToView as any).interactions ? {
        likes: (postToView as any).interactions.likes,
        comments: (postToView as any).interactions.replies,
        shares: (postToView as any).interactions.reposts,
      } : undefined,
      // Better snapNote handling - don't show "pending" if it's actually available
      snapNote: snapNote && typeof snapNote === 'string' ? snapNote : null,
      notes: (postToView as any).notes || '',
      // Include the full media array for carousel support
      media: postToView.media || [],
      inSight: {
        sentiment: (
          inSight?.sentiment &&
          ['positive', 'neutral', 'negative'].includes(inSight.sentiment)
        ) ? inSight.sentiment as 'positive' | 'neutral' | 'negative' : 'neutral',
        emoji: typeof inSight?.emoji === 'string' ? inSight.emoji : '🤔',
        contextTags: Array.isArray(inSight?.contextTags) ? inSight.contextTags.filter((tag: any) => typeof tag === 'string') : [],
      },
      // Better fastTake handling - don't show "pending" if it's actually available
      fastTake: fastTake && typeof fastTake === 'string' ? fastTake : null,
      tags: postTags || [],
      categories: postCategories || [],
      // Thread-specific fields
      isThread: postToView.isThread,
      threadId: postToView.threadId,
      threadPosition: postToView.threadPosition,
      threadLength: postToView.threadLength,
    };

    return result;
  };

  // --- MODIFIED: handleOpenPostDetails to use PostViewerFullScreen ---
  const handleOpenPostDetails = (postId: string) => {
    // First try to find in processedPosts (which includes search results), then fall back to posts
    const postToView = processedPosts.find(p => p.id === postId) || posts.find(p => p.id === postId);
    if (postToView) {
      // Check if this is a thread post
      if (postToView.isThread && postToView.threadId) {
        // Find all posts in the same thread
        const threadPosts = processedPosts.filter(p => p.threadId === postToView.threadId);

        // Sort thread posts by position
        const sortedThreadPosts = threadPosts.sort((a, b) =>
          (a.threadPosition || 0) - (b.threadPosition || 0)
        );

        // Find the index of the current post in the thread
        const currentIndex = sortedThreadPosts.findIndex(p => p.id === postId);

        // Convert thread posts to PostWithAIData format
        const threadPostsForViewer = sortedThreadPosts.map(post => convertPostToViewerFormat(post));

        // Set thread navigation state
        setCurrentThreadPosts(threadPostsForViewer);
        setCurrentThreadIndex(currentIndex >= 0 ? currentIndex : 0);
        setSelectedPostForViewer(threadPostsForViewer[currentIndex >= 0 ? currentIndex : 0]);

        return;
      }

      // For non-thread posts, use the converter function
      const postForViewer = convertPostToViewerFormat(postToView);

      // Clear thread state for individual posts
      setCurrentThreadPosts(null);
      setCurrentThreadIndex(0);
      setSelectedPostForViewer(postForViewer);
    } else {
      console.warn(`[Dashboard] Post with ID ${postId} not found for viewer.`);
    }
  };
  // --- END MODIFIED handleOpenPostDetails ---

  // --- NEW HANDLERS FOR POST VIEWER ---
  const handleClosePostViewer = () => {
    setSelectedPostForViewer(null);
    setCurrentThreadPosts(null);
    setCurrentThreadIndex(0);
  };

  // Thread navigation handlers
  const handleThreadNavigate = (index: number) => {
    setCurrentThreadIndex(index);
    if (currentThreadPosts && currentThreadPosts[index]) {
      setSelectedPostForViewer(currentThreadPosts[index]);
    }
  };

  const handleAddCategoryToPost = (postId: string, category: string) => {
    // Find the post and update its categories
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedCategories = Array.from(new Set([...(postToUpdate.categories || []), category]));
      handleUpdatePostDetails(postId, { categories: updatedCategories });
    }
  };

  const handleAddTagToPost = (postId: string, tag: string) => {
    // Find the post and update its tags
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedTags = Array.from(new Set([...(postToUpdate.tags || []), tag]));
      handleUpdatePostDetails(postId, { tags: updatedTags });
    }
  };

  const handleRemoveTagFromPost = (postId: string, tag: string) => {
    // Find the post and update its tags
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedTags = (postToUpdate.tags || []).filter(t => t !== tag);
      handleUpdatePostDetails(postId, { tags: updatedTags });
    }
  };

  const handleRemoveCategoryFromPost = (postId: string, category: string) => {
    // Find the post and update its categories
    const postToUpdate = posts.find(p => p.id === postId);
    if (postToUpdate) {
      const updatedCategories = (postToUpdate.categories || []).filter(c => c !== category);
      handleUpdatePostDetails(postId, { categories: updatedCategories });
    }
  };

  // New handlers for bulk updates
  const handleUpdateCategories = (postId: string, categories: string[]) => {
    handleUpdatePostDetails(postId, { categories });
  };

  const handleUpdateTags = (postId: string, tags: string[]) => {
    handleUpdatePostDetails(postId, { tags });
  };

  const handleUpdateNotes = (postId: string, notes: string) => {
    handleUpdatePostDetails(postId, { notes });
  };

  // --- END NEW HANDLERS ---



  // Show delete confirmation dialog
  const showDeleteConfirmation = (postId: string) => {
    setPostToDelete(postId);
    setIsDeleteConfirmOpen(true);
  };

  const handleDeletePost = (postId: string) => {
    showDeleteConfirmation(postId);
  };

  // Cancel delete
  const cancelDelete = () => {
    setIsDeleteConfirmOpen(false);
    setPostToDelete(null);
  };

  // Confirm delete
  const confirmDelete = async () => {
    const postId = postToDelete;
    if (!postId) return;

    setIsDeleteConfirmOpen(false);
    setPostToDelete(null);
    try {
      // Use direct storage API to delete the post instead of messaging
      try {
        // First, try to delete directly using the storage API
        await deletePostDirectly(postId);

        // After successful local deletion, try to delete from cloud if user is logged in
        const tokenResult = await new Promise<{ token?: string, authToken?: string }>(resolve =>
          chrome.storage.local.get(['token', 'authToken'], result => resolve(result as { token?: string, authToken?: string }))
        );
        const currentToken = tokenResult?.authToken || tokenResult?.token;

        if (currentToken) {
          // Find the post to get the correct MongoDB _id for cloud deletion
          const postToDelete = posts.find(p => p.id === postId);
          const cloudPostId = postToDelete?._id || postToDelete?.id || postId;

          // Use a Promise with timeout for cloud deletion
          try {
            const cloudDeletePromise = new Promise<void>((resolve, _reject) => {
              chrome.runtime.sendMessage(
                {
                  action: 'DELETE_POST_FROM_CLOUD',
                  postId: cloudPostId,
                  token: currentToken
                },
                (response) => {
                  if (chrome.runtime.lastError) {
                    console.warn('[Dashboard] Cloud deletion error:', chrome.runtime.lastError.message);
                    resolve(); // Resolve anyway since it's just cloud cleanup
                  } else if (response?.status === 'success') {
                    resolve();
                  } else {
                    console.warn('[Dashboard] Cloud deletion failed:', response);
                    resolve(); // Resolve anyway since it's just cloud cleanup
                  }
                }
              );
            });

            // Set a timeout for cloud deletion (3 seconds)
            const timeoutPromise = new Promise<void>((resolve) => {
              setTimeout(() => {
                resolve();
              }, 3000);
            });

            // Wait for either cloud deletion to complete or timeout
            await Promise.race([cloudDeletePromise, timeoutPromise]);
          } catch (cloudError) {
            console.warn('[Dashboard] Error in cloud deletion:', cloudError);
            // Continue anyway since local deletion was successful
          }
        }

        // Refresh posts after deletion
        refreshPosts();
      } catch (directDeleteError) {
        console.error('[Dashboard] Error in direct deletion:', directDeleteError);

        // Fallback to background script if direct deletion fails
        try {
          await new Promise<void>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
              console.warn('[Dashboard] Delete message timed out, continuing anyway');
              resolve(); // Resolve anyway to prevent hanging
            }, 3000);

            chrome.runtime.sendMessage(
              {
                action: 'DELETE_POST_LOCALLY',
                postId: postId
              },
              (response) => {
                clearTimeout(timeoutId);
                if (chrome.runtime.lastError) {
                  console.error('[Dashboard] Fallback delete error:', chrome.runtime.lastError.message);
                  reject(new Error(chrome.runtime.lastError.message));
                } else if (response?.status === 'success') {
                  resolve();
                } else {
                  console.error('[Dashboard] Fallback delete failed:', response);
                  reject(new Error(response?.message || 'Unknown error'));
                }
              }
            );
          });

          // If we get here, the fallback was successful
          refreshPosts();
        } catch (fallbackError) {
          console.error('[Dashboard] Fallback delete failed:', fallbackError);
          alert(`Failed to delete post: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`);
          refreshPosts();
        }
      }
    } catch (error) {
      console.error('[Dashboard] handleDeletePost: General error:', error);
      alert(`An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
      refreshPosts(); // Refresh to ensure UI consistency even on error
    }
  };

  // Helper function to delete a post directly using the storage API
  const deletePostDirectly = async (postId: string): Promise<void> => {
    // Get posts from sync storage
    const syncResult = await new Promise<{savedPosts?: any[]}>(resolve => {
      chrome.storage.sync.get('savedPosts', result => resolve(result as {savedPosts?: any[]}));
    });

    // Get posts from local storage
    const localResult = await new Promise<{localSavedPosts?: any[]}>(resolve => {
      chrome.storage.local.get('localSavedPosts', result => resolve(result as {localSavedPosts?: any[]}));
    });

    let syncPosts = syncResult.savedPosts || [];
    let localPosts = localResult.localSavedPosts || [];

    // Filter out the post to delete
    const newSyncPosts = syncPosts.filter(post => post.id !== postId);
    const newLocalPosts = localPosts.filter(post => post.id !== postId);

    // Check if any posts were removed
    const syncRemoved = newSyncPosts.length < syncPosts.length;
    const localRemoved = newLocalPosts.length < localPosts.length;

    // Save the updated posts back to storage
    if (syncRemoved) {
      await new Promise<void>(resolve => {
        chrome.storage.sync.set({savedPosts: newSyncPosts}, () => resolve());
      });
    }

    if (localRemoved) {
      await new Promise<void>(resolve => {
        chrome.storage.local.set({localSavedPosts: newLocalPosts}, () => resolve());
      });
    }

    if (!syncRemoved && !localRemoved) {
      console.warn(`[Dashboard] Post ${postId} not found in storage`);
    }
  };

  useEffect(() => {
    let sortedPosts = [...posts];

    // Sort posts by timestamp or savedAt date
    sortedPosts.sort((a, b) => {
      const dateA = new Date(a.timestamp || a.savedAt || 0).getTime(); // Added fallback for safety
      const dateB = new Date(b.timestamp || b.savedAt || 0).getTime(); // Added fallback for safety
      // Handle potential NaN dates
      if (isNaN(dateA) && isNaN(dateB)) return 0;
      if (isNaN(dateA)) return 1; // Put posts without valid dates last
      if (isNaN(dateB)) return -1;
      return dateB - dateA; // Descending order
    });





    if (selectedPlatform !== 'All' && selectedPlatform !== 'Mindstream') {
      sortedPosts = sortedPosts.filter(post => {
        const postPlatformLower = post.platform?.toLowerCase();
        const selectedPlatformLower = selectedPlatform.toLowerCase();
        return postPlatformLower === selectedPlatformLower;
      });
    }

    // Filter by selected category
    if (selectedCategory) {
      sortedPosts = sortedPosts.filter(post =>
        post.categories && Array.isArray(post.categories) && post.categories.includes(selectedCategory)
      );
    }



    setFilteredPosts(sortedPosts);
  }, [posts, selectedPlatform, selectedCategory]);

  const processedPosts = useMemo(() => {
    // If we have search results and a search query, use search results
    if (searchQuery.trim() && searchResults.length > 0) {
      return searchResults;
    }
    // Otherwise use filtered posts
    return filteredPosts;
  }, [filteredPosts, searchQuery, searchResults]);

  // Group posts by threads and display threads as single cards
  const postsForDisplay = useMemo(() => {

    const threadGroups = new Map<string, Post[]>();
    const nonThreadPosts: Post[] = [];

    // Group posts by threadId
    processedPosts.forEach(post => {
      if (post.isThread && post.threadId) {
        if (!threadGroups.has(post.threadId)) {
          threadGroups.set(post.threadId, []);
        }
        threadGroups.get(post.threadId)!.push(post);
      } else {
        nonThreadPosts.push(post);
      }
    });

    // Sort thread posts by position
    threadGroups.forEach(posts => {
      posts.sort((a, b) => (a.threadPosition || 0) - (b.threadPosition || 0));
    });

    const result: Array<{ type: 'post' | 'thread', data: Post | Post[] }> = [];

    // Add non-thread posts
    nonThreadPosts.forEach(post => {
      result.push({ type: 'post', data: post });
    });

    // Add thread groups
    threadGroups.forEach(posts => {
      result.push({ type: 'thread', data: posts });
    });

    return result;
  }, [processedPosts]);

  // Restore scroll position after all filtering and processing effects complete
  // This runs after postsForDisplay has been updated (the final rendered data)
  useEffect(() => {
    const restore = () => {
      if (typeof prevScrollYRef.current === 'number') {
        const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
        const targetScroll = Math.min(prevScrollYRef.current, Math.max(0, maxScroll));
        window.scrollTo({ top: targetScroll, behavior: 'auto' });
      }
    };

    // Wait for DOM to settle after all filtering effects complete
    requestAnimationFrame(() => {
      restore();
      // Additional attempts to handle any remaining layout shifts
      setTimeout(restore, 50);
      setTimeout(restore, 150);
      setTimeout(restore, 300); // Extra attempt for complex layouts
    });
  }, [postsForDisplay]); // Watch the final display data that actually renders

  const handleOpenLoginModal = () => {
    sessionStorage.setItem('acceptGoogleAuth', 'true');
    localStorage.setItem('acceptingAuthMessages', 'true');
    setIsLoginModalOpen(true);
  };

  const handleCloseLoginModal = () => {
    if (!isLoggedIn) {
      sessionStorage.removeItem('acceptGoogleAuth');
      localStorage.removeItem('acceptingAuthMessages');
    }
    setIsLoginModalOpen(false);
  };

  const handleLogout = async () => {
    sessionStorage.removeItem('acceptGoogleAuth');
    localStorage.removeItem('acceptingAuthMessages');
    await new Promise<void>(resolve => 
      chrome.storage.local.remove(['token', 'authToken', 'userInfo', 'user_profile'], () => resolve())
    );
    setIsLoggedIn(false);
    setLoggedInUser(null);
    await loadInitialData(); // Reload data, which will now fetch local posts
  };

  const handleLoginSuccess = (token: string, _user: any) => { // User param no longer needed from modal
    chrome.storage.local.set({ token: token, authToken: token, userInfo: {} /* Clear old userInfo, /auth/me is source of truth */ }, () => {
      // The storage listener (authChanged) will trigger loadInitialData.
      // Call sync after setting token.
      initiateLocalToCloudSync();
    });
    setIsLoginModalOpen(false);
  };

  const handleUpgradeToPremium = () => {
    if (!loggedInUser) {
      console.error("User not logged in, cannot upgrade.");
      return;
    }

    try {
      // Import upgrade utilities dynamically to avoid circular dependencies
      import('../utils/upgradeUtils').then(({ navigateToPlanChooser, UPGRADE_SOURCES }) => {
        navigateToPlanChooser(UPGRADE_SOURCES.DASHBOARD_CTA);
      });
    } catch (error) {
      console.error('Error navigating to plan chooser:', error);
      // Fallback to settings page
      window.location.href = 'settings.html?selectPlan=1';
    }
  };

  // initiateLocalToCloudSync is now defined earlier in the file

  return (
    <div className="h-screen flex flex-col bg-notely-bg overflow-hidden transition-all duration-300 ease-out animate-fadeIn">
      <header className="sticky top-0 z-50 bg-notely-surface/95 backdrop-blur-md shadow-notely-lg transition-all duration-300 ease-out animate-slideInDown">
        {/* Top header with logo and user info */}
        <div className="notely-breathing-lg flex justify-between items-center">
          <div className="flex items-center">
            <img
              src="/notely.svg"
              alt="Notely"
              className="h-10 w-auto transition-all duration-300 ease-out hover:scale-110 active:scale-95 cursor-pointer notely-logo-light"
              title="Notely – Your Smart Post Saver"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Save current scroll position so we can restore after the DOM updates
                prevScrollYRef.current = window.scrollY;
                setSelectedPlatform('All');
                setSelectedCategory(null);
                setSearchQuery('');
              }}
            />
            <img
              src="/notely-dark.svg"
              alt="Notely"
              className="h-10 w-auto transition-all duration-300 ease-out hover:scale-110 active:scale-95 cursor-pointer notely-logo-dark"
              title="Notely – Your Smart Post Saver"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Save current scroll position so we can restore after the DOM updates
                prevScrollYRef.current = window.scrollY;
                setSelectedPlatform('All');
                setSelectedCategory(null);
                setSearchQuery('');
              }}
            />
          </div>

          {/* Search Input - Center */}
          <div className="flex-1 max-w-lg mx-8">
            <div className="relative group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                {isSearching ? (
                  <svg className="h-4 w-4 text-blue-400 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg className="h-4 w-4 text-notely-text-muted group-focus-within:text-notely-text-secondary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                )}
              </div>
              <input
                type="text"
                placeholder={isLoggedIn ? t('dashboard.searchPlaceholder') : t('dashboard.searchPlaceholderLoggedOut')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                disabled={!isLoggedIn}
                className={`w-full h-10 pl-11 pr-4 backdrop-blur-sm border rounded-full text-sm focus:outline-none transition-all duration-200 ease-out notely-search-input ${
                  !isLoggedIn ? 'cursor-not-allowed bg-notely-surface/60 text-notely-text-muted border-notely-border/50' : 'focus:scale-105 hover:shadow-lg'
                } ${
                  searchQuery.trim() && searchResults.length > 0 ? 'ring-2 ring-blue-500/30' : ''
                }`}
              />
              {searchError && (
                <div className="absolute top-full left-0 right-0 mt-2 p-2 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-xs">
                  {searchError}
                </div>
              )}
            </div>
            {/* Debug Button (only in development) */}
            {process.env.NODE_ENV === 'development' && (
              <button
                onClick={debugSearchData}
                className="ml-2 px-3 py-1 text-xs bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 rounded-md hover:bg-yellow-500/30 transition-all duration-200 ease-out hover:scale-105 active:scale-95"
                title="Debug search data"
              >
                Debug
              </button>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <ThemeToggle className="hidden sm:flex" />

            {isUserDataLoading && (
              <div className="flex items-center space-x-2 animate-fadeIn">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-notely-accent border-t-transparent"></div>
                <p className="text-sm text-notely-text-muted leading-relaxed">{t('dashboard.loading')}</p>
              </div>
            )}
            {!isUserDataLoading && isLoggedIn && loggedInUser ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-semibold text-notely-text-primary">
                    {loggedInUser.name}
                  </p>
                  <a href="settings.html" title={t('settings.title')}>
                    <SettingsIcon className="w-4 h-4 text-notely-text-tertiary hover:text-notely-accent transition-all duration-200 ease-out hover:scale-110 hover:rotate-90 active:scale-95" />
                  </a>
                  {loggedInUser.plan === 'premium' ? (
                    <PremiumBadge
                      plan={loggedInUser.plan}
                      subscriptionStatus={loggedInUser.subscriptionStatus}
                      size="sm"
                    />
                  ) : (
                    <button
                      onClick={handleUpgradeToPremium}
                      className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full hover:from-purple-600 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 ease-out hover:scale-110 active:scale-95 hover:shadow-lg"
                      style={{ minWidth: '120px' }}
                    >
                      {t('premium.upgrade')}
                    </button>
                  )}
                  <button
                    onClick={handleLogout}
                    className="bg-red-500 hover:bg-red-600 text-white font-medium text-sm px-4 py-2 rounded-lg transition-all duration-200 ease-out hover:scale-110 active:scale-95 hover:shadow-lg"
                  >
                    {t('auth.logout')}
                  </button>
                </div>
              </div>
            ) : !isUserDataLoading && (
              <div className="flex items-center space-x-4 notely-stagger-item">
                <a href="settings.html" className="mr-2" title={t('settings.title')}>
                  <SettingsIcon className="w-4 h-4 text-notely-text-tertiary hover:text-notely-accent transition-all duration-200 ease-out hover:scale-110 hover:rotate-90 active:scale-95" />
                </a>
                <button
                  onClick={handleOpenLoginModal}
                  className="notely-btn-primary text-sm font-medium transition-all duration-200 ease-out hover:scale-110 active:scale-95 hover:shadow-lg"
                >
                  {t('auth.login')}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Two-Layer Header Structure */}
        {/* Top Layer: Main Navigation */}
        <div className="notely-breathing-lg bg-notely-surface">
          <div className="flex flex-wrap gap-2 mb-4 justify-center md:justify-start">
            <PlatformSelector
              onSelect={(platform) => {
                // Save current scroll position so we can restore after the DOM updates
                prevScrollYRef.current = window.scrollY;
                setSelectedPlatform(platform);
              }}
              selectedPlatform={selectedPlatform}
              enabledPlatforms={enabledPlatforms}
            />
            <div className="flex items-center gap-2 text-sm text-notely-text-secondary">
              {selectedCategory && (
                <span className="px-2 py-1 bg-notely-surface rounded-full text-xs">
                  📁 {formatForDisplay(selectedCategory)}
                </span>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="flex-1 overflow-y-auto">
        {/* Category and Tag Filters - Positioned just above posts */}
        {selectedPlatform !== 'Mindstream' && (
          <div className="bg-notely-surface transition-all duration-300 ease-out animate-fadeIn">
            {/* Category Selector - Show when we have categories */}
            {availableCategories.length > 0 && (
              <SimpleCategorySelector
                categories={availableCategories}
                selectedCategory={selectedCategory}
                onCategorySelect={(category) => {
                  // Save current scroll position so we can restore after the DOM updates
                  prevScrollYRef.current = window.scrollY;
                  setSelectedCategory(category);
                }}
                categoryCounts={categoryPostCounts}
              />
            )}

          </div>
        )}



        <div className="max-w-full bg-notely-bg px-6 py-8">
          {/* Main container with conditional left sidebar */}
          <div className="w-full flex flex-col lg:flex-row gap-6">
            {/* Left Sidebar with Daily Wisdom and Storage - Only visible when no category is selected */}
            {!selectedCategory && (
              <div className="w-full lg:w-1/5 space-y-4">
                {/* Daily Wisdom Card */}
                <DailyWisdom
                  className="w-full"
                  onQuoteClick={(quote) => {
                    // Convert wisdom quote to post format and display in PostViewerFullScreen
                    const postData = convertWisdomQuoteToPost(quote);
                    setSelectedPostForViewer(postData as PostWithAIData);
                  }}
                  onOpenPost={(post) => {
                    // Convert raw Post to PostWithAIData format for proper display
                    const postForViewer = convertPostToViewerFormat(post);
                    setSelectedPostForViewer(postForViewer);
                  }}
                />

                {/* Storage Usage Card */}
                {isLoggedIn && (
                  <StorageUsage
                    className="w-full animate-fadeIn"
                  />
                )}

                {/* Conditionally render Tags & Categories for Mindstream view */}
                {selectedPlatform === 'Mindstream' && (
                  <div className="notely-card bg-notely-card rounded-notely-lg shadow-notely-sm hover:shadow-notely-md transition-all duration-200 px-4 py-4 overflow-hidden animate-fadeIn border border-notely-border/10 dark:border-notely-border-dark/20">
                    <TagsCategoriesWidget posts={posts} />
                  </div>
                )}
              </div>
            )}

            {/* Main Content Area - Full width when category selected */}
            <div className={`w-full ${!selectedCategory ? 'lg:w-4/5' : ''}`}>
              {selectedPlatform === 'Mindstream' ? (
                // Mindstream View - Widgets take full width
                <div className="w-full">
                  <MindstreamWidgets
                    posts={processedPosts}
                    onOpenPost={(postId) => {
                      // Use the same handleOpenPostDetails as regular posts
                      handleOpenPostDetails(postId);
                    }}
                  />
                </div>
              ) : selectedCategory ? (
                // Split-screen view: Category summary on left, posts on right
                <div className="w-full flex flex-col lg:flex-row gap-6 h-full">
                  {/* Left Pane: Category Summary and Chat - Wider for better content display */}
                  <div className="w-full lg:w-2/5 space-y-6">
                    <CategorySummary
                      category={selectedCategory}
                      posts={filteredPosts}
                      className=""
                    />

                    <CategoryChatWithPosts
                      posts={filteredPosts}
                      category={selectedCategory}
                      onOpenPost={(postId) => {
                        // Use the same handleOpenPostDetails as regular posts
                        handleOpenPostDetails(postId);
                      }}
                    />
                  </div>

                  {/* Right Pane: Posts - Fill remaining space completely */}
                  <div className="w-full lg:w-3/5 h-full">
                    <div className="mb-4">
                      <h2 className="text-xl font-semibold text-notely-dark-text-primary mb-2 notely-heading flex items-center gap-2">
                        {formatForDisplay(selectedCategory)} Posts
                      </h2>
                    </div>
                    <div className="masonry-grid masonry-grid-category-view" style={{ columnCount: 2, columnGap: '1rem' }}>
                      {postsForDisplay.map((item, index) => {
                        if (item.type === 'thread') {
                          // Thread group - show only the first post with thread indicator
                          const threadPosts = item.data as Post[];
                          const firstPost = threadPosts[0];
                          if (!firstPost) return null;
                          
                          return (
                            <div className="masonry-item animate-fadeIn" key={`thread-${firstPost.threadId}-${index}`} style={{ animationDelay: `${index * 100}ms` }}>
                              <ThreadCard
                                threadPosts={threadPosts}
                                firstPost={firstPost}
                                onDelete={handleDeletePost}
                                onOpenDetails={() => handleOpenPostDetails(firstPost.id)}
                                t={t}
                              />
                            </div>
                          );
                        } else {
                          // Regular post
                          const post = item.data as Post;
                          return (
                            <div className="masonry-item animate-fadeIn" key={post.id} style={{ animationDelay: `${index * 100}ms` }}>
                              <PostCard
                                post={post}
                                onDelete={handleDeletePost}
                                onOpenDetails={() => handleOpenPostDetails(post.id)}
                                t={t}
                              />
                            </div>
                          );
                        }
                      })}
                    </div>
                  </div>
                </div>
              ) : postsForDisplay.length > 0 ? (
                // Regular view with posts and threads
                <div className="space-y-6">
                  <div className="masonry-grid">
                    {postsForDisplay.map((item, index) => {
                      if (item.type === 'thread') {
                        // Thread group - show only the first post with thread indicator
                        const threadPosts = item.data as Post[];
                        const firstPost = threadPosts[0];
                        if (!firstPost) return null;
                        
                        return (
                          <div className="masonry-item animate-fadeIn" key={`thread-${firstPost.threadId}-${index}`} style={{ animationDelay: `${index * 100}ms` }}>
                            <ThreadCard
                              threadPosts={threadPosts}
                              firstPost={firstPost}
                              onDelete={handleDeletePost}
                              onOpenDetails={() => handleOpenPostDetails(firstPost.id)}
                              t={t}
                            />
                          </div>
                        );
                      } else {
                        // Regular post
                        const post = item.data as Post;
                        return (
                          <div className="masonry-item animate-fadeIn" key={post.id} style={{ animationDelay: `${index * 100}ms` }}>
                            <PostCard
                              post={post}
                              onDelete={handleDeletePost}
                              onOpenDetails={() => handleOpenPostDetails(post.id)}
                              t={t}
                            />
                          </div>
                        );
                      }
                    })}
                  </div>
                </div>
              ) : (
                // No posts view
                <div className="text-center py-12">
                  <p className="text-lg text-notely-text-muted mb-4">{t('dashboard.noPosts')}</p>
                  {/* Only show install extension message in web environment */}
                  {!chrome?.runtime?.id && (
                    <p className="text-sm text-notely-text-tertiary">{t('dashboard.installExtension')}</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={handleCloseLoginModal}
        onLoginSuccess={handleLoginSuccess}
        preventAutoGoogleLogin={true}
      />

      {selectedPostForViewer && (
        <PostViewerFullScreen
          post={selectedPostForViewer}
          onClose={handleClosePostViewer}
          onAddCategory={handleAddCategoryToPost}
          onAddTag={handleAddTagToPost}
          onRemoveTag={handleRemoveTagFromPost}
          onRemoveCategory={handleRemoveCategoryFromPost}
          onUpdateNotes={handleUpdateNotes}
          onUpdateCategories={handleUpdateCategories}
          onUpdateTags={handleUpdateTags}
          threadPosts={currentThreadPosts || undefined}
          currentThreadIndex={currentThreadIndex}
          onThreadNavigate={handleThreadNavigate}
        />
      )}

      {/* Delete Confirmation Modal with blurred background */}
      {isDeleteConfirmOpen && (
        <div className="notely-modal-overlay fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="notely-modal-content open notely-card bg-notely-card rounded-notely-lg shadow-notely-md hover:shadow-notely-lg px-6 py-4 w-full max-w-md mx-auto border border-[#2F2F2F]/10 transition-all duration-300 ease-out animate-scaleIn">
            <h3 className="text-xl font-semibold leading-tight mb-3 text-notely-text-primary">{t('dashboard.deletePostTitle')}</h3>
            <p className="text-sm text-notely-text-muted leading-relaxed mb-6">{t('dashboard.deleteConfirmation')}</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                className="notely-btn-secondary px-4 py-2 text-sm font-medium transition-all duration-200 ease-out hover:scale-110 active:scale-95 border-none shadow-notely-sm hover:shadow-notely-md"
              >
                {t('dashboard.cancel')}
              </button>
              <button
                onClick={confirmDelete}
                className="notely-btn-danger px-4 py-2 text-sm font-medium transition-all duration-200 ease-out hover:scale-110 active:scale-95 border-none shadow-notely-sm hover:shadow-notely-md"
              >
                {t('dashboard.deletePostTitle')}
              </button>
            </div>
          </div>
        </div>
      )}
      <div className="p-4 border-t border-gray-700">
        <p className="text-sm text-gray-500">
          © 2025 Notely. All rights reserved. 
          <button onClick={() => {
            chrome.tabs.create({ url: 'settings.html' });
          }} className="ml-4 text-blue-400 hover:text-blue-300">Settings</button>
        </p>
      </div>
    </div>
  );
}  // End of DashboardContent

// Main Dashboard wrapper with LocaleProvider
function Dashboard() {
  return (
    <LocaleProvider>
      <DashboardContent />
    </LocaleProvider>
  );
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Dashboard />
  </React.StrictMode>,
);
