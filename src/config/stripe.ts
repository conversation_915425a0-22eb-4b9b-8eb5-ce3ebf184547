/**
 * Stripe Configuration
 * 
 * This file contains Stripe-related configuration including price IDs.
 * Update these values with your actual Stripe price IDs from your Stripe dashboard.
 */

export const STRIPE_CONFIG = {
  // Test Price IDs (matching Railway production environment)
  TEST: {
    MONTHLY_PRICE_ID: 'price_1RqF9TRNuCe56Tb5vo78Cr0j', // Railway production monthly price ID
    YEARLY_PRICE_ID: 'price_1RqFe8RNuCe56Tb5vF2mvgzG',  // Railway production yearly price ID
  },

  // Production Price IDs (same as test for now since Railway is using test keys)
  LIVE: {
    MONTHLY_PRICE_ID: 'price_1RqF9TRNuCe56Tb5vo78Cr0j', // Railway production monthly price ID
    YEARLY_PRICE_ID: 'price_1RqFe8RNuCe56Tb5vF2mvgzG',   // Railway production yearly price ID
  }
};

/**
 * Get the appropriate price IDs based on environment
 */
export const getStripePriceIds = () => {
  // In a real extension, you might want to check if you're in development or production
  // For now, we'll use test IDs
  const isProduction = false; // Set this based on your environment detection
  
  return isProduction ? STRIPE_CONFIG.LIVE : STRIPE_CONFIG.TEST;
};

/**
 * Pricing information for display
 */
export const PRICING_INFO = {
  FREE: {
    name: 'Free',
    price: 0,
    features: [
      'Basic AI analysis',
      '1GB cloud storage',
      'Standard support'
    ]
  },
  MONTHLY: {
    name: 'Premium Monthly',
    price: 9.99,
    interval: 'month',
    features: [
      'Unlimited AI analysis',
      '10GB cloud storage',
      'Advanced content insights',
      'Priority support'
    ]
  },
  YEARLY: {
    name: 'Premium Yearly',
    price: 99.99,
    interval: 'year',
    features: [
      'Unlimited AI analysis',
      '10GB cloud storage',
      'Advanced content insights',
      'Priority support',
      '2 months free'
    ]
  }
};

/**
 * Get price ID for a specific plan
 */
export const getPriceId = (plan: 'monthly' | 'yearly'): string => {
  const priceIds = getStripePriceIds();
  const priceId = plan === 'monthly' ? priceIds.MONTHLY_PRICE_ID : priceIds.YEARLY_PRICE_ID;

  if (!priceId) {
    throw new Error(`Missing Stripe price ID for ${plan} plan. Please check your configuration.`);
  }

  return priceId;
};

/**
 * Plan configuration mapping
 */
export const PLANS = {
  monthly: 'monthly' as const,
  yearly: 'yearly' as const,
} as const;

export type PlanType = keyof typeof PLANS;

/**
 * Instructions for setting up Stripe price IDs:
 * 
 * 1. Go to your Stripe Dashboard (https://dashboard.stripe.com)
 * 2. Navigate to Products > Add Product
 * 3. Create a product called "Notely Premium"
 * 4. Add two prices:
 *    - Monthly: $9.99/month recurring
 *    - Yearly: $99.99/year recurring
 * 5. Copy the price IDs (they start with "price_") and update the values above
 * 6. Make sure to set up both test and live price IDs
 * 7. Update your backend environment variables:
 *    - STRIPE_PRICE_ID_MONTHLY
 *    - STRIPE_PRICE_ID_YEARLY
 */
