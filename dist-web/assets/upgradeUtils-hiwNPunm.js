var c=Object.defineProperty;var l=(r,e,t)=>e in r?c(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var n=(r,e,t)=>l(r,typeof e!="symbol"?e+"":e,t);class d{constructor(){n(this,"storageKey","notely_analytics_events");n(this,"maxEvents",100)}track(e,t){try{const a={event:e,properties:t,timestamp:Date.now()},o=this.getStoredEvents();o.push(a);const s=o.slice(-this.maxEvents);localStorage.setItem(this.storageKey,JSON.stringify(s))}catch(a){console.warn("Failed to store analytics event:",a)}}getStoredEvents(){try{const e=localStorage.getItem(this.storageKey);return e?JSON.parse(e):[]}catch(e){return console.warn("Failed to retrieve stored analytics events:",e),[]}}getEvents(){return this.getStoredEvents()}clearEvents(){try{localStorage.removeItem(this.storageKey)}catch(e){console.warn("Failed to clear analytics events:",e)}}}class h{constructor(){n(this,"providers",[]);this.initializeProviders()}initializeProviders(){this.providers.push(new d)}track(e,t={}){const a={...t,timestamp:Date.now(),userAgent:navigator.userAgent,url:window.location.href};this.providers.forEach(o=>{try{o.track(e,a)}catch(s){console.warn("Analytics provider failed:",s)}})}trackUpgradeFlowOpened(e){this.track("upgrade_flow_opened",{source:e})}trackUpgradePlanSelected(e,t){this.track("upgrade_plan_selected",{source:e,plan:t})}trackCheckoutStarted(e,t){this.track("checkout_started",{source:e,plan:t})}trackBillingPortalOpened(e){this.track("billing_portal_opened",{source:e})}addProvider(e){this.providers.push(e)}clearProviders(){this.providers=[]}}const g=new h,w={NAV_CTA:"nav_cta",DASHBOARD_CTA:"dashboard_cta",SETTINGS_HEADER:"settings_header",PRICING_CARD:"pricing_card",WEB_DASHBOARD:"web_dashboard"};let i={};const y=(r,e)=>{var o;g.trackUpgradeFlowOpened(r),(o=i.upgrade_flow_opened)==null||o.call(i,{source:r});const t=new URLSearchParams;t.set("selectPlan","1"),e&&t.set("plan",e);let a;if(window.location.hostname==="notely.social"||window.location.hostname.includes("notely")?a=`/dashboard/settings?${t.toString()}`:a=`/settings?${t.toString()}`,typeof window<"u"){if(window.location.hostname==="notely.social"||window.location.hostname.includes("notely"))try{const{useNavigate:s}=require("react-router-dom");s()(`/dashboard/settings?${t.toString()}`);return}catch{}window.location.href=a}};export{w as UPGRADE_SOURCES,y as navigateToPlanChooser};
