document.addEventListener('DOMContentLoaded', () => {
  const loginButton = document.getElementById('login-button');
  const statusElement = document.getElementById('status');
  
  // Check initial auth status
  checkAuthStatus();
  
  loginButton.addEventListener('click', async () => {
    try {
      statusElement.textContent = 'Logging in...';
      const response = await chrome.runtime.sendMessage({ type: 'LOGIN' });
      
      if (response.success) {
        statusElement.textContent = 'Logged in successfully!';
        loginButton.style.display = 'none';
        
        // Open dashboard after successful login
        chrome.tabs.create({ url: 'dashboard.html' });
        window.close(); // Close the popup
      } else {
        statusElement.textContent = `Login failed: ${response.error}`;
      }
    } catch (error) {
      statusElement.textContent = `Error: ${error.message}`;
    }
  });
  
  // Add dashboard button
  const dashboardButton = document.createElement('button');
  dashboardButton.id = 'dashboard-button';
  dashboardButton.textContent = 'Open Dashboard';
  dashboardButton.style.marginTop = '10px';
  dashboardButton.addEventListener('click', () => {
    chrome.tabs.create({ url: 'dashboard.html' });
    window.close(); // Close the popup
  });
  
  // Insert dashboard button after the login button
  loginButton.insertAdjacentElement('afterend', dashboardButton);
});

async function checkAuthStatus() {
  const loginButton = document.getElementById('login-button');
  const dashboardButton = document.getElementById('dashboard-button');
  const statusElement = document.getElementById('status');
  
  try {
    const status = await chrome.runtime.sendMessage({ type: 'GET_AUTH_STATUS' });
    
    if (status.isAuthenticated) {
      statusElement.textContent = 'Logged in!';
      loginButton.style.display = 'none';
      if (dashboardButton) {
        dashboardButton.style.display = 'block';
      }
    } else {
      statusElement.textContent = 'Not logged in';
      loginButton.style.display = 'block';
      if (dashboardButton) {
        dashboardButton.style.display = 'none';
      }
    }
  } catch (error) {
    statusElement.textContent = `Error checking status: ${error.message}`;
  }
} 