<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Toggle Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        /* Theme variables - no default in :root */
        :root {
            /* No default theme variables */
        }
        
        /* Dark theme */
        .dark {
            --bg-color: #0f0f0f;
            --text-color: #ffffff;
            --card-bg: #252525;
        }
        
        /* Light theme */
        .light-theme {
            --bg-color: #ffffff;
            --text-color: #000000;
            --card-bg: #f8f8f8;
        }
        
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
        }
        
        .card {
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #74c0fc;
            color: white;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #5ba7e8;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: var(--card-bg);
        }
    </style>
</head>
<body>
    <h1>Theme Toggle Test</h1>
    
    <div class="card">
        <h2>Current Theme Status</h2>
        <div class="status" id="themeStatus">Loading...</div>
        <button onclick="toggleTheme()">Toggle Theme</button>
        <button onclick="checkTheme()">Check Current Theme</button>
        <button onclick="debugTheme()">Debug CSS Variables</button>
    </div>
    
    <div class="card">
        <h2>Test Instructions</h2>
        <ol>
            <li>Click "Toggle Theme" to switch between light and dark modes</li>
            <li>The theme should persist and not be overridden by system preferences</li>
            <li>Refresh the page - the theme should remain as you set it</li>
            <li>Check browser console for any theme-related logs</li>
        </ol>
    </div>

    <script>
        // Mock Chrome storage for testing
        if (typeof chrome === 'undefined') {
            window.chrome = {
                storage: {
                    local: {
                        get: function(keys, callback) {
                            const stored = localStorage.getItem('theme');
                            callback({ theme: stored });
                        },
                        set: function(data, callback) {
                            if (data.theme) {
                                localStorage.setItem('theme', data.theme);
                            }
                            if (callback) callback();
                        }
                    }
                }
            };
        }

        function applyTheme(theme) {
            const root = document.documentElement;
            
            // Remove all theme classes first
            root.classList.remove('dark', 'light-theme');
            
            // Apply the selected theme class
            if (theme === 'dark') {
                root.classList.add('dark');
            } else {
                root.classList.add('light-theme');
            }
            
            // Store theme preference
            chrome.storage.local.set({ theme: theme });
            updateStatus(`Applied ${theme} theme - Classes: ${root.classList.toString()}`);
            console.log(`Applied theme: ${theme}, classes:`, root.classList.toString());
        }

        function getCurrentTheme() {
            return new Promise((resolve) => {
                chrome.storage.local.get(['theme'], (result) => {
                    const storedTheme = result.theme;
                    if (storedTheme === 'dark' || storedTheme === 'light') {
                        resolve(storedTheme);
                    } else {
                        // No stored preference, check DOM
                        if (document.documentElement.classList.contains('light-theme')) {
                            resolve('light');
                        } else {
                            resolve('dark');
                        }
                    }
                });
            });
        }

        function toggleTheme() {
            getCurrentTheme().then((currentTheme) => {
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                applyTheme(newTheme);
                console.log(`Toggled from ${currentTheme} to ${newTheme}`);
            });
        }

        function checkTheme() {
            getCurrentTheme().then((theme) => {
                updateStatus(`Current theme: ${theme}`);
                console.log(`Current theme: ${theme}`);
            });
        }

        function updateStatus(message) {
            document.getElementById('themeStatus').textContent = message;
        }

        function debugTheme() {
            const root = document.documentElement;
            const computedStyle = getComputedStyle(root);
            const bgColor = computedStyle.getPropertyValue('--bg-color');
            const textColor = computedStyle.getPropertyValue('--text-color');
            const cardBg = computedStyle.getPropertyValue('--card-bg');
            
            console.log('CSS Variables:', {
                '--bg-color': bgColor,
                '--text-color': textColor,
                '--card-bg': cardBg
            });
            
            const bodyStyle = getComputedStyle(document.body);
            console.log('Body computed styles:', {
                backgroundColor: bodyStyle.backgroundColor,
                color: bodyStyle.color
            });
            
            updateStatus(`CSS Vars: bg=${bgColor}, text=${textColor}, card=${cardBg}`);
        }

        // Initialize theme on page load
        getCurrentTheme().then((theme) => {
            applyTheme(theme);
            updateStatus(`Initialized with ${theme} theme`);
        });
    </script>
</body>
</html>