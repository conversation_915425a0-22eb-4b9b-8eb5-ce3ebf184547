# Design Document

## Overview

This design document outlines the UI improvements for the Notely dashboard category tags to enhance visual clarity, contrast, and usability across both dark and light modes. The solution focuses on minimal but effective changes that leverage the existing <PERSON>ly design system while addressing specific contrast and readability issues.

## Architecture

The improvements will be implemented through modifications to the `SimpleCategorySelector` component, which is responsible for rendering category tags under the platform tabs. The design maintains the existing component structure while enhancing the styling system to provide better visual hierarchy and accessibility.

### Current System Analysis

Based on code analysis, the current implementation:
- Uses a gradient background (`bg-gradient-to-r from-purple-500 to-indigo-500`) for selected states
- Applies basic Tailwind classes for unselected states
- Has inconsistent contrast ratios in both light and dark modes
- Uses varying padding and sizing across different states

## Components and Interfaces

### SimpleCategorySelector Component Enhancement

The primary component requiring modification is `src/components/SimpleCategorySelector.tsx`. The design will enhance the `getButtonStyles` function to provide improved contrast and consistency.

#### Enhanced Button Styling System

```typescript
interface CategoryTagStyles {
  base: string;
  selected: {
    dark: string;
    light: string;
  };
  unselected: {
    dark: string;
    light: string;
  };
  hover: {
    dark: string;
    light: string;
  };
}
```

#### Count Badge Styling System

```typescript
interface CountBadgeStyles {
  dark: {
    selected: string;
    unselected: string;
  };
  light: {
    selected: string;
    unselected: string;
  };
}
```

## Data Models

### Style Configuration Object

```typescript
const categoryTagConfig = {
  // Unified base styles for all states
  baseStyles: 'text-sm font-medium rounded-full px-3 py-1.5 transition-all duration-200 focus:outline-none relative hover:scale-105',
  
  // Dark mode styles
  dark: {
    selected: 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg hover:shadow-purple-500/25 hover:from-purple-600 hover:to-indigo-600',
    unselected: 'bg-zinc-800/60 text-zinc-300 border border-zinc-700/50 hover:bg-zinc-700/60 hover:text-zinc-200 hover:border-zinc-600/50',
    countBadge: {
      selected: 'bg-black/20 text-white/90',
      unselected: 'bg-zinc-700/60 text-zinc-300'
    }
  },
  
  // Light mode styles  
  light: {
    selected: 'bg-zinc-200 text-zinc-800 border border-zinc-300 shadow-sm hover:bg-zinc-300 hover:shadow-md',
    unselected: 'bg-white text-zinc-600 border border-zinc-200 hover:bg-zinc-50 hover:text-zinc-800 hover:border-zinc-300',
    countBadge: {
      selected: 'bg-zinc-300/80 text-zinc-700',
      unselected: 'bg-zinc-100 text-zinc-600'
    }
  }
};
```

## Error Handling

### Theme Detection

The component will implement robust theme detection to ensure proper styling application:

```typescript
const isDarkMode = document.documentElement.classList.contains('dark') || 
                   document.body.classList.contains('dark-theme');
```

### Fallback Styling

If theme detection fails, the component will default to dark mode styling to maintain consistency with the Notely dark-first approach.

### Accessibility Compliance

- Minimum contrast ratio of 4.5:1 for normal text
- Minimum contrast ratio of 3:1 for large text
- Focus indicators that meet WCAG 2.1 AA standards
- Proper ARIA labels for screen readers

## Testing Strategy

### Visual Regression Testing

1. **Dark Mode Contrast Testing**
   - Verify category tag numbers are clearly visible
   - Test hover states maintain readability
   - Validate count badges have sufficient contrast

2. **Light Mode Contrast Testing**
   - Ensure selected tags have clear distinction from unselected
   - Test background/text contrast ratios
   - Verify border visibility and clarity

3. **Responsive Testing**
   - Test tag truncation on mobile devices
   - Verify consistent spacing across screen sizes
   - Validate touch target sizes meet accessibility guidelines

4. **Cross-Browser Testing**
   - Test in Chrome, Firefox, Safari, and Edge
   - Verify gradient rendering consistency
   - Test transition animations performance

### Accessibility Testing

1. **Screen Reader Testing**
   - Test with NVDA, JAWS, and VoiceOver
   - Verify proper announcement of selected/unselected states
   - Test keyboard navigation flow

2. **Keyboard Navigation Testing**
   - Tab order follows logical sequence
   - Focus indicators are clearly visible
   - Enter/Space keys activate tags properly

3. **Color Contrast Testing**
   - Use tools like WebAIM Contrast Checker
   - Test all color combinations meet WCAG standards
   - Verify readability for color-blind users

### Implementation Testing

1. **Unit Tests**
   - Test theme detection logic
   - Verify style application based on props
   - Test count badge rendering logic

2. **Integration Tests**
   - Test component behavior within dashboard context
   - Verify theme switching functionality
   - Test interaction with parent components

3. **Performance Tests**
   - Measure rendering performance with large category lists
   - Test transition animation smoothness
   - Verify memory usage during state changes

## Implementation Approach

### Phase 1: Core Styling Updates
- Update `getButtonStyles` function with new contrast-aware logic
- Implement theme detection mechanism
- Apply unified padding and sizing

### Phase 2: Count Badge Enhancement
- Improve count badge contrast in both themes
- Ensure proper spacing and alignment
- Add hover state improvements

### Phase 3: Accessibility Improvements
- Add proper ARIA attributes
- Enhance focus indicators
- Implement keyboard navigation improvements

### Phase 4: Testing and Refinement
- Conduct comprehensive testing across all scenarios
- Gather user feedback on readability improvements
- Fine-tune colors and spacing based on results

## Design Decisions and Rationales

### Color Choices

**Dark Mode:**
- Selected: Maintains existing purple gradient for brand consistency
- Unselected: Uses `zinc-800/60` background with `zinc-300` text for optimal contrast
- Count badges: Uses semi-transparent backgrounds to maintain hierarchy

**Light Mode:**
- Selected: Uses `zinc-200` background with `zinc-800` text for maximum contrast
- Unselected: Uses white background with subtle borders for clean appearance
- Count badges: Uses lighter zinc tones to maintain readability without overwhelming

### Typography and Spacing

- **Font Size:** Standardized to `text-sm` (14px) for consistency
- **Padding:** Unified to `px-3 py-1.5` for proper touch targets
- **Border Radius:** Uses `rounded-full` for modern pill appearance
- **Transitions:** 200ms duration for smooth but responsive feel

### Accessibility Considerations

- **Contrast Ratios:** All combinations exceed WCAG AA standards
- **Focus Indicators:** Clear visual feedback for keyboard navigation
- **Touch Targets:** Minimum 44px height for mobile accessibility
- **Screen Reader Support:** Proper semantic markup and ARIA labels

This design ensures that the category tags provide excellent usability and visual clarity while maintaining the existing Notely design language and brand identity.