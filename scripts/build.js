import fs from 'fs-extra';
import { execa } from 'execa';
import path from 'path';
import { glob } from 'glob';

const log = (message) => console.log(`\x1b[36m${message}\x1b[0m`);
const logError = (message) => console.error(`\x1b[31m${message}\x1b[0m`);

const buildTargets = {
  extension: 'extension',
  web: 'web',
};

async function main() {
  const args = process.argv.slice(2);
  const buildTarget = args[0] || buildTargets.extension;

  try {
    log(`Starting build for: ${buildTarget}...`);

    if (buildTarget === buildTargets.extension) {
      await buildExtension();
    } else if (buildTarget === buildTargets.web) {
      await buildWeb();
    } else {
      throw new Error(`Unknown build target: ${buildTarget}`);
    }

    log('✅ Build completed successfully!');
  } catch (error) {
    logError(`❌ Build failed: ${error.message}`);
    if (error.stack) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

async function buildExtension() {
  const distPath = path.resolve('dist');

  // 1. Clean up
  log('Cleaning up old build directory...');
  await fs.remove(distPath);
  await fs.ensureDir(path.join(distPath, 'assets', 'content'));

  // 2. Build content scripts with esbuild
  log('Building content scripts...');
  const contentScripts = [
    { in: 'src/content/twitter-content.ts', out: 'dist/twitter-content.js' },
    { in: 'src/content/platforms/LinkedInContentScript.ts', out: 'dist/linkedin-content.js' },
    { in: 'src/content/instagram-content.ts', out: 'dist/instagram-content.js' },
    { in: 'src/content/reddit-content.ts', out: 'dist/reddit-content.js' },
    { in: 'src/content/pinterest-content.ts', out: 'dist/pinterest-content.js' },
    { in: 'src/content/web-content.ts', out: 'dist/assets/content/web.js' },
  ];

  await Promise.all(
    contentScripts.map(script => 
      execa('esbuild', [script.in, '--bundle', '--format=iife', `--outfile=${script.out}`, '--target=esnext', '--sourcemap', '--allow-overwrite'])
    )
  );

  // 3. Build background script with esbuild
  log('Building background script...');
  const apiKey = process.env.VITE_OPENAI_API_KEY || '';
  await execa('esbuild', [
    'src/background/background.ts',
    '--bundle',
    '--format=iife',
    '--outfile=dist/background.js',
    '--target=esnext',
    '--sourcemap',
    '--allow-overwrite',
    `--define:import.meta.env.VITE_OPENAI_API_KEY="${apiKey}"`,
    `--define:process.env.OPENAI_API_KEY="${apiKey}"`,
  ]);

  // 4. Copy static assets
  log('Copying static assets...');
  const publicPath = path.resolve('public');
  await fs.copy(path.join(publicPath, 'icons'), path.join(distPath, 'icons'));
  await fs.copy(path.join(publicPath, 'extension-icons'), path.join(distPath, 'extension-icons'));
  await fs.copy(path.join(publicPath, 'popup.html'), path.join(distPath, 'popup.html'));
  await fs.copy(path.join(publicPath, 'assets', 'popup.js'), path.join(distPath, 'assets', 'popup.js'));

  // 5. Build the rest with Vite (popup, dashboard, etc.)
  log('Building with Vite...');
  await execa('vite', ['build']);

  // 6. Force copy the correct manifest.json
  log('Ensuring correct manifest.json...');
  await fs.copy(path.join(publicPath, 'manifest.json'), path.join(distPath, 'manifest.json'));

  // 7. Fix relative paths for extension compatibility
  await fixHtmlPaths(distPath);

  // 8. Validate manifest.json
  await validateManifest(path.join(distPath, 'manifest.json'));
}

async function buildWeb() {
  log('Building web dashboard with Vite...');
  await execa('vite', ['build', '--config', 'vite.config.web.ts']);
  
  // Copy serve.json for SPA routing
  log('Copying serve.json for client-side routing...');
  const servePath = path.resolve('serve.json');
  const distWebPath = path.resolve('dist-web');
  if (await fs.pathExists(servePath)) {
    await fs.copy(servePath, path.join(distWebPath, 'serve.json'));
    log('serve.json copied successfully!');
  } else {
    log('Creating serve.json for client-side routing...');
    const serveConfig = {
      "rewrites": [
        { "source": "/privacy", "destination": "/index.html" },
        { "source": "/terms", "destination": "/index.html" },
        { "source": "/dashboard/*", "destination": "/index.html" },
        { "source": "/login", "destination": "/index.html" },
        { "source": "/register", "destination": "/index.html" },
        { "source": "/settings", "destination": "/index.html" },
        { "source": "/**", "destination": "/index.html" }
      ]
    };
    await fs.writeJSON(path.join(distWebPath, 'serve.json'), serveConfig, { spaces: 2 });
    log('serve.json created successfully!');
  }
}

async function fixHtmlPaths(buildDir) {
  log('Fixing relative paths in HTML files...');
  const htmlFiles = await glob('**/*.html', { cwd: buildDir });

  for (const file of htmlFiles) {
    const filePath = path.join(buildDir, file);
    let content = await fs.readFile(filePath, 'utf-8');
    
    content = content.replace(/src="\/assets\//g, 'src="./assets/');
    content = content.replace(/href="\/assets\//g, 'href="./assets/');
    // A more generic replacement for any root-relative hrefs
    content = content.replace(/href="\/(?!\/)/g, 'href="./');

    await fs.writeFile(filePath, content, 'utf-8');
  }
  log('Paths fixed successfully!');
}

async function validateManifest(manifestPath) {
  log('Validating manifest.json...');
  if (!(await fs.pathExists(manifestPath))) {
    throw new Error('manifest.json was not created!');
  }

  try {
    const content = await fs.readFile(manifestPath, 'utf-8');
    JSON.parse(content);
  } catch (e) {
    const content = await fs.readFile(manifestPath, 'utf-8');
    logError('manifest.json is not valid JSON!');
    console.error('File content:\n', content);
    throw new Error('Invalid manifest.json syntax.');
  }
  log('Manifest validation passed!');
}

main();
